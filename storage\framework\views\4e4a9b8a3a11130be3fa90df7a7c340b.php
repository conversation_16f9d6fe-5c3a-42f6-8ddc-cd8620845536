<?php $__env->startSection('title', 'Notification | E-Mail | SMTP'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Notification | E-Mail | SMTP</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.smtp.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       placeholder="smtp.gmail.com" value="<?php echo e(siteValue('smtp_host')); ?>">
                                <label for="smtp_host">SMTP Host</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       placeholder="587" value="<?php echo e(siteValue('smtp_port')); ?>">
                                <label for="smtp_port">SMTP Port</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="smtp_user" name="smtp_user"
                                       placeholder="<EMAIL>" value="<?php echo e(siteValue('smtp_user')); ?>">
                                <label for="smtp_user">SMTP Username</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="smtp_pass" name="smtp_pass"
                                       placeholder="••••••••" value="<?php echo e(siteValue('smtp_pass')); ?>">
                                <label for="smtp_pass">SMTP Password</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_name" name="smtp_name"
                                       placeholder="Website Name" value="<?php echo e(siteValue('smtp_name')); ?>">
                                <label for="smtp_name">From Name</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Lưu cấu hình
                                </button>
                                <!-- Chức năng gửi email thử nghiệm đã được tắt để tránh spam -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Email Templates Section -->
    <div class="col-md-12 mt-4">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Nội dung thông báo Mail</h5>
                <p class="text-muted mb-0">Để mặc định nếu bạn không có nhu cầu tùy chỉnh. Xóa toàn bộ nội dung trong ô Subject nếu không muốn bật thông báo</p>
            </div>
            <div class="card-body">
                <!-- Login Notification Template -->
                <div class="mb-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-sign-in-alt me-2"></i>Thông báo đăng nhập</h6>
                    <form action="<?php echo e(route('admin.email-template.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="type" value="login_notification">

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="subject"
                                           placeholder="Subject" value="<?php echo e($loginTemplate->subject ?? ''); ?>">
                                    <label>Subject</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">Nội dung Email</label>
                                <textarea class="form-control" name="content" rows="10"
                                          placeholder="Nội dung HTML email"><?php echo e($loginTemplate->content ?? ''); ?></textarea>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Lưu template đăng nhập
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="mt-2">
                        <small class="text-muted">
                            <strong>Biến có thể sử dụng:</strong>
                            {domain} => Link Website, {title} => Tên website, {username} => Tên khách hàng,
                            {ip} => Địa chỉ IP, {device} => Thiết bị, {time} => Thời gian
                        </small>
                    </div>
                </div>

                <hr>

                <!-- Password Reset Template -->
                <div class="mb-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-key me-2"></i>Khôi phục mật khẩu</h6>
                    <form action="<?php echo e(route('admin.email-template.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="type" value="password_reset">

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="subject"
                                           placeholder="Subject" value="<?php echo e($passwordTemplate->subject ?? ''); ?>">
                                    <label>Subject</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">Nội dung Email</label>
                                <textarea class="form-control" name="content" rows="10"
                                          placeholder="Nội dung HTML email"><?php echo e($passwordTemplate->content ?? ''); ?></textarea>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Lưu template khôi phục mật khẩu
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="mt-2">
                        <small class="text-muted">
                            <strong>Biến có thể sử dụng:</strong>
                            {domain} => Link Website, {title} => Tên website, {username} => Tên khách hàng,
                            {link} => Link xác minh, {ip} => Địa chỉ IP, {device} => Thiết bị, {time} => Thời gian
                        </small>
                    </div>
                </div>

                <hr>

                <!-- Product Delivery Template -->
                <div class="mb-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-box me-2"></i>Gửi sản phẩm (API tạp hóa và kho)</h6>
                    <form action="<?php echo e(route('admin.email-template.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="type" value="product_delivery">

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" name="subject"
                                           placeholder="Subject" value="<?php echo e($productTemplate->subject ?? ''); ?>">
                                    <label>Subject</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">Nội dung Email</label>
                                <textarea class="form-control" name="content" rows="10"
                                          placeholder="Nội dung HTML email"><?php echo e($productTemplate->content ?? ''); ?></textarea>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Lưu template gửi sản phẩm
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="mt-2">
                        <small class="text-muted">
                            <strong>Biến có thể sử dụng:</strong>
                            {domain} => Link Website, {title} => Tên website, {username} => Tên khách hàng,
                            {product_name} => Tên sản phẩm, {product_data} => Dữ liệu sản phẩm,
                            {order_id} => Mã đơn hàng, {time} => Thời gian
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- JavaScript cho chức năng test email đã được xóa để tránh spam -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/smtp.blade.php ENDPATH**/ ?>