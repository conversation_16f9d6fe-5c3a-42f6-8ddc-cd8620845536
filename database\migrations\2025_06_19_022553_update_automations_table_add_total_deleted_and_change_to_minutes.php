<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('automations', function (Blueprint $table) {
            // Thêm cột total_deleted nếu chưa có
            if (!Schema::hasColumn('automations', 'total_deleted')) {
                $table->integer('total_deleted')->default(0)->comment('Tổng số bản ghi đã xóa')->after('completed_days');
            }

            // Đổi tên cột execution_days thành execution_minutes nếu cần
            if (Schema::hasColumn('automations', 'execution_days') && !Schema::hasColumn('automations', 'execution_minutes')) {
                $table->renameColumn('execution_days', 'execution_minutes');
            }
        });

        // Cập nhật dữ liệu: chuyển đổi từ ngày sang phút (1 ngày = 1440 phút)
        if (Schema::hasColumn('automations', 'execution_minutes')) {
            DB::statement('UPDATE automations SET execution_minutes = execution_minutes * 1440 WHERE execution_minutes < 1440');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('automations', function (Blueprint $table) {
            // Xóa cột total_deleted
            if (Schema::hasColumn('automations', 'total_deleted')) {
                $table->dropColumn('total_deleted');
            }

            // Đổi tên cột execution_minutes về execution_days
            if (Schema::hasColumn('automations', 'execution_minutes') && !Schema::hasColumn('automations', 'execution_days')) {
                $table->renameColumn('execution_minutes', 'execution_days');
            }
        });

        // Chuyển đổi dữ liệu từ phút về ngày
        if (Schema::hasColumn('automations', 'execution_days')) {
            DB::statement('UPDATE automations SET execution_days = ROUND(execution_days / 1440) WHERE execution_days >= 1440');
        }
    }
};
