@extends('admin.layouts.app')
@section('title', 'Thêm mới danh mục')
@section('content')
    <div class="card custom-card shadow">
        <div class="card-header">
            <h4 class="card-title">Thêm danh mục</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.products.categories.update', ['id' => $category->id]) }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group mb-3">
                    <label for="name" class="form-label">Tên danh mục</label>
                    <input type="text" name="name" id="name" class="form-control" placeholder="Nhập tên danh mục"
                        value="{{ $category->name }}" />
                </div>
                <div class="form-group mb-3">
                    <label for="description" class="form-label"><PERSON><PERSON> chú</label>
                    <textarea name="description" id="description" class="form-control" placeholder="Nhập ghi chú">{!! $category->description !!}</textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="image" class="form-label">Hình ảnh</label>
                    <input type="file" name="image" id="image" class="form-control" />
                    <img src="{{ $category->image }}" alt="{{ $category->name }}" class="img-fluid mt-3" style="max-width: 250px;" />
                </div>
                <div class="form-group mb-3">
                    <label for="slug" class="form-label">Đường dẫn</label>
                    <input type="text" name="slug" id="slug" class="form-control" placeholder="Nhập đường dẫn"
                        value="{{ $category->slug }}" />
                </div>
                <div class="form-group mb-3">
                    <label for="order" class="form-label">Thứ tự hiển thị</label>
                    <input type="number" name="order" id="order" class="form-control"
                        placeholder="Nhập thứ tự hiển thị" value="{{ $category->order }}" />
                </div>
                <div class="form-group mb-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select name="status" id="status" class="form-control">
                        <option value="active" {{ $category->status == 'active' ? 'selected' : '' }}>Hiển thị</option>
                        <option value="inactive" {{ $category->status == 'inactive' ? 'selected' : '' }}>Ẩn</option>
                    </select>
                </div>
                <h3 class="fs-4 mb-3">Meta SEO</h3>
                <div class="form-group mb-3">
                    <label for="meta_title" class="form-label">Tiêu đề</label>
                    <input type="text" name="meta_title" id="meta_title" class="form-control" placeholder="Nhập tiêu đề"
                        value="{{ $category->meta_title }}" />
                </div>
                <div class="form-group mb-3">
                    <label for="meta_description" class="form-label">Nội dung</label>
                    <textarea name="meta_description" id="meta_description" class="form-control" placeholder="Nhập nội dung">{{ $category->meta_description }}</textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="meta_keywords" class="form-label">Từ khoá</label>
                    <input type="text" name="meta_keywords" id="meta_keywords" class="form-control"
                        placeholder="Nhập từ khoá" value="{{ $category->meta_keywords }}" />
                </div>
                <button type="submit" class="btn btn-primary">Cập nhật</button>
            </form>
        </div>
    </div>
@endsection
@section('script')
    <script>
        CKEDITOR.replace('description');
    </script>
@endsection
