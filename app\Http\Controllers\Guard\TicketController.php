<?php

namespace App\Http\Controllers\Guard;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TicketController extends Controller
{
    public function viewTicket()
    {
        $search = request()->search;
        $category = request()->category;
        $status = request()->status;

        $ticket = Ticket::where('username', Auth::user()->id)
            ->where('domain', getDomain())
            ->search($search)
            ->byCategory($category)
            ->byStatus($status)
            ->with(['order', 'user'])
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('guard.ticket', compact('ticket'));
    }


    public function viewEditTicket()
    {
        $ticket = Ticket::where('domain', getDomain())->get();

        return view('guard.ticket.edit', compact('ticket'));
    }
    public function createTicket(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'category' => 'required|in:don_hang,thanh_toan,dich_vu,webcon,khac',
        ], [
            'title.required' => 'Tiêu đề là bắt buộc',
            'body.required' => 'Nội dung là bắt buộc',
            'category.required' => 'Mục hỗ trợ là bắt buộc',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $ticket = new Ticket();
        $ticket->username = Auth::user()->id;
        $ticket->title = $request->title;
        $ticket->body = $request->body;
        $ticket->level = 'Trung Bình'; // Mặc định
        $ticket->category = $request->category;
        $ticket->order_id = null; // Không sử dụng order_id
        $ticket->status = 'pending';
        $ticket->reply = '';
        $ticket->domain = request()->getHost();
        $ticket->save();

        if ($ticket) {
            return redirect()->back()->with('success', 'Tạo ticket thành công!');
        }

        return redirect()->back()->with('error', 'Có lỗi xảy ra, vui lòng thử lại!');
    }
}
