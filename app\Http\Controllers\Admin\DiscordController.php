<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Library\DiscordSdk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DiscordController extends Controller
{
    public function viewDiscordConfig()
    {
        return view('admin.discord.config');
    }

    public function testWebhook()
    {
        try {
            if (site('discord_webhook_url')) {
                $discord = new DiscordSdk();
                $result = $discord->botNotify()->sendMessage([
                    'text' => '**Test webhook thành công!** <PERSON><PERSON><PERSON> là tin nhắn test từ hệ thống.',
                ]);
                
                if ($result['ok']) {
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Test webhook thành công',
                    ]);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => $result['error'] ?? 'Có lỗi x<PERSON>y ra khi gửi webhook',
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bạn chưa cấu hình webhook URL',
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Discord test webhook error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function testWebhookProduct()
    {
        try {
            if (site('discord_webhook_product_url')) {
                $discord = new DiscordSdk();
                $result = $discord->botProduct()->sendMessage([
                    'text' => '**Test webhook đơn hàng sản phẩm thành công!** Đây là tin nhắn test từ hệ thống.',
                ]);

                if ($result['ok']) {
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Test webhook đơn hàng sản phẩm thành công',
                    ]);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => $result['error'] ?? 'Có lỗi xảy ra khi gửi webhook',
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bạn chưa cấu hình webhook URL đơn hàng sản phẩm',
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Discord test webhook product error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }
}


