@extends('admin.layouts.app')
@section('title', 'Quản lí danh mục')
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Danh sách danh mục</h4>
                    <a href="{{ route('admin.products.categories.create') }}" class="btn btn-primary">Thêm mới</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table w-100" id="table-categories">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Thao tác</th>
                                    <th>Tên danh mục</th>
                                    <th>Đường dẫn</th>
                                    <th>Thứ tự</th>
                                    <th>Trạng thái</th>
                                    <th>Th<PERSON><PERSON> gian</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')

    <script>
        $(document).ready(function() {
            loadDatatable('#table-categories', 'categories', [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <a href="/admin/products/categories/edit/${row.id}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-danger" onclick="deleteData(${row.id}, 'categories')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                    }
                },
                {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'slug',
                    name: 'slug'
                },
                {
                    data: 'order',
                    name: 'order'
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        if (data == 'active') {
                            return `<span class="badge bg-success">Hiển thị</span>`;
                        }
                        return `<span class="badge bg-danger">Ẩn</span>`;
                    }
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY');
                    }
                }
            ])
        });
    </script>
@endsection
