@extends('guard.layouts.app') 
@section('title', 'Sản phẩm')
@section('style')
    <style>
        .card-hover-top {
            transition: 0.4s;
        }

        .card-hover-top:hover {
            transform: translateY(-8px);
            transition: 0.4s;
        }
    </style>
@endsection
@section('content')
    <div class="row">
        @foreach ($products as $productMain)
        <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-3 col-xxl-2">
                <a href="{{ route('client.product', ['slug' => $productMain->slug]) }}">
                    <div class="card shadow card-hover-top overflow-hidden">
                        <img src="{{ asset($productMain->image) }}" class="card-img-top" alt="{{ $productMain->name }}">
                        <div class="card-body text-center p-2">
                            <h5 class="card-title fs-14 mb-2 twoline">
                                                                {{ $productMain->name }}
                                                            </h5>
                            <strong class="text-primary fs-12">
                                @php
                                    $activeProducts = $productMain->product->where('domain', request()->getHost())->where('status', 'active');
                                @endphp
                                <span class="text-danger">
                                    @if ($activeProducts->count() > 1)
                                        <span class="text-danger">{{ number_format($productMain->getPriceStart()) }} đ</span> -
                                        <span class="text-danger">{{ number_format($productMain->getPriceEnd() ?? 0) }} đ</span>
                                    @elseif ($activeProducts->count() == 1)
                                        <span class="text-danger">{{ number_format($productMain->getPriceStart() ?? 0) }} đ</span>
                                    @else
                                        <span class="text-muted">Liên hệ</span>
                                    @endif
                                </span>
                            </strong>
                        </div>
                    </div>
                </a>
            </div>
            
             
        @endforeach
    </div>
@endsection
