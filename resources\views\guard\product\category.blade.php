@extends('guard.layouts.app') 
@section('title', 'Sản phẩm - ' . $category->name)
@section('style')
    <style>
        .card-hover-top {
            transition: 0.4s;
        }

        .card-hover-top:hover {
            transform: translateY(-8px);
            transition: 0.4s;
        }
    </style>
@endsection
@section('content')
    <div class="row">
        @foreach ($productsMain as $productMain)
            <div class="row">
                <div class="col-md-6 col-lg-3">
                    <a href="{{ route('client.product', ['cate_slug' => $category->slug, 'slug' => $productMain->slug]) }}">
                        <div class="card shadow card-hover-top">
                            <img src="{{ asset($productMain->image) }}" class="card-img-top" alt="{{ $productMain->name }}">
                            <div class="card-body">
                                <h5 class="card-title text-center mb-3">{{ $productMain->name }}</h5>
                                <strong class="text-primary">Giá: @if ($productMain->product->count() > 1)
                                        <span class="text-danger">{{ number_format($productMain->getPriceStart()) }} đ</span> - <span
                                            class="text-danger">{{ number_format($productMain->getPriceEnd() ?? 0) }} đ</span>
                                    @else
                                        <span class="text-danger">{{ number_format($productMain->getPriceStart() ?? 0) }} đ</span>
                                    @endif
                                </strong>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        @endforeach
    </div>
@endsection
