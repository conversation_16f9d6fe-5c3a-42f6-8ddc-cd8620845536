
<?php $__env->startSection('title', 'Sản phẩm đã mua'); ?>
<?php $__env->startSection('style'); ?>
<style>
    .purchased-products-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
    }

    .purchased-products-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107);
        border-radius: 15px 15px 0 0;
    }

    .purchased-products-section h4 {
        color: #2c3e50;
        font-weight: 700;
        border-bottom: 3px solid #007bff;
        padding-bottom: 15px;
        margin-bottom: 25px;
        position: relative;
    }

    .purchased-products-section h4::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 60px;
        height: 3px;
        background: #28a745;
        border-radius: 2px;
    }

    .table-container {
        background: #ffffff;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        border: 1px solid #e9ecef;
    }

    .table thead th {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        padding: 15px 10px;
        font-size: 0.9rem;
    }

    .table thead th:first-child {
        border-radius: 8px 0 0 0;
    }

    .table thead th:last-child {
        border-radius: 0 8px 0 0;
    }

    .table tbody td {
        vertical-align: middle;
        padding: 12px 10px;
        border-bottom: 1px solid #f1f3f4;
        font-size: 0.9rem;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }

    .badge {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 20px;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 6px;
        font-weight: 500;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
        box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.4);
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        box-shadow: 0 2px 8px rgba(220,53,69,0.3);
    }

    .btn-danger:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220,53,69,0.4);
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 10px 0;
    }

    .dataTables_wrapper .dataTables_filter input {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        margin-left: 8px;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .swal-wide {
        max-width: 90% !important;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
        box-shadow: 0 2px 8px rgba(23,162,184,0.3);
    }

    .btn-info:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(23,162,184,0.4);
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="purchased-products-section">
        <h4 class="mb-4">
            <i class="fas fa-shopping-bag me-2"></i>Sản Phẩm Đã Mua
        </h4>
        <div class="table-container">
            <div class="dt-responsive table-responsive">
                <table class="table table-hover" id="table-products">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Mã Đơn</th>
                            <th>Ngày Mua</th>
                            <th>Sản Phẩm</th>
                            <th>Số Lượng</th>
                            <th>Tổng Tiền</th>
                            <th>Dữ Liệu</th>
                            <th>Trạng Thái</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        $(document).ready(function() {
            loadTable('#table-products', 'products', [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: 'order_id',
                    name: 'order_id'
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data, type, row) {
                        return moment(data).format('DD/MM/YYYY HH:mm:ss');
                    }
                },
                {
                    data: 'product',
                    name: 'product',
                    render: function(data, type, row) {
                        return data.name;
                    }
                },
                {
                    data: 'quantity',
                    name: 'quantity'
                },
                {
                    data: 'payment',
                    name: 'payment',
                    render: function(data, type, row) {
                        return `<span class="badge bg-success">${formatCurrency(data)}</span>`;
                    }
                },
                {
                    data: 'data',
                    name: 'data',
                    render: function(data, type, row) {
                        if (data && data.trim() !== '') {
                            const truncatedData = data.length > 50 ? data.substring(0, 50) + '...' : data;
                            return `<div class="text-truncate" style="max-width: 200px;" title="${data}">
                                        <small class="text-success"><i class="fas fa-check-circle"></i> Có dữ liệu</small>
                                        <br><small class="text-muted">${truncatedData}</small>
                                    </div>`;
                        } else {
                            return '<span class="text-muted small"><i class="fas fa-times-circle"></i> Không có dữ liệu</span>';
                        }
                    }
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data, type, row) {
                        if(data == 'success') {
                            return `<span class="badge bg-success">Thành công</span>`;
                        } else if(data == 'pending') {
                            return `<span class="badge bg-warning">Đang chờ</span>`;
                        }   else if(data == 'Refunded') {
                            return `<span class="badge bg-danger">Hoàn tiền toàn bộ</span>`;
                        }
                        else if(data == 'Partial_Refunded') {
                            return `<span class="badge bg-warning">Hoàn tiền một phần</span>`;
                        }else {
                            return `<span class="badge bg-danger">Thất bại</span>`;
                        }
                    }
                },
                {
                    data: null,
                    name: 'actions',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        let actions = '';
                        if(row.status == 'success') {
                            if(row.data && row.data.trim() !== '') {
                                const productName = row.product.name.replace(/'/g, "\\'").replace(/"/g, '\\"');
                                actions += `<div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-primary" onclick="downloadProduct(${row.id}, '${productName}')" title="Tải xuống dữ liệu">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info" onclick="viewProductData(${row.id})" title="Xem dữ liệu">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>`;
                            } else {
                                actions += `<span class="badge bg-secondary"><i class="fas fa-times-circle"></i> Không có dữ liệu</span>`;
                            }
                        } else {
                            actions += `<span class="badge bg-warning">Chưa thành công</span>`;
                        }
                        return actions;
                    }
                }
            ]);
        });

        // Hàm download sản phẩm
        function downloadProduct(id, productName) {
            Swal.fire({
                title: 'Xác nhận download',
                text: `Bạn có muốn tải xuống dữ liệu sản phẩm "${productName}" dưới dạng file txt không?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Có, tải xuống!',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Tạo form ẩn để download
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/product/download/' + id;

                    // Thêm CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '<?php echo e(csrf_token()); ?>';
                    form.appendChild(csrfToken);

                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                }
            });
        }

        // Hàm xem dữ liệu sản phẩm
        function viewProductData(id) {
            // Lấy dữ liệu từ DataTable
            const table = $('#table-products').DataTable();
            const rowData = table.rows().data().toArray().find(row => row.id == id);

            if (rowData && rowData.data && rowData.data.trim() !== '') {
                Swal.fire({
                    title: 'Dữ liệu sản phẩm: ' + rowData.product.name,
                    html: `<div style="text-align: left; max-height: 400px; overflow-y: auto;">
                        <pre style="white-space: pre-wrap; word-wrap: break-word; background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">${rowData.data}</pre>
                    </div>`,
                    width: '80%',
                    showCloseButton: true,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'swal-wide'
                    }
                });
            } else {
                Swal.fire('Thông báo', 'Không có dữ liệu để hiển thị', 'info');
            }
        }


    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/guard/product/bought.blade.php ENDPATH**/ ?>