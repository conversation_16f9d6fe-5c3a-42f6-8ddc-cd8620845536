<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Cập nhật bảng users
        if (Schema::hasTable('users')) {
            // Kiểm tra và thêm cột discord_id nếu chưa có
            if (!Schema::hasColumn('users', 'discord_id')) {
                if (Schema::hasColumn('users', 'telegram_id')) {
                    // Đ<PERSON>i tên cột telegram_id thành discord_id
                    DB::statement('ALTER TABLE users CHANGE telegram_id discord_id VARCHAR(255) NULL COMMENT "ID discord của người dùng"');
                } else {
                    Schema::table('users', function (Blueprint $table) {
                        $table->string('discord_id')->nullable()->comment('ID discord của người dùng');
                    });
                }
            }
            
            // Ki<PERSON>m tra và thêm cột notification_discord nếu chưa có
            if (!Schema::hasColumn('users', 'notification_discord')) {
                if (Schema::hasColumn('users', 'notification_telegram')) {
                    // Đổi tên cột notification_telegram thành notification_discord
                    DB::statement("ALTER TABLE users CHANGE notification_telegram notification_discord ENUM('yes', 'no') NOT NULL DEFAULT 'yes' COMMENT 'Cho phép gửi thông báo qua discord'");
                } else {
                    Schema::table('users', function (Blueprint $table) {
                        $table->enum('notification_discord', ['yes', 'no'])->default('yes')->comment('Cho phép gửi thông báo qua discord');
                    });
                }
            }
            
            // Kiểm tra và thêm cột discord_link nếu chưa có
            if (!Schema::hasColumn('users', 'discord_link')) {
                if (Schema::hasColumn('users', 'telegram_link')) {
                    // Đổi tên cột telegram_link thành discord_link
                    DB::statement('ALTER TABLE users CHANGE telegram_link discord_link VARCHAR(255) NULL COMMENT "Đường dẫn tới discord của người dùng"');
                } else {
                    Schema::table('users', function (Blueprint $table) {
                        $table->string('discord_link')->nullable()->comment('Đường dẫn tới discord của người dùng');
                    });
                }
            }
        }

        // Cập nhật bảng config_sites - thêm các cột Discord nếu chưa có
        if (Schema::hasTable('config_sites')) {
            Schema::table('config_sites', function (Blueprint $table) {
                if (!Schema::hasColumn('config_sites', 'discord_webhook_url')) {
                    $table->string('discord_webhook_url')->nullable();
                }
                if (!Schema::hasColumn('config_sites', 'discord_webhook_chat_url')) {
                    $table->string('discord_webhook_chat_url')->nullable();
                }
                if (!Schema::hasColumn('config_sites', 'discord_webhook_dontay_url')) {
                    $table->string('discord_webhook_dontay_url')->nullable();
                }
                if (!Schema::hasColumn('config_sites', 'discord_webhook_box_url')) {
                    $table->string('discord_webhook_box_url')->nullable();
                }
                if (!Schema::hasColumn('config_sites', 'discord_webhook_withdraw_url')) {
                    $table->string('discord_webhook_withdraw_url')->nullable();
                }
                if (!Schema::hasColumn('config_sites', 'discord_server_invite')) {
                    $table->string('discord_server_invite')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rollback changes
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'discord_id')) {
                    $table->dropColumn('discord_id');
                }
                if (Schema::hasColumn('users', 'notification_discord')) {
                    $table->dropColumn('notification_discord');
                }
                if (Schema::hasColumn('users', 'discord_link')) {
                    $table->dropColumn('discord_link');
                }
            });
        }

        if (Schema::hasTable('config_sites')) {
            Schema::table('config_sites', function (Blueprint $table) {
                $table->dropColumn([
                    'discord_webhook_url',
                    'discord_webhook_chat_url',
                    'discord_webhook_dontay_url',
                    'discord_webhook_box_url',
                    'discord_webhook_withdraw_url',
                    'discord_server_invite'
                ]);
            });
        }
    }
};