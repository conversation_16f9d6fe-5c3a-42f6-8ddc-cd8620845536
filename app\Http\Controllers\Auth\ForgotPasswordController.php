<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class ForgotPasswordController extends Controller
{

    /**
     * Display the form to request a password reset link.
     */
    public function showLinkRequestForm()
    {
        return view('auth.passwords.email');
    }

    /**
     * Send a reset link to the given user.
     */
    public function sendResetLinkEmail(Request $request)
    {
        $this->validateEmail($request);

        // Kiểm tra user có tồn tại không
        $user = User::where('email', $request->email)
            ->where('domain', request()->getHost())
            ->first();

        if (!$user) {
            return back()->withErrors(['email' => __('passwords.user')]);
        }

        // Kiểm tra cấu hình <PERSON>
        if (!$this->isSmtpConfigured()) {
            return back()->withErrors(['email' => '<PERSON><PERSON><PERSON> hình email chưa được thiết lập. Vui lòng liên hệ admin.']);
        }

        // Tạo token reset password
        $token = Str::random(64);
        
        // Lưu token vào database
        \DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $request->email],
            [
                'email' => $request->email,
                'token' => $token,
                'created_at' => now()
            ]
        );

        // Gửi email với template tùy chỉnh
        $this->sendCustomResetEmail($user, $token);

        return back()->with('status', __('passwords.sent'));
    }

    /**
     * Gửi email reset password với template tùy chỉnh
     */
    protected function sendCustomResetEmail($user, $token)
    {
        $domain = request()->getHost();
        $template = EmailTemplate::getOrCreateDefault($domain, EmailTemplate::TYPE_PASSWORD_RESET);

        if (!$template || !$template->isEnabled()) {
            return; // Không gửi email nếu template bị tắt
        }

        // Tạo link reset
        $resetLink = url(route('password.reset', ['token' => $token, 'email' => $user->email], false));

        // Dữ liệu để thay thế placeholder
        $data = [
            '{domain}' => $domain,
            '{title}' => siteValue('name_site') ?? $domain,
            '{username}' => $user->name,
            '{link}' => $resetLink,
            '{ip}' => request()->ip(),
            '{device}' => $this->getDeviceInfo(),
            '{time}' => now()->format('d/m/Y H:i:s')
        ];

        // Thay thế placeholders
        $emailContent = $template->replacePlaceholders($data);

        // Gửi email
        Mail::send([], [], function ($message) use ($user, $emailContent) {
            $message->to($user->email)
                ->subject($emailContent['subject'])
                ->html($emailContent['content'])
                ->from(siteValue('smtp_user'), siteValue('smtp_name'));
        });
    }

    /**
     * Kiểm tra cấu hình SMTP
     */
    protected function isSmtpConfigured()
    {
        return siteValue('smtp_host') && 
               siteValue('smtp_port') && 
               siteValue('smtp_user') && 
               siteValue('smtp_pass') && 
               siteValue('smtp_name');
    }

    /**
     * Lấy thông tin thiết bị
     */
    protected function getDeviceInfo()
    {
        $userAgent = request()->header('User-Agent');
        
        if (strpos($userAgent, 'Mobile') !== false) {
            return 'Mobile Device';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            return 'Tablet';
        } else {
            return 'Desktop Computer';
        }
    }

    /**
     * Validate the email for the given request.
     */
    protected function validateEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
    }
}
