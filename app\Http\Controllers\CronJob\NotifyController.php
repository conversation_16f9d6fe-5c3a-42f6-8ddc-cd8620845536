<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\Recharge;
use App\Models\Order;
use App\Models\Withdraw;
use App\Library\DiscordSdk;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotifyController extends Controller
{
    public function ticketRevenueNotify(Request $request)
    {
        try {
            // Lấy thông tin ticket mới
            $newTickets = Ticket::whereDate('created_at', Carbon::today())->count();
            $pendingTickets = Ticket::where('status', 'pending')->count();
            
            // Lấy thông tin rút hoa hồng
            $todayWithdraws = Withdraw::whereDate('created_at', Carbon::today())->sum('amount');
            $pendingWithdraws = Withdraw::where('status', 'pending')->count();
            
            // L<PERSON>y doanh thu hôm nay theo cách tính của dashboard
            $todayRevenue = Order::whereDate('created_at', Carbon::today())->sum('total_profit');
            $todayRecharges = Recharge::whereDate('created_at', Carbon::today())
                ->where('status', 'Success')
                ->sum('amount');
            
            // Gửi thông báo đến Discord
            $webhook_url = site('discord_webhook_ticket_revenue_url');
            if (!$webhook_url) {
                return "Chưa cấu hình Discord webhook URL cho ticket/revenue!";
            }
            
            $discord = new DiscordSdk();
            
            $message = "  Báo Cáo Ngày " . Carbon::now()->format('d/m/Y') . " \n\n" .
                "  Hỗ Trợ\n" .
                "- Hỗ Trợ Mới Hôm Nay : " . $newTickets . "\n" .
                "- Hỗ Trợ Đang Chờ Xử Lý : " . $pendingTickets . "\n\n" .
                "  Rút Hoa Hồng\n" .
                "- Tổng Rút Hôm Nay : " . number_format($todayWithdraws) . " VNĐ\n" .
                "- Yêu Cầu Chờ Duyệt : " . $pendingWithdraws . "\n\n" .
                " Doanh Thu\n" .
                "- Doanh Thu Đơn Hàng Hôm Nay : " . number_format($todayRevenue) . " VNĐ\n" .
                "- Nạp Tiền Hôm Nay : " . number_format($todayRecharges) . " VNĐ\n" .
                "- Tổng Doanh Thu Hôm Nay: " . number_format($todayRevenue + $todayRecharges) . " VNĐ\n\n" .
                " Thời Gian Gửi : " . Carbon::now()->format('H:i:s d/m/Y') . "\n" .
                " Domain Của Bot : " . $request->getHost();
            
            $result = $discord->sendWebhookMessage($webhook_url, [
                'text' => $message
            ]);
            
            if ($result['ok']) {
                return "Đã gửi thông báo thành công!";
            } else {
                return "Lỗi gửi Discord: " . ($result['error'] ?? 'Không xác định');
            }
        } catch (\Exception $e) {
            return "Lỗi: " . $e->getMessage();
        }
    }
}