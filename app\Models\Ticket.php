<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;

    protected $table = 'tickets';

    protected $fillable = [
        'title',
        'body',
        'level',
        'category',
        'order_id',
        'status',
        'username',
        'reply',
        'domain',
    ];

    // Định nghĩa các danh mục hỗ trợ
    const CATEGORIES = [
        'don_hang' => 'Đơn hàng',
        'thanh_toan' => 'Thanh toán',
        'dich_vu' => 'Dịch vụ',
        'webcon' => 'Webcon',
        'khac' => 'Khác'
    ];

    // Định nghĩa các trạng thái
    const STATUSES = [
        'pending' => 'Chờ xử lý',
        'processing' => 'Đang xử lý',
        'success' => 'Đã xử lý',
        'cancelled' => 'Đã hủy'
    ];

    // Đ<PERSON>nh nghĩa các mức độ ưu tiên
    const LEVELS = [
        'Thấp' => 'Thấp',
        'Trung Bình' => 'Trung Bình',
        'Cao' => 'Cao',
        'Khẩn Cấp' => 'Khẩn Cấp'
    ];

    /**
     * Relationship với User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'username');
    }

    /**
     * Relationship với Order (chỉ khi category = 'don_hang')
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Lấy tên danh mục
     */
    public function getCategoryNameAttribute()
    {
        return self::CATEGORIES[$this->category] ?? 'Khác';
    }

    /**
     * Lấy tên trạng thái
     */
    public function getStatusNameAttribute()
    {
        return self::STATUSES[$this->status] ?? 'Chờ xử lý';
    }

    /**
     * Scope để lọc theo danh mục
     */
    public function scopeByCategory($query, $category)
    {
        if ($category && $category !== 'all') {
            return $query->where('category', $category);
        }
        return $query;
    }

    /**
     * Scope để lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        if ($status && $status !== 'all') {
            return $query->where('status', $status);
        }
        return $query;
    }

    /**
     * Scope để tìm kiếm
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function($q) use ($search) {
                $q->where('title', 'like', '%' . $search . '%')
                  ->orWhere('body', 'like', '%' . $search . '%')
                  ->orWhere('id', 'like', '%' . $search . '%');
            });
        }
        return $query;
    }
}
