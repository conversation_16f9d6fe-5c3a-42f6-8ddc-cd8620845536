@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON><PERSON>ơ<PERSON>')
@section('content') 
<div class="row">
    <div class="col-md-12">
        <div class="card card-body">
            <form action="" method="GET">
                <div class="row">
                    <!-- Cột 1: <PERSON><PERSON><PERSON>, loại, nhà cung cấp -->
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <select data-choices data-choices-groups name="per_page" class="form-select" onchange="this.form.submit()">
                                @foreach([10, 20, 30, 50, 100, 1000, 10000, 'all'] as $num)
                                    <option value="{{ $num }}" {{ request('per_page') == $num ? 'selected' : '' }}>
                                        {{ $num == 'all' ? 'Tất cả' : $num }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group mb-2">
                           <select data-choices data-choices-groups name="order_type" class="form-select" onchange="this.form.submit()">
                           @if (getDomain() == env("APP_MAIN_SITE"))
                           <option value="normal" {{ request('order_type') == 'normal' ? 'selected' : '' }}>- Tất Cả -</option>
                             <option value="dontay" {{ request('order_type') == 'dontay' ? 'selected' : '' }}>- Đơn Tay -</option>
                             <option value="refund" {{ request('order_type') == 'refund' ? 'selected' : '' }}>- Chờ Hoàn Tiền -</option>
                            @else
                              <option value="refund" {{ request('order_type') == 'refund' ? 'selected' : '' }}>- Chờ Hoàn Tiền -</option>
                            @endif
                           </select>

                        </div>
                        <div class="form-group mb-2">
                            <select data-choices data-choices-groups name="orderProviderName" class="form-select" onchange="this.form.submit()">
                                <option value="">- Nhà Cung Cấp Dịch Vụ -</option>
                                @foreach ($providers as $provider)
                                    <option value="{{ $provider }}" {{ request('orderProviderName') == $provider ? 'selected' : '' }}>
                                        {{ $provider }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fad fa-search me-2"></i> Tìm Kiếm 
                        </button>
                    </div>

                  <!-- Cột 2: Tìm Kiếm Nội Dung -->
                    <div class="col-md-4">
                        <div class="form-group mb-2">
                            <textarea class="form-control" rows="5" name="search" placeholder="Nhập Nội Dung Tìm Kiếm, Mỗi Hàng 1 Dòng">{{ request('search') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <a href="{{ url()->current() }}" class="btn btn-outline-secondary status-btn btn-sm rounded-2 mb-2" data-status="">
                                Tất Cả ({{ array_sum($countStatusOrder) }})
                            </a>
                            @foreach ($statuses as $status)
                                @if(isset($countStatusOrder[$status])) 
                                    <button type="button" 
                                        class="btn btn-outline-{{ 
                                            in_array($status, ['Completed', 'Success']) ? 'success' : 
                                            (in_array($status, ['Cancelled', 'Failed', 'Expired']) ? 'danger' : 
                                            (in_array($status, ['Pending', 'PendingRefundCancel', 'PendingRefundPartial', 'WaitingForRefund']) ? 'warning' : 
                                            (in_array($status, ['Running', 'Processing', 'Active']) ? 'primary' : 
                                            (in_array($status, ['Partially Completed', 'Partially Refunded', 'Partial']) ? 'info' : 'secondary'))))
                                        }} status-btn btn-sm rounded-2 mb-2" 
                                        data-status="{{ $status }}">
                                        {{ [
                                            'Running' => 'Đang Chạy',
                                            'Processing' => 'Đang Xử Lý',
                                            'Holding' => 'Tạm Dừng',
                                            'Completed' => 'Hoàn Thành',
                                            'Cancelled' => 'Đã Hủy',
                                            'Refunded' => 'Đã Hoàn Tiền',
                                            'Failed' => 'Thất Bại',
                                            'Pending' => 'Chờ Xử Lý',
                                            'PendingRefundCancel' => 'Chờ Xử Lý Hoàn Tiền',
                                            'PendingRefundPartial' => 'Chờ Hoàn Tiền Một Phần',
                                            'Partially Refunded' => 'Hoàn Tiền Một Phần',
                                            'Partial' => 'Hoàn Thành Một Phần',
                                            'Partially Completed' => 'Hoàn Thành Một Phần',
                                            'WaitingForRefund' => 'Chờ Hoàn Tiền',
                                            'Expired' => 'Đã Hết Hạn',
                                            'Success' => 'Thành Công',
                                            'Active' => 'Đang Hoạt Động',
                                        ][$status] ?? $status }} 
                                        ({{ $countStatusOrder[$status] }})
                                    </button>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">LỊCH SỬ ĐẶT HÀNG</h5>
            </div>
            <div class="card-body">
                <form action="" method="GET">
                    
                </form>
                <div class="table-responsive mb-3">
                      <table class="table text-nowrap table-striped table-hover">
                         <thead>
                            <tr>
                                <th>Thông Tin</th>
                                <th>Thao Tác</th>
                                <th>Lợi Nhuận</th>
                                <th>Dịch Vụ</th>
                                <th>Dữ Liệu Đơn Hàng</th>
                                <th>Thời Gian</th>
                                <th>Nhà Cung Cấp</th>
                            </tr>
                        </thead>
                        <tbody class="f-w-600">
                            @if ($orders->isEmpty()) 
                                @include('table-search-not-found') 
                            @endif 
                            @foreach ($orders as $order) 
                            <tr>
                                <td>
                                    <ul class="list-unstyled">
                                        <li>ID: {{ $order->id }} - Mã Đơn: <span onclick="coppy('{{ $order->order_code }}')">{{ $order->order_code }}</span></li>
                                        <li>Username: [{{ optional($order->user)->id ?? 'Null' }}] {{ optional($order->user)->username ?? 'User Null!' }}</li>
                                        <li>Trạng Thái: {!! statusOrder($order->status, true) !!}</li>
                                    </ul>
                                </td>
                                <td>
                                    <div class="row  ">
                                        @if($order->status === "Pending_Balance")
                                        <div class="col-6 mb-2">
                                            <a href="{{ route('admin.order.send', ['id' => $order->id]) }}" onclick="return confirm('Bạn Có Chắc Chắn Muốn Gửi Lại Đơn Hàng Này Không?')" class="btn btn-sm btn-primary rounded-2" data-bs-toggle="tooltip" title="Gửi lại đơn">
                                                <i class="fas fa-paper-plane"></i>
                                            </a>
                                             
                                        </div>
                                        @endif
                                        <div class="col-6 mb-2">
                                            <button type="button" class="btn btn-sm btn-info rounded-2" data-bs-toggle="modal" data-bs-target="#editOrder-{{ $order->id }}">
                                                <i class="fas fa-pencil" data-bs-toggle="tooltip" title="Chi Tiết"></i>
                                            </button>
                                        </div>
                                        
                                        <div class="col-6 mb-2">
                                            <a href="{{ route('admin.order.delete', ['id' => $order->id]) }}" onclick="return confirm('Bạn Có Chắc Chắn Muốn Xoá Đơn Hàng Này Không?')" class="btn btn-sm btn-danger rounded-2" data-bs-toggle="tooltip" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <div class="row">
                                        @if($order->status == 'PendingRefundCancel' || $order->status == 'PendingRefundPartial')
                                        <div class="col-6 mb-2">
                                            <a href="javascript:;" class="btn btn-sm btn-warning rounded-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Hoàn Tiền" onclick="refundOrders('{{ $order->order_code }}')">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                        </div>
                                        @endif
                                        
                                    </div>
                                </td>
                                <td>
                                    <ul class="list-unstyled">
                                        <li>Rate: {{ $order->price }}đ</li>
                                        <li>Thành Tiền: {{ number_format($order->payment) }}đ</li>
                                        <li>Lãi Đơn Hàng: {{ number_format($order->total_profit) }}đ</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul class="list-unstyled">
                                        <li><img src="{{ $order->server->service->platform->image ?? 'https://cdn-icons-png.flaticon.com/128/17003/17003579.png' }}" class="img-fluid rounded-3 me-1" style="width:24px;height:24px"><b>Nền Tảng: </b>{{ $order->server->service->platform->name ?? 'Null'}}</li>
                                        <li><img src="{{ $order->server->service->image ?? 'https://cdn-icons-png.flaticon.com/128/17003/17003579.png' }}" class="img-fluid rounded-3 me-1" style="width:24px;height:24px"><b>Dịch Vụ: </b>{{ $order->server->service->name ?? 'Null'}}</li>
                                        <li><b>Máy Chủ: </b>[{{$order->server->id ?? 'Null'}}] - {{ $order->server->name ?? 'Null'}}</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul class="list-unstyled mb-0">
                                        <li>Object ID: <input type="text" onclick="coppy('{{ $order->object_id }}')" value="{{ $order->object_id }}" class="form-control form-control-sm d-inline-block" style="width: 200px;" readonly></li>
                                        @if ($order->cb_loop == 'yes')
                                        <li><b class="text-danger">Vòng Lặp Còn Lại: </b>{{ $order->quantity_loop }} - Delay: {{$order->time_loop }} Phút/ Lần</li>
                                        @endif
                                    </ul>
                                    <ul class="list-unstyled mb-0">
                                    @if (!empty($order->orderdata()['quantity']))
                                        <li><b>Số Lượng: </b>{{ number_format(json_decode($order->order_data, true)['quantity']) }} - Đã Tăng: {{ number_format($order->buff) }}</li>
                                    @endif

                                    @if (!empty($order->orderdata()['reaction']))
                                        <li><b>Cảm Xúc: </b>{{ $order->orderdata()['reaction'] }}</li>
                                    @endif

                                    @if (!empty($order->orderdata()['comments']))
                                        <li>
                                            <textarea class="form-control note" rows="2" readonly>{{ $order->orderdata()['comments'] }}</textarea>
                                        </li>
                                    @endif
                                   @if (!empty($order->orderdata()['minute']))
                                   <li><b>Thời Lượng: </b>{{ $order->orderdata()['minute'] }} Phút</li>
                                    @endif
                                @if (!empty($order->orderdata()['duration']))
                                    <li><b>Thời Gian: </b>{{ $order->orderdata()['duration'] }} Ngày</li>
                                    <li><b>Còn Lại: </b>{{ remainingDays($order->time, $order->remaining, true) }}</li>
                                @endif
                                </ul>
                            </td>
                            <td>
                                <ul  class="list-unstyled">
                                    <li><b>Ngày Tạo: </b>{{ $order->created_at }}</li>
                                    <li><b>Ngày Mua: </b>{{ $order->time_order ?? $order->time }}</li>
                                    @if($order->status !== 'Completed' || $order->status !== 'Refunded')
                                        <li><b>Cập Nhật: </b>{{ timeago($order->last_update) }}</li>
                                    @endif
                                    @if($order->status == 'Completed')
                                    <li><b class="text-success">Ngày Hoàn Thành: </b>{{ $order->completed_at }}</li>
                                    @endif
                                </ul>
                            </td>
                            <td>
                                <ul  class="list-unstyled">
                                    <li><b>Mã Đối Tác: </b>{{ $order->order_id }}</li>
                                    <li><b>NCC: </b>{{ $order->provider->name_partner ?? $order->orderProviderName }}</li>
                                </ul>
                            </td>
                        </tr>
                        <!-- Modal for Order Editing -->
                        <div class="modal fade" id="editOrder-{{ $order->id }}" tabindex="-1" aria-labelledby="editOrderLabel-{{ $order->id }}" data-bs-backdrop="static" data-bs-keyboard="false" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="editOrderLabel-{{ $order->id }}">Chỉnh Sửa Đơn Hàng #{{ $order->order_code }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <form action="{{ route('admin.order.action', ['id' => $order->id]) }}" method="POST">
                                        @csrf
                                        <input name="action" value="edit" hidden>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12 mb-2">
                                                    <div class="alert alert-warning" role="alert">
                                                        <label class="form-label">Thông Tin Đối Tác</label>
                                                        <ul class="list-unstyled">
                                                            <li>Mã Đối Tác: {{ $order->order_id }}</li>
                                                            <li>Đối Tác: {{ $order->orderProviderName }}</li>
                                                            <li>Máy Chủ Đối Tác: <span class="badge bg-primary">{{$order->orderProviderServer }}</span></li>
                                                            <li>Đường Dẫn Máy Chủ Đối Tác: <span class="badge bg-primary">{{$order->orderProviderPath }}</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Trạng Thái:</label>
                                                    <div class="form-group mb-2">
                                                        <select id="status" name="status" class="form-control form-select">
                                                            <option value="Processing" {{ $order->status === 'Processing' ? 'selected' : '' }}> Đang Xử Lý</option>
                                                            <option value="Completed" {{ $order->status === 'Completed' ? 'selected' : '' }}> Hoàn Thành</option>
                                                            <option value="Cancelled" {{ $order->status === 'Cancelled' ? 'selected' : '' }}> Đã Hủy</option>
                                                            <option value="Refunded" {{ $order->status === 'Refunded' ? 'selected' : '' }}> Đã Hoàn Tiền</option>
                                                            <option value="Failed" {{ $order->status === 'Failed' ? 'selected' : '' }}> Thất Bại</option>
                                                            <option value="Pending" {{ $order->status === 'Pending' ? 'selected' : '' }}> Chờ Xử Lý</option>
                                                            <option value="Partially Refunded" {{ $order->status === 'Partially Refunded' ? 'selected' : '' }}> Hoàn Tiền Một Phần</option>
                                                            <option value="Partially Completed" {{ $order->status === 'Partially Completed' ? 'selected' : '' }}> Hoàn Thành Một Phần</option>
                                                            <option value="WaitingForRefund" {{ $order->status === 'WaitingForRefund' ? 'selected' : '' }}> Chờ Hoàn Tiền (Chỉ Chỉnh Trạng Thái)</option>
                                                            <option value="PendingRefundPartial" {{ $order->status === 'PendingRefundPartial' ? 'selected' : '' }}> Chờ Hoàn Tiền (Tự Động Hoàn Tiền)</option>
                                                            <option value="Expired" {{ $order->status === 'Expired' ? 'selected' : '' }}> Hết Hạn</option>
                                                            <option value="Success" {{ $order->status === 'Success' ? 'selected' : '' }}> Thành Công</option>
                                                            <option value="Active" {{ $order->status === 'Active' ? 'selected' : '' }}> Đang Hoạt Động</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="col-md-3">
                                                    <label class="form-label">Bắt Đầu: </label>
                                                    <input class="form-control" name="start" value="{{$order->start}}">
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Đã Tăng: </label>
                                                    <input class="form-control" name="buff" value="{{$order->buff}}">
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Bình Luận</label>
                                                        <textarea class="form-control" rows="5">{{ json_decode($order->order_data, true)['comments'] }}</textarea>
                                                    </div>
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Ghi Chú</label>
                                                        <textarea class="form-control" rows="3" name="note">{{ $order->note }}</textarea>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-sm btn-light" data-bs-dismiss="modal">Đóng</button>
                                            <button type="submit" class="btn btn-sm btn-primary"><i class="fad fa-save me-1"></i>Cập Nhật</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @endforeach 
                    </tbody>
                </table>
                <div class="d-flex justify-content-end mt-3">
                    @if(method_exists($orders, 'links'))
                        {{ $orders->appends(request()->all())->links('pagination::bootstrap-4') }}
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
</div>
@endsection 
@section('script') 
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const buttons = document.querySelectorAll('.status-btn');
        const urlParams = new URLSearchParams(window.location.search);
        const currentStatus = urlParams.get('status') || ''; 
    buttons.forEach(button => {
        const buttonStatus = button.getAttribute('data-status') || '';
        if (buttonStatus === currentStatus) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        button.addEventListener('click', function() {
            if (buttonStatus) {
                urlParams.set('status', buttonStatus);
            } else {
                urlParams.delete('status');
            }
            window.location.href = window.location.pathname + '?' + urlParams.toString();
        });
    });
});
</script>
<script>
    function refundOrders(order_code) {
      Swal.fire({
        title: 'Xác Nhận Hoàn Tiền ?',
        text: "Bạn Chắc Chắn Muốn Hoàn Tiền Cho Đơn Hàng Này ?",
        icon: 'warning',
        showCloseButton: true,
        showCancelButton: true,
        confirmButtonText: "Hoàn Tiền",
        cancelButtonColor: "rgb(224, 56, 56)",
        cancelButtonText: "Hủy"
      }).then(result => {
        if (result.isConfirmed) {
          $.ajax({
            url: '/admin/order/refund',
            method: 'POST',
            data: {
              order_code: order_code,
              _token: $('meta[name="csrf-token"]').attr('content') // CSRF Token
            },
            dataType: 'json',
            beforeSend: function(xhr) {
              xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content')); // Add CSRF Token to header
              Swal.fire({
                title: 'Đang Xử Lý...',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                },
              });
            },
            success: function(response) {
              if (response.status === 'success') {
                Swal.fire({
                  icon: 'success',
                  title: "Thông Báo",
                  text: response.message,
                  confirmButtonText: "Đồng Ý !",
                }).then(() => {
                  window.location.reload();
                });
              } else {
                Swal.fire({
                  icon: 'error',
                  title: "Thông Báo",
                  text: response.message,
                  confirmButtonText: "Đồng Ý !",
                });
              }
            },
            error: function(xhr) {
              Swal.fire({
                icon: 'error',
                title: "Thông Báo",
                text: xhr.responseJSON.message,
                confirmButtonText: "Đồng Ý !",
              });
            }
          })
        }
      })
    }
</script> 
@endsection