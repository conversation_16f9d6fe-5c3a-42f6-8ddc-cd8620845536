
<?php $__env->startSection('title', 'Thông Tin Cá Nhân'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
<?php if(Auth::user()->discord_id == null && Auth::user()->discord_id == ''): ?>
    <div class="col-md-12">
        <div class="card bg-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 me-3">
                        <h3 class="text-white">Bạn chưa xác thực Discord</h3>
                        <p class="text-white text-opacity-75 text-opa mb-0">Vui lòng xác thực Discord để nhận thông báo từ hệ thống!</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="/app/images/application/img-accout-alert.png" alt="img" class="img-fluid wid-80">
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php endif; ?>
<div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <ul class="nav bordered-tab d-inline-flex nav-pills gap-2 mb-0" id="pills-tab-six" role="tablist">
                        <li class="nav-item" role="presentation">
                            
    <button class="nav-link  active" id="user-set-profile-tab" data-bs-toggle="pill" data-bs-target="#user-set-profile" type="button" role="tab" aria-controls="user-set-profile" aria-selected="true">
        <span class="f-w-500">Thông tin tài khoản</span>
    </button>
</li>
<li class="nav-item" role="presentation">
    <button class="nav-link " id="user-set-account-tab" data-bs-toggle="pill" data-bs-target="#user-set-account" type="button" role="tab" aria-controls="user-set-account" aria-selected="false" tabindex="-1">
        <span class="f-w-500" >Cấu hình Discord</span>
        
    </button>
</li>
<li class="nav-item" role="presentation">
    <button class="nav-link " id="user-set-passwort-tab" data-bs-toggle="pill" data-bs-target="#user-set-passwort" type="button" role="tab" aria-controls="user-set-passwort" aria-selected="false" tabindex="-1">
        <span class="f-w-500"><i class="ph-duotone ph-key m-r-10"></i>Mật khẩu & Bảo mật</span>
    </button>
</li>
<li class="nav-item" role="presentation">
    <button class="nav-link " id="user-set-information-tab" data-bs-toggle="pill" data-bs-target="#user-set-information" type="button" role="tab" aria-controls="user-set-information" aria-selected="true">
        <span class="f-w-500"><i class="ph-duotone ph-clipboard-text m-r-10"></i>Lịch sử hoạt động</span>
    </button>
</li>

                    </ul>
                </div>
            </div>
        </div>
        
      
    <div class="col-lg-12 col-xxl-12">
        <div class="tab-content" id="user-set-tabContent">
            <div class="tab-pane fade active show" id="user-set-profile" role="tabpanel" aria-labelledby="user-set-profile-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Thông tin cá nhân</h5>
                    </div>
                    <div class="card-body">
                        <form action="">
                            <div class="row">
                                <div class="col-md-6 form-group mb-3">
                                    <label for="name" class="form-label">Họ và tên:</label>
                                    <input type="text" class="form-control" id="name" disabled
                                        value="<?php echo e(Auth::user()->name); ?>">
                                </div>
                                <div class="col-md-6 form-group mb-3">
                                    <label for="email" class="form-label">Địa chỉ Email:</label>
                                    <input type="text" class="form-control" id="email" disabled
                                        value="<?php echo e(Auth::user()->email); ?>">
                                </div>
                                <div class="col-md-6 form-group mb-3">
                                    <label for="username" class="form-label">Tài khoản:</label>
                                    <input type="text" class="form-control" id="username" disabled
                                        value="<?php echo e(Auth::user()->username); ?>">
                                </div>
                                <div class="col-md-6 form-group mb-3">
                                    <label for="created_at" class="form-label">Thời gian đăng kí:</label>
                                    <input type="text" class="form-control" id="created_at" disabled
                                        value="<?php echo e(Auth::user()->created_at); ?>">
                                </div>
                                <div class="col-md-6 form-group mb-3">
                                    <label for="balance" class="form-label">Số dư:</label>
                                    <input type="text" class="form-control" id="balance" disabled
                                        value="<?php echo e(number_format(Auth::user()->balance)); ?>">
                                </div>
                                <div class="col-md-6 form-group mb-3">
                                    <label for="last_login" class="form-label">Đăng nhập gần đây:</label>
                                    <input type="text" class="form-control" id="last_login" disabled
                                        value="<?php echo e(Auth::user()->last_login); ?>">
                                </div>
                                <div class="col-md-12 form-group mb-3">
                                    <label for="api_token" class="form-label">Api Token</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="api_token" readonly
                                            onclick="coppy('<?php echo e(Auth::user()->api_token ?? 'null'); ?>')"
                                            value="<?php echo e(Auth::user()->api_token ?? 'Bạn chưa tạo Api Token!'); ?>"
                                            placeholder="Bạn cần ấn thay đổi Token">
                                        <button class="btn btn-primary" type="button" id="btn-reload-token">
                                        <i class="ti ti-refresh"></i>
                                        Thay đổi
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="user-set-account" role="tabpanel" aria-labelledby="user-set-account-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Cấu hình Discord</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 border-bottom py-2">
                            <h4 class="text-primary fw-bold fs-4 mb-3">Liên kết Discord</h4>
                            <div class="alert alert-primary">
                                <h4 class="alert-heading">Cấu hình Discord</h4>
                                <ul>
                                    <li>Để bảo mật tài khoản của bạn, bạn có thể liên kết tài khoản của mình với
                                        Discord. Khi liên kết, bạn sẽ nhận được thông báo qua Discord khi có hoạt
                                        động đăng nhập từ thiết bị không xác định.
                                    </li>
                                    <li>Nên Cấu Hình Để Sử Dụng Nhằm Bảo Vệ Tài Khoản Và Cập Nhật Lịch Sử Đơn Hàng Nhanh
                                        Chóng Tránh Bị Bug
                                    </li>
                                    <li>Gửi Lịch Sử Mua Hàng & Nạp Tiền Về Discord Của Bạn </li>
                                </ul>
                            </div>
                            <?php if(Auth::user()->discord_id !== null && Auth::user()->discord_id !== ''): ?>
                            <h6 class="text-muted fw-bold fs-6 mb-3">Trạng thái: <span
                                class="badge bg-success badge-sm">Đã liên kết</span></h6>
                            <form action="<?php echo e(route('account.update.status-discord')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="mb-3 form-group">
                                    <label class="form-label">ID Discord</label>
                                    <input type="text" class="form-control" id="discord_id"
                                        value="<?php echo e(Auth::user()->discord_id); ?>" disabled>
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label">Thông báo về Discord</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="status" name="status" value="<?php echo e(Auth::user()->notification_discord == 'yes' ? 'no' : 'yes'); ?>"
                                        <?php echo e(Auth::user()->notification_discord == 'yes' ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="status">
                                        Gửi thông báo
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary shadow-2 btn-sm text-sm">Cập nhật</button>
                                </div>
                            </form>
                            <?php else: ?>
                            <h6 class="text-muted fw-bold fs-6 mb-3">Trạng thái: <span
                                class="badge bg-danger badge-sm">Chưa liên kết</span></h6>
                            <button data-pc-animate="slide-in-right" type="button" class="btn btn-primary"
                                data-bs-toggle="modal" data-bs-target="#discord">
                            Liên kết Discord
                            </button>
                            <div class="modal fade modal-animate" id="discord" tabindex="-1"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Liên Kết Discord</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"> </button>
                                        </div>
                                        <form action="<?php echo e(route('account.update.status-discord')); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <div class="modal-body">
                                                <div class="mb-3 text-center">
                                                    <h4 class="text-gray">Thông tin Bot Discord</h4>
                                                    <p class="text-muted">Để liên kết tài khoản của bạn với
                                                        Discord,
                                                        bạn cần thực hiện các bước sau:
                                                    </p>
                                                    <ol>
                                                        <li>Nhắn tin riêng với Bot Discord</li>
                                                        <li>Gửi lệnh <strong>/active <?php echo e(Auth::user()->api_token); ?></strong> để liên kết
                                                            tài khoản
                                                        </li>
                                                    </ol>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-outline-secondary"
                                                    data-bs-dismiss="modal">Đóng</button>
                                                <button type="submit" class="btn btn-primary shadow-2">Liên
                                                Kết</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="user-set-passwort" role="tabpanel" aria-labelledby="user-set-passwort-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Đổi mật khẩu</h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('account.change-password')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-12 form-group mb-3">
                                    <label for="current_password" class="form-label">Mật khẩu hiện
                                    tại:</label>
                                    <input type="password" class="form-control" id="current_password"
                                        name="current_password">
                                </div>
                                <div class="col-md-12 form-group mb-3">
                                    <label for="new_password" class="form-label">Mật khẩu mới:</label>
                                    <input type="password" class="form-control" id="new_password"
                                        name="new_password">
                                </div>
                                <div class="col-md-12 form-group mb-3">
                                    <label for="confirm_password" class="form-label">Xác nhận mật
                                    khẩu:</label>
                                    <input type="password" class="form-control" id="confirm_password"
                                        name="confirm_password">
                                </div>
                                <div class="col-md-12 form-group mb-3">
                                    <button type="submit" class="btn btn-primary col-12">
                                    <i class="ti ti-lock"></i>
                                    Thay đổi mật khẩu
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h5>Xác thực 2 yếu tố</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 border-bottom py-2">
                            <h4 class="text-primary fw-bold fs-4 mb-3">Xác thực 2 yếu tố</h4>
                            <div class="alert alert-primary">
                                <h4 class="alert-heading">Xác thực 2 yếu tố là gì?</h4>
                                <p class="mb-0">Xác thực 2 yếu tố (2FA) là một phương pháp bảo mật mạnh mẽ hơn
                                    so với mật khẩu đơn lẻ. Khi bật xác thực 2 yếu tố, bạn sẽ cần nhập một mã xác
                                    thực được tạo ra từ ứng dụng xác thực trên điện thoại di động của bạn sau
                                    khi nhập mật khẩu của bạn. Điều này giúp bảo vệ tài khoản của bạn khỏi các
                                    cuộc tấn công xâm nhập và truy cập trái phép.
                                </p>
                            </div>
                            
                            <?php if(Auth::user()->two_factor_auth === 'yes'): ?>
                            <h6 class="text-muted fw-bold fs-6 mb-3">Trạng thái: <span
                                class="badge bg-success badge-sm">Đã bật</span></h6>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                data-bs-target="#two_factor_auth">
                            Tắt xác thực
                            </button>
                            <div class="modal fade modal-animate" id="two_factor_auth" tabindex="-1"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Tắt xác thực 2 yếu tố</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"> </button>
                                        </div>
                                        <form action="<?php echo e(route('account.two-factor-auth-disable')); ?>"
                                            method="POST">
                                            <?php echo csrf_field(); ?>
                                            <div class="modal-body">
                                                <div class="mb-3 text-center">
                                                    <h4 class="text-gray"> Nhập mã xác thực để tắt xác thực 2
                                                        yếu tố
                                                    </h4>
                                                </div>
                                                <div class="mb-3 form-group">
                                                    <label class="form-label">Nhập mã xác thực</label>
                                                    <input type="text" class="form-control" id="code"
                                                        autocomplete="off" name="code">
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-outline-secondary"
                                                    data-bs-dismiss="modal">Đóng</button>
                                                <button type="submit" class="btn btn-primary shadow-2">Tắt
                                                xác thực</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <h6 class="text-muted fw-bold fs-6 mb-3">Trạng thái: <span
                                class="badge bg-danger badge-sm">Chưa bật</span></h6>
                            <button data-pc-animate="slide-in-right" type="button" class="btn btn-primary"
                                data-bs-toggle="modal" data-bs-target="#two_factor_auth">
                            Xác thực
                            </button>
                            <div class="modal fade modal-animate" id="two_factor_auth" tabindex="-1"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Xác thực 2 yếu tố</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"> </button>
                                        </div>
                                        <form action="<?php echo e(route('account.two-factor-auth')); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <div class="modal-body">
                                                <div class="mb-3 text-center">
                                                    <h4 class="text-gray">Quét mã QR bằng ứng dụng xác thực</h4>
                                                    <img src="<?php echo e($qrCodeUrl ?? ''); ?>" alt="QR Google Authenticate">
                                                </div>
                                                <div class="mb-3 text-center">
                                                    <h4 class="text-gray">Hoặc nhập mã bí mật</h4>
                                                    <p class="text-muted">Nhập mã bí mật vào ứng dụng xác thực nếu
                                                        không thể quét mã QR
                                                    </p>
                                                    <input type="text" class="form-control" id="secret"
                                                        value="<?php echo e($secret ?? ''); ?>" disabled>
                                                    <button type="button" class="btn btn-primary mt-3"
                                                        id="copy-secret">
                                                    <i class="ti ti-clipboard"></i>
                                                    Sao chép mã bí mật
                                                    </button>
                                                </div>
                                                <div class="mb-3 form-group">
                                                    <label class="form-label">Nhập mã xác thực</label>
                                                    <input type="text" class="form-control" id="code"
                                                        autocomplete="off" name="code">
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-outline-secondary"
                                                    data-bs-dismiss="modal">Đóng</button>
                                                <button type="submit" class="btn btn-primary shadow-2">Bật xác
                                                thực</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="user-set-information" role="tabpanel" aria-labelledby="user-set-information-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Lịch sử hoạt động</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered nowrap dataTable"
                                style="max-width: 1444px;">
                                <thead>
                                    <tr>
                                        <th>Thời gian</th>
                                        <th>Hoạt động</th>
                                        <th>IP</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = \App\Models\UserActivity::where('user_id', Auth::user()->id)->where('activity', 'auth')->orderBy('id', 'DESC')->limit(10)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($activity->created_at); ?></td>
                                        <td><?php echo e($activity->note); ?></td>
                                        <td><?php echo e($activity->ip); ?></td>
                                        <td><?php echo e($activity->user_agent); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script>
    $(document).ready(function() {
        $('#copy-secret').click(function() {
            var copyText = document.getElementById("secret");
            copyText.select();
            copyText.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(copyText.value);
            document.execCommand("copy");
            swal("Đã sao chép mã bí mật!", "success");
        });


        $('#btn-reload-token').click(function() {
            $.ajax({
                url: "<?php echo e(route('account.reload-user-token')); ?>",
                type: 'GET',
                dataType: 'json',
                beforeSend: function() {
                $('#btn-reload-token').html('<i class="fa fa-spinner fa-spin"></i> Đang xử lý..').prop(
                    'disabled', true);
                    },
                    complete: function() {
                        $('#btn-reload-token').html('<i class="fa fa-sync"></i> Thay đổi').prop('disabled', false);
                    },
                success: function(data) {
                    $('#api_token').val(data.api_token);
                    swal("Đã thay đổi Api Token!", "success");
                },
                error: function() {
                    swal("Có lỗi xảy ra!", "error");
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/guard/profile/index.blade.php ENDPATH**/ ?>