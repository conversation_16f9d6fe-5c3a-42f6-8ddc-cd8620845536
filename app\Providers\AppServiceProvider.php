<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // <PERSON><PERSON><PERSON> <PERSON><PERSON>TP động từ database
        if (function_exists('siteValue')) {
            config([
                'mail.default'                => 'smtp',
                'mail.mailers.smtp.host'     => siteValue('smtp_host'),
                'mail.mailers.smtp.port'     => siteValue('smtp_port'),
                'mail.mailers.smtp.username' => siteValue('smtp_user'),
                'mail.mailers.smtp.password' => siteValue('smtp_pass'),
                'mail.mailers.smtp.encryption' => 'tls',
                'mail.from.address'            => siteValue('smtp_user'),
                'mail.from.name'               => siteValue('smtp_name') ?? strtoupper(getDomain()),
            ]);
        }
    }
}
