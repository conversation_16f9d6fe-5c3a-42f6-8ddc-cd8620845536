<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('automations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên công việc tự động');
            $table->enum('type', [
                'delete_completed_orders',
                'delete_completed_recharges',
                'delete_completed_transactions',
                'delete_inactive_users'
            ])->comment('Loại công việc tự động');
            $table->integer('completed_days')->comment('Số ngày đã hoàn thành');
            $table->integer('execution_minutes')->comment('Số phút thực hiện một lần');
            $table->integer('total_deleted')->default(0)->comment('Tổng số bản ghi đã xóa');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('Trạng thái');
            $table->timestamp('last_run')->nullable()->comment('Lần chạy cuối cùng');
            $table->string('domain')->nullable()->comment('Tên miền');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('automations');
    }
};
