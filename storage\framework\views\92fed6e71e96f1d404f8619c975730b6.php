<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> Sử Đơn H<PERSON>ng'); ?>
<?php $__env->startSection('content'); ?> 
<div class="row">
    <div class="col-md-12">
        <div class="card card-body">
            <form action="" method="GET">
                <div class="row">
                    <!-- Cột 1: <PERSON><PERSON><PERSON> l<PERSON>, loại, nhà cung cấp -->
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <select data-choices data-choices-groups name="per_page" class="form-select" onchange="this.form.submit()">
                                <?php $__currentLoopData = [10, 20, 30, 50, 100, 1000, 10000, 'all']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $num): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($num); ?>" <?php echo e(request('per_page') == $num ? 'selected' : ''); ?>>
                                        <?php echo e($num == 'all' ? 'Tất cả' : $num); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                           <select data-choices data-choices-groups name="order_type" class="form-select" onchange="this.form.submit()">
                           <?php if(getDomain() == env("APP_MAIN_SITE")): ?>
                           <option value="normal" <?php echo e(request('order_type') == 'normal' ? 'selected' : ''); ?>>- Tất Cả -</option>
                             <option value="dontay" <?php echo e(request('order_type') == 'dontay' ? 'selected' : ''); ?>>- Đơn Tay -</option>
                             <option value="refund" <?php echo e(request('order_type') == 'refund' ? 'selected' : ''); ?>>- Chờ Hoàn Tiền -</option>
                            <?php else: ?>
                              <option value="refund" <?php echo e(request('order_type') == 'refund' ? 'selected' : ''); ?>>- Chờ Hoàn Tiền -</option>
                            <?php endif; ?>
                           </select>

                        </div>
                        <div class="form-group mb-2">
                            <select data-choices data-choices-groups name="orderProviderName" class="form-select" onchange="this.form.submit()">
                                <option value="">- Nhà Cung Cấp Dịch Vụ -</option>
                                <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($provider); ?>" <?php echo e(request('orderProviderName') == $provider ? 'selected' : ''); ?>>
                                        <?php echo e($provider); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fad fa-search me-2"></i> Tìm Kiếm 
                        </button>
                    </div>

                  <!-- Cột 2: Tìm Kiếm Nội Dung -->
                    <div class="col-md-4">
                        <div class="form-group mb-2">
                            <textarea class="form-control" rows="5" name="search" placeholder="Nhập Nội Dung Tìm Kiếm, Mỗi Hàng 1 Dòng"><?php echo e(request('search')); ?></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <a href="<?php echo e(url()->current()); ?>" class="btn btn-outline-secondary status-btn btn-sm rounded-2 mb-2" data-status="">
                                Tất Cả (<?php echo e(array_sum($countStatusOrder)); ?>)
                            </a>
                            <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(isset($countStatusOrder[$status])): ?> 
                                    <button type="button" 
                                        class="btn btn-outline-<?php echo e(in_array($status, ['Completed', 'Success']) ? 'success' : 
                                            (in_array($status, ['Cancelled', 'Failed', 'Expired']) ? 'danger' : 
                                            (in_array($status, ['Pending', 'PendingRefundCancel', 'PendingRefundPartial', 'WaitingForRefund']) ? 'warning' : 
                                            (in_array($status, ['Running', 'Processing', 'Active']) ? 'primary' : 
                                            (in_array($status, ['Partially Completed', 'Partially Refunded', 'Partial']) ? 'info' : 'secondary'))))); ?> status-btn btn-sm rounded-2 mb-2" 
                                        data-status="<?php echo e($status); ?>">
                                        <?php echo e([
                                            'Running' => 'Đang Chạy',
                                            'Processing' => 'Đang Xử Lý',
                                            'Holding' => 'Tạm Dừng',
                                            'Completed' => 'Hoàn Thành',
                                            'Cancelled' => 'Đã Hủy',
                                            'Refunded' => 'Đã Hoàn Tiền',
                                            'Failed' => 'Thất Bại',
                                            'Pending' => 'Chờ Xử Lý',
                                            'PendingRefundCancel' => 'Chờ Xử Lý Hoàn Tiền',
                                            'PendingRefundPartial' => 'Chờ Hoàn Tiền Một Phần',
                                            'Partially Refunded' => 'Hoàn Tiền Một Phần',
                                            'Partial' => 'Hoàn Thành Một Phần',
                                            'Partially Completed' => 'Hoàn Thành Một Phần',
                                            'WaitingForRefund' => 'Chờ Hoàn Tiền',
                                            'Expired' => 'Đã Hết Hạn',
                                            'Success' => 'Thành Công',
                                            'Active' => 'Đang Hoạt Động',
                                        ][$status] ?? $status); ?> 
                                        (<?php echo e($countStatusOrder[$status]); ?>)
                                    </button>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">LỊCH SỬ ĐẶT HÀNG</h5>
            </div>
            <div class="card-body">
                <form action="" method="GET">
                    
                </form>
                <div class="table-responsive mb-3">
                      <table class="table text-nowrap table-striped table-hover">
                         <thead>
                            <tr>
                                <th>Thông Tin</th>
                                <th>Thao Tác</th>
                                <th>Lợi Nhuận</th>
                                <th>Dịch Vụ</th>
                                <th>Dữ Liệu Đơn Hàng</th>
                                <th>Thời Gian</th>
                                <th>Nhà Cung Cấp</th>
                            </tr>
                        </thead>
                        <tbody class="f-w-600">
                            <?php if($orders->isEmpty()): ?> 
                                <?php echo $__env->make('table-search-not-found', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> 
                            <?php endif; ?> 
                            <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                            <tr>
                                <td>
                                    <ul class="list-unstyled">
                                        <li>ID: <?php echo e($order->id); ?> - Mã Đơn: <span onclick="coppy('<?php echo e($order->order_code); ?>')"><?php echo e($order->order_code); ?></span></li>
                                        <li>Username: [<?php echo e(optional($order->user)->id ?? 'Null'); ?>] <?php echo e(optional($order->user)->username ?? 'User Null!'); ?></li>
                                        <li>Trạng Thái: <?php echo statusOrder($order->status, true); ?></li>
                                    </ul>
                                </td>
                                <td>
                                    <div class="row  ">
                                        <?php if($order->status === "Pending_Balance"): ?>
                                        <div class="col-6 mb-2">
                                            <a href="<?php echo e(route('admin.order.send', ['id' => $order->id])); ?>" onclick="return confirm('Bạn Có Chắc Chắn Muốn Gửi Lại Đơn Hàng Này Không?')" class="btn btn-sm btn-primary rounded-2" data-bs-toggle="tooltip" title="Gửi lại đơn">
                                                <i class="fas fa-paper-plane"></i>
                                            </a>
                                             
                                        </div>
                                        <?php endif; ?>
                                        <div class="col-6 mb-2">
                                            <button type="button" class="btn btn-sm btn-info rounded-2" data-bs-toggle="modal" data-bs-target="#editOrder-<?php echo e($order->id); ?>">
                                                <i class="fas fa-pencil" data-bs-toggle="tooltip" title="Chi Tiết"></i>
                                            </button>
                                        </div>
                                        
                                        <div class="col-6 mb-2">
                                            <a href="<?php echo e(route('admin.order.delete', ['id' => $order->id])); ?>" onclick="return confirm('Bạn Có Chắc Chắn Muốn Xoá Đơn Hàng Này Không?')" class="btn btn-sm btn-danger rounded-2" data-bs-toggle="tooltip" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <?php if($order->status == 'PendingRefundCancel' || $order->status == 'PendingRefundPartial'): ?>
                                        <div class="col-6 mb-2">
                                            <a href="javascript:;" class="btn btn-sm btn-warning rounded-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Hoàn Tiền" onclick="refundOrders('<?php echo e($order->order_code); ?>')">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                        
                                    </div>
                                </td>
                                <td>
                                    <ul class="list-unstyled">
                                        <li>Rate: <?php echo e($order->price); ?>đ</li>
                                        <li>Thành Tiền: <?php echo e(number_format($order->payment)); ?>đ</li>
                                        <li>Lãi Đơn Hàng: <?php echo e(number_format($order->total_profit)); ?>đ</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul class="list-unstyled">
                                        <li><img src="<?php echo e($order->server->service->platform->image ?? 'https://cdn-icons-png.flaticon.com/128/17003/17003579.png'); ?>" class="img-fluid rounded-3 me-1" style="width:24px;height:24px"><b>Nền Tảng: </b><?php echo e($order->server->service->platform->name ?? 'Null'); ?></li>
                                        <li><img src="<?php echo e($order->server->service->image ?? 'https://cdn-icons-png.flaticon.com/128/17003/17003579.png'); ?>" class="img-fluid rounded-3 me-1" style="width:24px;height:24px"><b>Dịch Vụ: </b><?php echo e($order->server->service->name ?? 'Null'); ?></li>
                                        <li><b>Máy Chủ: </b>[<?php echo e($order->server->id ?? 'Null'); ?>] - <?php echo e($order->server->name ?? 'Null'); ?></li>
                                    </ul>
                                </td>
                                <td>
                                    <ul class="list-unstyled mb-0">
                                        <li>Object ID: <input type="text" onclick="coppy('<?php echo e($order->object_id); ?>')" value="<?php echo e($order->object_id); ?>" class="form-control form-control-sm d-inline-block" style="width: 200px;" readonly></li>
                                        <?php if($order->cb_loop == 'yes'): ?>
                                        <li><b class="text-danger">Vòng Lặp Còn Lại: </b><?php echo e($order->quantity_loop); ?> - Delay: <?php echo e($order->time_loop); ?> Phút/ Lần</li>
                                        <?php endif; ?>
                                    </ul>
                                    <ul class="list-unstyled mb-0">
                                    <?php if(!empty($order->orderdata()['quantity'])): ?>
                                        <li><b>Số Lượng: </b><?php echo e(number_format(json_decode($order->order_data, true)['quantity'])); ?> - Đã Tăng: <?php echo e(number_format($order->buff)); ?></li>
                                    <?php endif; ?>

                                    <?php if(!empty($order->orderdata()['reaction'])): ?>
                                        <li><b>Cảm Xúc: </b><?php echo e($order->orderdata()['reaction']); ?></li>
                                    <?php endif; ?>

                                    <?php if(!empty($order->orderdata()['comments'])): ?>
                                        <li>
                                            <textarea class="form-control note" rows="2" readonly><?php echo e($order->orderdata()['comments']); ?></textarea>
                                        </li>
                                    <?php endif; ?>
                                   <?php if(!empty($order->orderdata()['minute'])): ?>
                                   <li><b>Thời Lượng: </b><?php echo e($order->orderdata()['minute']); ?> Phút</li>
                                    <?php endif; ?>
                                <?php if(!empty($order->orderdata()['duration'])): ?>
                                    <li><b>Thời Gian: </b><?php echo e($order->orderdata()['duration']); ?> Ngày</li>
                                    <li><b>Còn Lại: </b><?php echo e(remainingDays($order->time, $order->remaining, true)); ?></li>
                                <?php endif; ?>
                                </ul>
                            </td>
                            <td>
                                <ul  class="list-unstyled">
                                    <li><b>Ngày Tạo: </b><?php echo e($order->created_at); ?></li>
                                    <li><b>Ngày Mua: </b><?php echo e($order->time_order ?? $order->time); ?></li>
                                    <?php if($order->status !== 'Completed' || $order->status !== 'Refunded'): ?>
                                        <li><b>Cập Nhật: </b><?php echo e(timeago($order->last_update)); ?></li>
                                    <?php endif; ?>
                                    <?php if($order->status == 'Completed'): ?>
                                    <li><b class="text-success">Ngày Hoàn Thành: </b><?php echo e($order->completed_at); ?></li>
                                    <?php endif; ?>
                                </ul>
                            </td>
                            <td>
                                <ul  class="list-unstyled">
                                    <li><b>Mã Đối Tác: </b><?php echo e($order->order_id); ?></li>
                                    <li><b>NCC: </b><?php echo e($order->provider->name_partner ?? $order->orderProviderName); ?></li>
                                </ul>
                            </td>
                        </tr>
                        <!-- Modal for Order Editing -->
                        <div class="modal fade" id="editOrder-<?php echo e($order->id); ?>" tabindex="-1" aria-labelledby="editOrderLabel-<?php echo e($order->id); ?>" data-bs-backdrop="static" data-bs-keyboard="false" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="editOrderLabel-<?php echo e($order->id); ?>">Chỉnh Sửa Đơn Hàng #<?php echo e($order->order_code); ?></h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <form action="<?php echo e(route('admin.order.action', ['id' => $order->id])); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input name="action" value="edit" hidden>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12 mb-2">
                                                    <div class="alert alert-warning" role="alert">
                                                        <label class="form-label">Thông Tin Đối Tác</label>
                                                        <ul class="list-unstyled">
                                                            <li>Mã Đối Tác: <?php echo e($order->order_id); ?></li>
                                                            <li>Đối Tác: <?php echo e($order->orderProviderName); ?></li>
                                                            <li>Máy Chủ Đối Tác: <span class="badge bg-primary"><?php echo e($order->orderProviderServer); ?></span></li>
                                                            <li>Đường Dẫn Máy Chủ Đối Tác: <span class="badge bg-primary"><?php echo e($order->orderProviderPath); ?></span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Trạng Thái:</label>
                                                    <div class="form-group mb-2">
                                                        <select id="status" name="status" class="form-control form-select">
                                                            <option value="Processing" <?php echo e($order->status === 'Processing' ? 'selected' : ''); ?>> Đang Xử Lý</option>
                                                            <option value="Completed" <?php echo e($order->status === 'Completed' ? 'selected' : ''); ?>> Hoàn Thành</option>
                                                            <option value="Cancelled" <?php echo e($order->status === 'Cancelled' ? 'selected' : ''); ?>> Đã Hủy</option>
                                                            <option value="Refunded" <?php echo e($order->status === 'Refunded' ? 'selected' : ''); ?>> Đã Hoàn Tiền</option>
                                                            <option value="Failed" <?php echo e($order->status === 'Failed' ? 'selected' : ''); ?>> Thất Bại</option>
                                                            <option value="Pending" <?php echo e($order->status === 'Pending' ? 'selected' : ''); ?>> Chờ Xử Lý</option>
                                                            <option value="Partially Refunded" <?php echo e($order->status === 'Partially Refunded' ? 'selected' : ''); ?>> Hoàn Tiền Một Phần</option>
                                                            <option value="Partially Completed" <?php echo e($order->status === 'Partially Completed' ? 'selected' : ''); ?>> Hoàn Thành Một Phần</option>
                                                            <option value="WaitingForRefund" <?php echo e($order->status === 'WaitingForRefund' ? 'selected' : ''); ?>> Chờ Hoàn Tiền (Chỉ Chỉnh Trạng Thái)</option>
                                                            <option value="PendingRefundPartial" <?php echo e($order->status === 'PendingRefundPartial' ? 'selected' : ''); ?>> Chờ Hoàn Tiền (Tự Động Hoàn Tiền)</option>
                                                            <option value="Expired" <?php echo e($order->status === 'Expired' ? 'selected' : ''); ?>> Hết Hạn</option>
                                                            <option value="Success" <?php echo e($order->status === 'Success' ? 'selected' : ''); ?>> Thành Công</option>
                                                            <option value="Active" <?php echo e($order->status === 'Active' ? 'selected' : ''); ?>> Đang Hoạt Động</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="col-md-3">
                                                    <label class="form-label">Bắt Đầu: </label>
                                                    <input class="form-control" name="start" value="<?php echo e($order->start); ?>">
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Đã Tăng: </label>
                                                    <input class="form-control" name="buff" value="<?php echo e($order->buff); ?>">
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Bình Luận</label>
                                                        <textarea class="form-control" rows="5"><?php echo e(json_decode($order->order_data, true)['comments']); ?></textarea>
                                                    </div>
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Ghi Chú</label>
                                                        <textarea class="form-control" rows="3" name="note"><?php echo e($order->note); ?></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-sm btn-light" data-bs-dismiss="modal">Đóng</button>
                                            <button type="submit" class="btn btn-sm btn-primary"><i class="fad fa-save me-1"></i>Cập Nhật</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                    </tbody>
                </table>
                <div class="d-flex justify-content-end mt-3">
                    <?php if(method_exists($orders, 'links')): ?>
                        <?php echo e($orders->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<?php $__env->stopSection(); ?> 
<?php $__env->startSection('script'); ?> 
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const buttons = document.querySelectorAll('.status-btn');
        const urlParams = new URLSearchParams(window.location.search);
        const currentStatus = urlParams.get('status') || ''; 
    buttons.forEach(button => {
        const buttonStatus = button.getAttribute('data-status') || '';
        if (buttonStatus === currentStatus) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        button.addEventListener('click', function() {
            if (buttonStatus) {
                urlParams.set('status', buttonStatus);
            } else {
                urlParams.delete('status');
            }
            window.location.href = window.location.pathname + '?' + urlParams.toString();
        });
    });
});
</script>
<script>
    function refundOrders(order_code) {
      Swal.fire({
        title: 'Xác Nhận Hoàn Tiền ?',
        text: "Bạn Chắc Chắn Muốn Hoàn Tiền Cho Đơn Hàng Này ?",
        icon: 'warning',
        showCloseButton: true,
        showCancelButton: true,
        confirmButtonText: "Hoàn Tiền",
        cancelButtonColor: "rgb(224, 56, 56)",
        cancelButtonText: "Hủy"
      }).then(result => {
        if (result.isConfirmed) {
          $.ajax({
            url: '/admin/order/refund',
            method: 'POST',
            data: {
              order_code: order_code,
              _token: $('meta[name="csrf-token"]').attr('content') // CSRF Token
            },
            dataType: 'json',
            beforeSend: function(xhr) {
              xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content')); // Add CSRF Token to header
              Swal.fire({
                title: 'Đang Xử Lý...',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                  Swal.showLoading();
                },
              });
            },
            success: function(response) {
              if (response.status === 'success') {
                Swal.fire({
                  icon: 'success',
                  title: "Thông Báo",
                  text: response.message,
                  confirmButtonText: "Đồng Ý !",
                }).then(() => {
                  window.location.reload();
                });
              } else {
                Swal.fire({
                  icon: 'error',
                  title: "Thông Báo",
                  text: response.message,
                  confirmButtonText: "Đồng Ý !",
                });
              }
            },
            error: function(xhr) {
              Swal.fire({
                icon: 'error',
                title: "Thông Báo",
                text: xhr.responseJSON.message,
                confirmButtonText: "Đồng Ý !",
              });
            }
          })
        }
      })
    }
</script> 
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/history/orders.blade.php ENDPATH**/ ?>