<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Transaction;
use App\Models\Service;
use App\Models\ServiceServer;
use App\Models\PartnerWebsite;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class OrderDuplicateChargeTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $admin;
    protected $service;
    protected $server;
    protected $partner;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo test data
        $this->user = User::factory()->create([
            'balance' => 10000,
            'status' => 'active',
            'api_token' => 'test-token-123',
            'domain' => 'test.com'
        ]);

        $this->admin = User::factory()->create([
            'balance' => 50000,
            'status' => 'active',
            'api_token' => 'admin-token-123',
            'domain' => env('APP_MAIN_SITE', 'main.com')
        ]);

        $this->partner = PartnerWebsite::factory()->create([
            'name' => 'test.com',
            'domain' => env('APP_MAIN_SITE', 'main.com'),
            'user_id' => $this->admin->id
        ]);

        $this->service = Service::factory()->create([
            'package' => 'facebook-like',
            'status' => 'active',
            'domain' => env('APP_MAIN_SITE', 'main.com')
        ]);

        $this->server = ServiceServer::factory()->create([
            'service_id' => $this->service->id,
            'status' => 'active',
            'visibility' => 'public',
            'providerName' => 'tuongtaccheo',
            'domain' => 'test.com'
        ]);
    }

    /** @test */
    public function test_prevent_duplicate_speed_up_requests()
    {
        // Tạo đơn hàng test
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'service_id' => $this->service->id,
            'server_id' => $this->server->id,
            'status' => 'Processing',
            'order_data' => json_encode(['quantity' => 1000]),
            'buff' => 500,
            'domain' => 'test.com'
        ]);

        // Test request đầu tiên
        $response1 = $this->withHeaders([
            'X-Access-Token' => $this->user->api_token,
            'Host' => 'test.com'
        ])->postJson('/api/v1/order/speed-up', [
            'order_id' => $order->id
        ]);

        // Test request thứ hai ngay lập tức (duplicate)
        $response2 = $this->withHeaders([
            'X-Access-Token' => $this->user->api_token,
            'Host' => 'test.com'
        ])->postJson('/api/v1/order/speed-up', [
            'order_id' => $order->id
        ]);

        // Request thứ hai phải bị từ chối
        $response2->assertStatus(400);
        $response2->assertJson([
            'status' => 'error',
            'message' => 'Yêu cầu tăng tốc đang được xử lý. Vui lòng đợi!'
        ]);
    }

    /** @test */
    public function test_prevent_duplicate_order_sync()
    {
        // Tạo đơn hàng cha (web mẹ)
        $parentOrder = Order::factory()->create([
            'user_id' => $this->admin->id,
            'status' => 'Completed',
            'start' => 100,
            'buff' => 1000,
            'domain' => env('APP_MAIN_SITE', 'main.com')
        ]);

        // Tạo đơn hàng con (webcon)
        $subOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_id' => $parentOrder->id,
            'orderProviderName' => env('APP_MAIN_SITE', 'main.com'),
            'status' => 'Processing',
            'start' => 0,
            'buff' => 0,
            'domain' => 'test.com'
        ]);

        // Gọi sync method nhiều lần đồng thời
        $controller = new \App\Http\Controllers\CronJob\StatusOrderServiceController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('syncOrderStatusToSubsites');
        $method->setAccessible(true);

        // Lần đầu tiên phải thành công
        $result1 = $method->invoke($controller, $parentOrder);
        $this->assertTrue($result1);

        // Lần thứ hai phải bị block bởi cache lock
        $result2 = $method->invoke($controller, $parentOrder);
        $this->assertFalse($result2);

        // Kiểm tra đơn hàng con đã được cập nhật
        $subOrder->refresh();
        $this->assertEquals('Completed', $subOrder->status);
        $this->assertEquals(100, $subOrder->start);
        $this->assertEquals(1000, $subOrder->buff);
    }

    /** @test */
    public function test_transaction_rollback_on_webcon_order_failure()
    {
        // Mock HTTP response thất bại từ web mẹ
        Http::fake([
            '*' => Http::response([
                'status' => 'error',
                'message' => 'API call failed'
            ], 400)
        ]);

        $initialBalance = $this->user->balance;
        $initialAdminBalance = $this->admin->balance;

        $response = $this->withHeaders([
            'X-Access-Token' => $this->user->api_token,
            'Host' => 'test.com'
        ])->postJson('/api/v1/start/create/order', [
            'provider_package' => $this->service->package,
            'provider_server' => 'sv-' . $this->server->package_id,
            'object_id' => 'https://facebook.com/test',
            'quantity' => 100,
            'reaction' => 'LIKE'
        ]);

        // Request phải thất bại
        $response->assertStatus(400);

        // Kiểm tra balance không bị trừ
        $this->user->refresh();
        $this->admin->refresh();
        $this->assertEquals($initialBalance, $this->user->balance);
        $this->assertEquals($initialAdminBalance, $this->admin->balance);

        // Kiểm tra không có transaction nào được tạo
        $this->assertEquals(0, Transaction::where('user_id', $this->user->id)->count());
    }

    /** @test */
    public function test_order_creation_with_proper_transaction_handling()
    {
        // Mock HTTP response thành công từ web mẹ
        Http::fake([
            '*' => Http::response([
                'status' => 'success',
                'data' => ['id' => 12345]
            ], 200)
        ]);

        $initialBalance = $this->user->balance;
        $initialAdminBalance = $this->admin->balance;

        $response = $this->withHeaders([
            'X-Access-Token' => $this->user->api_token,
            'Host' => 'test.com'
        ])->postJson('/api/v1/start/create/order', [
            'provider_package' => $this->service->package,
            'provider_server' => 'sv-' . $this->server->package_id,
            'object_id' => 'https://facebook.com/test',
            'quantity' => 100,
            'reaction' => 'LIKE'
        ]);

        // Request phải thành công
        $response->assertStatus(200);
        $response->assertJson(['status' => 'success']);

        // Kiểm tra đơn hàng được tạo
        $this->assertEquals(1, Order::where('user_id', $this->user->id)->count());

        // Kiểm tra transaction được tạo đúng
        $transaction = Transaction::where('user_id', $this->user->id)->first();
        $this->assertNotNull($transaction);
        $this->assertEquals('order', $transaction->type);
        $this->assertEquals('sub', $transaction->action);

        // Kiểm tra balance được trừ đúng
        $this->user->refresh();
        $this->admin->refresh();
        $this->assertLessThan($initialBalance, $this->user->balance);
        $this->assertLessThan($initialAdminBalance, $this->admin->balance);
    }

    protected function tearDown(): void
    {
        // Xóa cache sau mỗi test
        Cache::flush();
        parent::tearDown();
    }
}
