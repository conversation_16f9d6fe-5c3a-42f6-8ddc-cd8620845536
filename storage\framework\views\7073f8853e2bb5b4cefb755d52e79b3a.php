<?php $__env->startSection('content'); ?>
<?php $__env->startSection('title', 'Tiến trình'); ?>
<div class="col-md-12">
    <div class="card">
        <div class="card-header">
            <h5>Tất cả tiến trình</h5>
        </div>
        <div class="card-body">
            <!-- Form tìm kiếm -->
            <form method="GET" action="<?php echo e(route('account.progress')); ?>" class="mb-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Tìm kiếm mã đơn hàng:</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Nhập mã đơn hàng..." value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Trạng thái:</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">Tất cả</option>
                                <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Đang chờ</option>
                                <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Đang xử lý</option>
                                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Hoàn thành</option>
                                <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="start_date">Từ ngày:</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="<?php echo e(request('start_date')); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="end_date">Đến ngày:</label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="<?php echo e(request('end_date')); ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Tìm kiếm
                                </button>
                                <a href="<?php echo e(route('account.progress')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-refresh"></i> Làm mới
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <div class="dt-responsive table-responsive">
                <table id="data-table" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="fw-bold">
                        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <ul>
                                    <li><b>ID: </b><?php echo e($order->id); ?></li>
                                    <li><b>Mã đơn: </b><i onclick="coppy('<?php echo e($order->order_code); ?>')"><?php echo e($order->order_code); ?> <i class="fas fa-copy"></i></i></li>
                                    <li><b>Thời gian: </b><?php echo e($order->created_at->diffForHumans()); ?></li>
                                    <li><?php echo e($order->created_at); ?> -</li>
                                </ul>
                            </td>
                            <td>
                                <ul>
                                    <a href="<?php echo e(route('service', ['platform' => $order->service->platform->slug ?? 'Null', 'service' => $order->service->slug ?? 'Null'])); ?>">
                                        <li><b>Máy chủ <?php echo e(($order->server_id) ?? 'Không tìm thấy máy chủ'); ?> : </b><u class="text-sky-600"><?php echo e(($order->service->name) ?? 'Không tìm thấy dịch vụ'); ?></u></li>
                                    </a>
                                    <li>
                                        <b>Link: </b>
                                        <a href="javascript:;" onclick="coppy('<?php echo e($order->object_id); ?>')"><?php echo e($order->object_id); ?></a>
                                    </li>
                                    <li class="mt-1"><?php echo statusOrder($order->status, true); ?></li>
                                    <li class="mt-1">
                                        <a class="text-primary" href="<?php echo e(route('service', [ 'platform' => $order->service->platform->slug ?? 'Null', 'service' => $order->service->slug ?? 'Null'])); ?>">Đặt lại hàng<a> - 
                                        <a class="text-danger" href="<?php echo e(route('ticket')); ?>">Hỗ trợ</a>
                                    </li>
                                </ul>
                            </td>
                            <td>
                                <?php if(isset($order->server->action->refund_status) && $order->server->action->refund_status === 'on'): ?>                                <a href="javascript:;" class="btn btn-sm btn-warning"
                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Hoàn tiền"
                                    onclick="refundOrder('<?php echo e($order->order_code); ?>')">
                                <i class="fas fa-undo"></i>
                                <?php endif; ?>
                                
                                <?php if(isset($order->server->action->warranty_status) && $order->server->action->warranty_status === 'on'): ?>
                                <a href="javascript:;" class="btn btn-sm btn-info"
                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Bảo hành"
                                    onclick="warrantyOrder('<?php echo e($order->order_code); ?>')">
                                <i class="fas fa-sync"></i>
                                </a>
                                <?php endif; ?>
                                
                                <a href="javascript:;" class="btn btn-sm btn-primary"
                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Cập nhật"
                                    onclick="updateOrder('<?php echo e($order->order_code); ?>')">
                                <i class="fas fa-cube"></i>
                                </a>
                                
                                <?php if(isset($order->server->action->renews_status) && $order->server->action->renews_status === 'on'): ?>
                                <a href="javascript:;" class="btn btn-sm btn-info"
                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Gia hạn"
                                    onclick="renewsOrder('<?php echo e($order->order_code); ?>')">
                                <i class="fas fa-sync"></i>
                                </a>
                                <?php endif; ?>
                            </td>
                            <td>
                                <ul>
    <li><b>Số tiền: </b><span class="text-primary"><?php echo e(number_format((float) $order->price, 0, ',', '.')); ?>đ</span></li>
    <li><b>Số lượng: </b><span class="text-info"><?php echo e(number_format((int) $order->orderdata()['quantity'])); ?></span></li>
    <li><b>Bắt đầu: </b><?php echo e(number_format((int) $order->start)); ?></li>
    <li><b>Đã tăng: </b><?php echo e(number_format((int) $order->buff)); ?></li>
</ul>
                            </td>u
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->startSection('script'); ?>
<script src="<?php echo e(asset('assets/pack-lamtilo/js/service1.js?time=')); ?><?php echo e(time()); ?>"></script>
<?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/muasub.online/resources/views/guard/profile/progress.blade.php ENDPATH**/ ?>