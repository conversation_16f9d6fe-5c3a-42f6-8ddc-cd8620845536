@extends('admin.layouts.app')
@section('title', 'Phản Hồi Ticket Hỗ Trợ')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">Chi Tiết Ticket #{{ $ticket->id }}</h5>
                    <a href="{{ route('admin.ticket.ticket') }}" class="btn btn-secondary">
                        <i class="ti ti-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Thông tin ticket -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Thông tin ticket</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>ID:</strong></td>
                                        <td>#{{ $ticket->id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Người tạo:</strong></td>
                                        <td>{{ $ticket->user->name ?? 'N/A' }} ({{ $ticket->user->email ?? 'N/A' }})</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Danh mục:</strong></td>
                                        <td>{!! categoryTicket($ticket->category) !!}</td>
                                    </tr>

                                    <tr>
                                        <td><strong>Trạng thái:</strong></td>
                                        <td>{!! statusTicket($ticket->status) !!}</td>
                                    </tr>
                                    @if($ticket->order_id)
                                    <tr>
                                        <td><strong>Đơn hàng:</strong></td>
                                        <td>
                                            @if($ticket->order)
                                                <span class="badge bg-info">#{{ $ticket->order->order_code }}</span><br>
                                                <small class="text-muted">Tạo: {{ $ticket->order->created_at->format('d/m/Y H:i') }}</small>
                                            @else
                                                <span class="badge bg-warning">ID: {{ $ticket->order_id }}</span><br>
                                                <small class="text-muted">Đơn hàng không tồn tại trong hệ thống</small>
                                            @endif
                                        </td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td><strong>Thời gian tạo:</strong></td>
                                        <td>{{ $ticket->created_at->format('d/m/Y H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Nội dung yêu cầu</h6>
                            </div>
                            <div class="card-body">
                                <h6>{{ $ticket->title }}</h6>
                                <div class="mt-2">
                                    {!! $ticket->body !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form phản hồi -->
                <div class="card border">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Phản hồi ticket</h6>
                    </div>
                    <div class="card-body">
                        @if($ticket->reply)
                        <div class="alert alert-info">
                            <h6>Phản hồi hiện tại:</h6>
                            {!! $ticket->reply !!}
                        </div>
                        @endif

                        <form action="{{ route('admin.ticket.ticket.update', ['id' => $ticket->id]) }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group mb-3">
                                        <label for="reply">Nội dung phản hồi <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="reply" name="reply"
                                            placeholder="Nhập nội dung phản hồi..."
                                            style="height: 150px" required>{{ old('reply', $ticket->reply) }}</textarea>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="status">Trạng thái <span class="text-danger">*</span></label>
                                        <select class="form-control" name="status" id="status" required>
                                            <option value="pending" {{ $ticket->status == 'pending' ? 'selected' : '' }}>Chờ xử lý</option>
                                            <option value="processing" {{ $ticket->status == 'processing' ? 'selected' : '' }}>Đang xử lý</option>
                                            <option value="success" {{ $ticket->status == 'success' ? 'selected' : '' }}>Đã xử lý</option>
                                            <option value="cancelled" {{ $ticket->status == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="ti ti-send"></i> Cập nhật phản hồi
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script src="/assets/js/plugins/tinymce/tinymce.min.js"></script>
<script>
    // Khởi tạo TinyMCE
    tinymce.init({
        height: '200',
        selector: '#reply',
        content_style: 'body { font-family: "Inter", sans-serif; }',
        menubar: false,
        toolbar: [
            'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist | link'
        ],
        plugins: 'lists link',
        placeholder: 'Nhập nội dung phản hồi...'
    });

    // Auto-save draft (optional)
    let autoSaveTimer;
    function autoSave() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            const content = tinymce.get('reply').getContent();
            if (content.trim()) {
                localStorage.setItem('ticket_reply_{{ $ticket->id }}', content);
            }
        }, 2000);
    }

    // Load draft on page load
    document.addEventListener('DOMContentLoaded', function() {
        const savedContent = localStorage.getItem('ticket_reply_{{ $ticket->id }}');
        if (savedContent && !tinymce.get('reply').getContent().trim()) {
            tinymce.get('reply').setContent(savedContent);
        }
    });

    // Clear draft on successful submit
    document.querySelector('form').addEventListener('submit', function() {
        localStorage.removeItem('ticket_reply_{{ $ticket->id }}');
    });
</script>
@endsection
