<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Recharge;
use App\Models\Card;
use App\Models\Transaction;
use App\Models\User;
use App\Models\OrderProduct;
use App\Models\ConfigSite;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ViewAdminController extends Controller
{
    public function viewDashboard()
    {
        $domain = request()->getHost();
        //User
        $totalUser = User::where("domain", $domain)->count();
        $totalUserToday = User::where("domain", $domain)->whereDate("created_at", Carbon::today())->count();
        $totalUserWeek = User::where("domain", $domain)->whereBetween("created_at", [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count();
        $totalUserMonth = User::where("domain", $domain)->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", Carbon::now()->month)->count();

        //Nạp tiền
        $totalBalance = User::where('domain', $domain)->sum('balance');
        $totalRecharge = Transaction::where("domain", $domain)->whereIn("type", ["balance", "recharge"])->where("action", "add")->sum("before_balance");
        $totalRechargeToday = Transaction::where("domain", $domain)->whereDate("created_at", Carbon::today())->whereIn("type", ["balance", "recharge"])->where("action", "add")->sum("before_balance");
        $totalRechargeWeek = Transaction::where("domain", $domain)->whereBetween("created_at", [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->whereIn("type", ["balance", "recharge"])->where("action", "add")->sum("before_balance");
        $totalRechargeMonth = Transaction::where("domain", $domain)->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", Carbon::now()->month)->whereIn("type", ["balance", "recharge"])->where("action", "add")->sum("before_balance");        

        //Đơn hàng
        $totalOrder = Order::where('domain', $domain)->count();
        $OrderToday = Order::where('domain', $domain)->whereDate('created_at', Carbon::today())->count();
        $OrderWeek = Order::where('domain', $domain)->whereBetween("created_at", [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count();
        $OrderMonth = Order::where('domain', $domain)->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", Carbon::now()->month)->count();


        //Nạp tiền
        $totalTieuToday = Order::where('domain', $domain)->whereDate('created_at', Carbon::today())->sum('payment');

        // Doanh thu từ đơn hàng dịch vụ
        $totalRenvenueToday = Order::where('domain', $domain)->whereDate('created_at', Carbon::today())->sum('total_profit');
        $totalRenvenueWeek = Order::where('domain', $domain)->whereBetween("created_at", [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->sum('total_profit');
        $totalRenvenueMonth = Order::where('domain', $domain)->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", Carbon::now()->month)->sum('total_profit');
        $totalRevenue =  Order::where('domain', $domain)->sum('total_profit');

        // Doanh thu từ đơn hàng sản phẩm
        $productProfitToday = DB::table('order_products')
            ->where('domain', $domain)
            ->where('status', 'success')
            ->whereDate('created_at', Carbon::today())
            ->selectRaw('SUM(payment - (rate * quantity)) as profit')
            ->value('profit') ?? 0;

        $productProfitWeek = DB::table('order_products')
            ->where('domain', $domain)
            ->where('status', 'success')
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->selectRaw('SUM(payment - (rate * quantity)) as profit')
            ->value('profit') ?? 0;

        $productProfitMonth = DB::table('order_products')
            ->where('domain', $domain)
            ->where('status', 'success')
            ->whereYear('created_at', Carbon::now()->year)
            ->whereMonth('created_at', Carbon::now()->month)
            ->selectRaw('SUM(payment - (rate * quantity)) as profit')
            ->value('profit') ?? 0;

        $productProfitTotal = DB::table('order_products')
            ->where('domain', $domain)
            ->where('status', 'success')
            ->selectRaw('SUM(payment - (rate * quantity)) as profit')
            ->value('profit') ?? 0;

        // Tổng doanh thu (dịch vụ + sản phẩm)
        $totalRenvenueToday += $productProfitToday;
        $totalRenvenueWeek += $productProfitWeek;
        $totalRenvenueMonth += $productProfitMonth;
        $totalRevenue += $productProfitTotal;

        // Thống kê số lượng đơn hàng sản phẩm
        $totalProductOrders = OrderProduct::where('domain', $domain)->count();
        $productOrdersToday = OrderProduct::where('domain', $domain)->whereDate('created_at', Carbon::today())->count();
        $productOrdersWeek = OrderProduct::where('domain', $domain)->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count();
        $productOrdersMonth = OrderProduct::where('domain', $domain)->whereYear('created_at', Carbon::now()->year)->whereMonth('created_at', Carbon::now()->month)->count();

        $labels = [];
        $data = ['recharge' => [], 'order' => [], 'user' => []];

        for ($i = 1; $i <= 12; $i++) {
            $labels[] = "Tháng " . $i;
            $data["recharge"][] = (int) Transaction::where("domain", $domain)->whereIn("type", ["recharge", "balance"])->where("action", "add")->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", $i)->sum("before_balance");
            $data["user"][] = User::where("domain", $domain)->whereYear("created_at", Carbon::now()->year)->whereMonth("created_at", $i)->count();
        }

        $totalOrderStatus = [
            'totalOrder' => [],
            'totalProcessing' => [],
            'totalPending' => [],
            'totalCanceled' => [],
            'totalPendingRefundCancel' => [],
            'totalRefund' => [],
            'totalCompleted' => [],
        ];
        

        for ($month = 1; $month <= 12; $month++) {
            $totalOrderStatus['totalOrder'][] = Order::where('domain', $domain)->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalProcessing'][] = Order::where('domain', $domain)->where('status', 'Processing')->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalPending'][] = Order::where('domain', $domain)->where('status', 'Pending')->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalCanceled'][] = Order::where('domain', $domain)->where('status', 'Cancelled')->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalPendingRefundCancel'][] = Order::where('domain', $domain)->where('status', 'PendingRefundCancel')->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalRefund'][] = Order::where('domain', $domain)->where('status', 'Refunded')->whereMonth('created_at', $month)->count();
            $totalOrderStatus['totalCompleted'][] = Order::where('domain', $domain)->where('status', 'Completed')->whereMonth('created_at', $month)->count();

            // Doanh thu từ dịch vụ
            $serviceRevenue = (int) Order::where('domain', $domain)->whereMonth('created_at', $month)->sum('total_profit');

            // Doanh thu từ sản phẩm
            $productRevenue = (int) DB::table('order_products')
                ->where('domain', $domain)
                ->where('status', 'success')
                ->whereYear('created_at', Carbon::now()->year)
                ->whereMonth('created_at', $month)
                ->selectRaw('SUM(payment - (rate * quantity)) as profit')
                ->value('profit') ?? 0;

            // Tổng doanh thu
            $totalOrderStatus['totalRevenue'][] = $serviceRevenue + $productRevenue;
        }

        $statuses = [
            'Running',
            'Processing',
            'Holding',
            'Completed',
            'Cancelled',
            'Refunded',
            'Failed',
            'Pending',
            'PendingRefundCancel',
            'PendingRefundPartial',
            'Partially Refunded',
            'Partial',
            'Partially Completed',
            'WaitingForRefund',
            'Expired',
            'Success',
            'Active',
        ];
        
        $countStatusOrder = [];
        
        foreach ($statuses as $status) {
            $countStatusOrder[$status] = Order::where('domain', $domain)->where('status', $status)->count();
        }

        $top = request('top', 5);

        $topRechargeUsers = Transaction::where('domain', $domain)
            ->whereIn('type', ['recharge', 'balance'])
            ->where('action', 'add')
            ->whereYear('created_at', Carbon::now()->year)
            ->whereMonth('created_at', Carbon::now()->month)
            ->select('user_id', \DB::raw('SUM(before_balance) as total_recharge'))
            ->groupBy('user_id')
            ->orderByDesc('total_recharge')
            ->limit($top)
            ->get();
        
        $topRechargeUsers = $topRechargeUsers->map(function ($transaction) {
            $transaction->user = User::find($transaction->user_id);
            return $transaction;
        })->filter(function ($transaction) {
            return $transaction->user !== null;
        })->values();

        
        $levelUsersCount = User::where('domain', $domain)->selectRaw('level, COUNT(*) as count')->groupBy('level')->pluck('count', 'level');

        return view(
            'admin.dashboard',
            compact(
                "totalUser",
                "totalUserToday",
                "totalUserWeek",
                "totalUserMonth",

                'totalBalance', 
                "totalRecharge",
                "totalRechargeToday",
                "totalRechargeWeek",
                "totalRechargeMonth",

                'totalRecharge', 
                'totalTieuToday', 

                'totalOrder', 
                'OrderToday', 
                'OrderWeek', 
                'OrderMonth', 

                'totalRenvenueToday', 
                'totalRenvenueWeek', 
                'totalRenvenueMonth', 
                'totalRevenue', 
                'labels', 
                'data', 
                'totalOrderStatus',
                'countStatusOrder',
                'topRechargeUsers',
                'levelUsersCount',
                'productProfitToday',
                'productProfitWeek',
                'productProfitMonth',
                'productProfitTotal',
                'totalProductOrders',
                'productOrdersToday',
                'productOrdersWeek',
                'productOrdersMonth',

            )
        );
    }

    public function viewWebsiteConfig()
    {
        return view('admin.website.config');
    }

    public function viewSmtpConfig()
    {
        $domain = request()->getHost();

        // Lấy các email templates hiện tại
        $loginTemplate = \App\Models\EmailTemplate::getTemplate($domain, \App\Models\EmailTemplate::TYPE_LOGIN_NOTIFICATION);
        $passwordTemplate = \App\Models\EmailTemplate::getTemplate($domain, \App\Models\EmailTemplate::TYPE_PASSWORD_RESET);
        $productTemplate = \App\Models\EmailTemplate::getTemplate($domain, \App\Models\EmailTemplate::TYPE_PRODUCT_DELIVERY);

        return view('admin.smtp', compact('loginTemplate', 'passwordTemplate', 'productTemplate'));
    }

    public function viewRulesManage()
    {
        return view('admin.rules.manage');
    }
    public function viewCron()
    {
        return view('admin.cron');
    }
}
