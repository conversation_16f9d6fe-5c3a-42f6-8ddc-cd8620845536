@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON><PERSON> Tiền')
@section('content')
<div class="d-flex align-items-center mb-3">
    <div>
        <button type="button" class="btn btn-outline-primary recharge-btn mb-2 active" data-tab="bankings">
        <i class="fas fa-bank me-2"></i> <PERSON><PERSON>
        </button>
        <button type="button" class="btn btn-outline-primary recharge-btn mb-2" data-tab="cards">
        <i class="fad fa-credit-card me-2"></i> Thẻ Cào
        </button>
    </div>
</div>
<div class="row">
    <div class="col-md-12 recharge-tab" id="bankings">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title text-uppercase"><i class="fas fa-bank fs-5 me-2"></i>NGÂN HÀNG</h5>
            </div>
            <div class="card-body pt-0">
                <form action="" method="GET">
                    <div class="row justify-item-center mb-3">
                        <div class="col-md-2 col-3 mb-2">
                            <label class="form-label">Hiển Thị</label>
                            <select name="per_page" id="per_page" class="form-select form-control" onchange="this.form.submit()">
                                <option value="">--Hiển Thị --</option>
                                @foreach([10, 25, 50, 100, 1000, 5000, 10000] as $option)
                                <option value="{{ $option }}" {{ request('per_page') == $option ? 'selected' : '' }}>
                                - {{ $option }} -
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 col-8 mb-2">
                            <label class="form-label">Từ Ngày</label>
                            <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Đến Ngày</label>
                            <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Người Dùng</label>
                            <input type="text" name="username" class="form-control" value="{{ request('username') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Cổng Nạp</label>
                            <input type="text" name="bank_name" class="form-control" value="{{ request('bank_name') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Giao Dịch</label>
                            <input type="text" name="bank_code" class="form-control" value="{{ request('bank_code') }}">
                        </div>
                        <div class="col-md-3 col-8 mb-2">
                            <label class="form-label">Tìm Kiếm</label>
                            <div class=" input-group">
                                <input type="text" name="search" class="form-control" value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary"><i class="fad fa-search me-2"></i></button>
                            </div>
                        </div>
                        <div class="col-md-3 col-3 mb-2">
                            <a href="{{ route('admin.history.payment') }}" class="btn btn-secondary mt-4">
                            <i class="fad fa-sync-alt me-1"></i> Làm Mới
                            </a>
                        </div>
                    </div>
                </form>
                <div class="table-responsive">
                    <table class="table table-vcenter table--default text-nowrap table-borderless table-mb w-100">
                        <thead class="text-uppercase">
                            <tr>
                                <th>#</th>
                                <th>Tài Khoản</th>
                                <th>Phương Thức</th>
                                <th>Mã Giao Dịch</th>
                                <th>Số Tiền Nạp</th>
                                <th>Thực Nhận</th>
                                <th>Nội Dung</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($payments as $key => $payment)
                            <tr>
                                <td>{{ $payment->id }}</td>
                                <td>
                                    <ul>
                                        <li>{{ $payment->user->name ?? "Null" }}</li>
                                        <li>[{{ $payment->user->id ?? 'Null' }}] {{ $payment->user->username ?? 'Không Tìm Thấy Người Dùng' }}</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li><b>Mã GD: </b>{{ $payment->payment_method }}</li>
                                        <li><b>Thời Gian: </b>{{ $payment->created_at }}</li>
                                    </ul>
                                </td>
                                <td>{{ $payment->bank_code }}</td>
                                <td><span class="text-primary">{{ number_format($payment->amount) }}đ</span></td>
                                <td><span class="text-danger">{{ number_format($payment->real_amount) }}đ</span></td>
                                <td>
                                    <textarea type="text" class="form-control" rows="3" style="min-width: 400px" readonly>{{ $payment->note }}</textarea>
                                </td>
                            </tr>
                            @empty
                        @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center align-items-center mt-3 pagination-style-1">
                        {{ $payments->appends(request()->all())->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12 recharge-tab" id="cards">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title text-uppercase"><i class="fad fa-credit-card fs-5 me-2"></i>THẺ CÀO</h5>
            </div>
            <div class="card-body pt-0">
                <form action="" method="GET">
                    <input type="hidden" name="tab" value="cards">
                    <div class="row justify-item-center mb-3">
                        <div class="col-md-2 col-2 mb-2">
                            <label class="form-label">Hiển Thị</label>
                            <select name="card_per_page" id="card_per_page" class="form-select" onchange="this.form.submit()">
                                <option value="">--Hiển Thị --</option>
                                @foreach([10, 25, 50, 100, 1000, 5000, 10000] as $option)
                                <option value="{{ $option }}" {{ request('card_per_page') == $option ? 'selected' : '' }}>
                                - {{ $option }} -
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 col-9 mb-2">
                            <label class="form-label">Từ Ngày</label>
                            <input type="date" name="card_start_date" class="form-control" value="{{ request('card_start_date') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Đến Ngày</label>
                            <input type="date" name="card_end_date" class="form-control" value="{{ request('card_end_date') }}">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Tài Khoản</label>
                            <input type="text" name="card_username" class="form-control" value="{{ request('card_username') }}" placeholder="Tên Người Dùng...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Loại Thẻ</label>
                            <input type="text" name="card_type" class="form-control" value="{{ request('card_type') }}" placeholder="Loại Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Giao Dịch</label>
                            <input type="text" name="card_tranid" class="form-control" value="{{ request('card_tranid') }}" placeholder="Mã Giao Dịch...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Thẻ</label>
                            <input type="text" name="card_code" class="form-control" value="{{ request('card_code') }}" placeholder="Mã Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Seri</label>
                            <input type="text" name="card_serial" class="form-control" value="{{ request('card_serial') }}" placeholder="Seri Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Trạng Thái</label>
                            <select name="card_status" class="form-select">
                                <option value="">--Tất Cả --</option>
                                <option value="0" {{ request('card_status') == '0' ? 'selected' : '' }}>Đang Xử Lý</option>
                                <option value="1" {{ request('card_status') == '1' ? 'selected' : '' }}>Thành Công</option>
                                <option value="2" {{ request('card_status') == '2' ? 'selected' : '' }}>Thất Bại</option>
                                <option value="3" {{ request('card_status') == '3' ? 'selected' : '' }}>Sai Mệnh Giá</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Tìm Kiếm</label>
                            <div class="input-group">
                                <input type="text" name="card_search" class="form-control" value="{{ request('card_search') }}" placeholder="Tìm Kiếm...">
                                <button type="submit" class="btn btn-primary"><i class="fad fa-search me-2"></i></button>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="{{ route('admin.history.payment') }}" class="btn btn-secondary mt-4">
                            <i class="fad fa-sync-alt me-1"></i> Làm Mới
                            </a>
                        </div>
                    </div>
                </form>
                <div class="table-responsive">
                    <table class="table table-vcenter table--default text-nowrap table-borderless table-mb w-100">
                        <thead class="text-uppercase">
                            <tr>
                                <th>#</th>
                                <th>Tài Khoản</th>
                                <th>Mã Giao Dịch</th>
                                <th>Loại</th>
                                <th>Mã Thẻ</th>
                                <th>Seri</th>
                                <th>Số Tiền</th>
                                <th>Thực Nhận</th>
                                <th>Trạng Thái</th>
                                <th>Nội Dung</th>
                                <th>Thời Gian</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($cards as $key => $payment)
                            <tr>
                                <td>{{ $payment->id }}</td>
                                <td>
                                    <ul>
                                        <li>{{ $payment->user->name  ?? "Null"}}</li>
                                        <li>[{{ $payment->user->id ?? 'Null' }}] {{ $payment->user->username ?? 'Không Tìm Thấy Người Dùng' }}</li>
                                    </ul>
                                </td>
                                <td>{{ $payment->tranid }}</td>
                                <td>{{ $payment->card_type }}</td>
                                <td>{{ $payment->card_code }}</td>
                                <td>{{ $payment->card_serial }}</td>
                                <td>{{ number_format($payment->card_amount) }}đ</td>
                                <td>{{ number_format($payment->card_real_amount) }}đ</td>
                                <td>{!! statusCard($payment->status) !!}</td>
                                <td>
                                    <textarea type="text" class="form-control" rows="2" style="min-width: 400px" readonly>{{ $payment->note }}</textarea>
                                </td>
                                <td>{{ $payment->created_at }}</td>
                            </tr>
                            @empty
                            @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center align-items-center mt-3 pagination-style-1">
                        {{ $cards->appends(request()->all())->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    $(document).ready(function() {
        function showTab(tabId, button) {
            $('.recharge-tab').hide();
            $('#' + tabId).show();
            $('.recharge-btn').removeClass('active');
            $(button).addClass('active');
    
            localStorage.setItem('activeRechargeTab', tabId);
        }
        
        $('.recharge-btn').click(function() {
            const tabId = $(this).data('tab');
            showTab(tabId, this);
        });
    
        const savedTab = localStorage.getItem('activeRechargeTab') || $('.recharge-btn').first().data('tab');
        const savedButton = $('.recharge-btn[data-tab="' + savedTab + '"]');
        
        showTab(savedTab, savedButton);
    });
</script>
@endsection