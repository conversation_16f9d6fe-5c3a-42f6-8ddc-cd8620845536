@extends('guard.layouts.app')
@section('title', 'Nhậ<PERSON> K<PERSON>')
@section('content')
<div class="col-md-12">
    <div class="card">
        <div class="card-header">
            <h5><PERSON><PERSON><PERSON><PERSON> k<PERSON></h5>
        </div>
        <div class="card-body">
            <div class="dt-responsive table-responsive">
                <table id="dom-table" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Giao dịch</th>
                            <th>Th<PERSON>i gian</th>
                            <th>Mã giao dịch</th>
                            <th><PERSON>ố dư trước</th>
                            <th>Số tiền</th>
                            <th>Số dư sau</th>
                            <th>Nội dung</th>
                        </tr>
                    </thead>
                    <tbody class="fw-bold">
                        @foreach ($transactions as $transaction)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>
                                @if ($transaction->type == 'recharge')
                                <span class="badge bg-success"><PERSON><PERSON><PERSON> tiền</span>
                                @elseif ($transaction->type == 'order')
                                <span class="badge bg-primary">Tạo đơn</span>
                                @elseif ($transaction->type == 'refund')
                                <span class="badge bg-danger">Hoàn tiền</span>
                                @elseif ($transaction->type == 'balance')
                                <span class="badge bg-warning">Điều chỉnh số dư</span>
                                @endif
                            </td>
                            <td>{{ $transaction->created_at->diffForHumans() }}</td>
                            <td>{{ $transaction->tran_code }}</td>
                            <td class="text-muted">{{ number_format($transaction->before_balance) }} VNĐ</td>
                            <td>
                                @if ($transaction->action == 'add')
                                <span class="text-success">+{{ number_format($transaction->first_balance) }}</span>
                                @elseif ($transaction->action == 'sub')
                                <span class="text-danger">-{{ number_format($transaction->first_balance) }}</span>
                                @endif
                            </td>
                            <td class="text-primary fw-bold">{{ number_format($transaction->after_balance) }} VNĐ</td>
                            <td>
                                <textarea class="form-control" rows="1" readonly>{{ $transaction->note }}</textarea>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection