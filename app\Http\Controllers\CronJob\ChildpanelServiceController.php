<?php

namespace App\Http\Controllers\CronJob;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Models\ServerAction;
use App\Models\Service;
use App\Models\User;
use App\Models\PartnerWebsite;
use App\Models\ServiceServer;
use App\Models\Order;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ChildpanelServiceController extends Controller
{
    protected function roundPrice($basePrice, $percent) {
        $price = $basePrice + ($basePrice * $percent) / 100;
    
        if (round($price, 2) > 0) {
            return round($price, 2);  
        } elseif (round($price, 3) > 0) {
            return round($price, 3); 
        }
        return round($price, 4);  
    }

     public function updateSubSiteServices()
    {
        set_time_limit(900);

        $partner = PartnerWebsite::where("name", getDomain())->first();
        $usernss = User::find($partner->user_id);
        $servers = ServiceServer::where("domain", $partner->domain)->get();
        $host = request()->getHost();
    
        $existingServices = ServiceServer::where("domain", $host)->get();
        foreach ($existingServices as $existingService) {
            $serverExist = $servers->first(function ($server) use ($existingService) {
                return $server->package_id === $existingService->package_id 
                    && $server->service_id === $existingService->service_id;
            });
            if (!$serverExist) {
                $existingService->delete();
            }
        }
    
        foreach ($servers as $server) {
            $priceCurrent = $server->levelPrice($usernss->level);
            $priceMember = $this->roundPrice($priceCurrent, siteValue("price"));
            $priceCollaborator = $this->roundPrice($priceCurrent, siteValue("price_collaborator"));
            $priceAgency = $this->roundPrice($priceCurrent, siteValue("price_agency"));
            $priceDistributor = $this->roundPrice($priceCurrent, siteValue("price_distributor"));
            $status = $server->status === 'inactive' ? 'inactive' : 'active';
    
            $serverExist = ServiceServer::where([
                'package_id' => $server->package_id,
                'service_id' => $server->service_id,
                'domain' => $host
            ])->first();
    
            $serviceData = [
                'name' => $server->name,
                'details' => $server->details,
                'percents' => $server->percents,
                'price' => $priceCurrent,
                'price_update' => $priceCurrent,
                'price_member' => $priceMember,
                'price_collaborator' => $priceCollaborator,
                'price_agency' => $priceAgency,
                'price_distributor' => $priceDistributor,
                'min' => $server->min,
                'max' => $server->max,
                'limit_day' => $server->limit_day,
                'status' => $status,
                'visibility' => $server->visibility,
                'domain' => $host,
                'updated_at' => $server->updated_at,
            ];
    
            $service = ServiceServer::updateOrCreate([
                'package_id' => $server->package_id,
                'service_id' => $server->service_id,
                'domain' => $host
            ], $serviceData);
    
            // Xử lý hành động
            $action = $server->action;
            $actionData = [
                'get_uid' => $action->get_uid,
                'auto_price' => $action->auto_price,
                'quantity_status' => $action->quantity_status,
                'reaction_status' => $action->reaction_status,
                'reaction_data' => $action->reaction_data,
                'comments_status' => $action->comments_status,
                'comments_data' => $action->comments_data,
                'minutes_status' => $action->minutes_status,
                'minutes_data' => $action->minutes_data,
                'time_status' => $action->time_status,
                'time_data' => $action->time_data,
                'posts_status' => $action->posts_status,
                'posts_data' => $action->posts_data,
                'refund_status' => $action->refund_status,
                'warranty_status' => $action->warranty_status,
                'up_status' => $action->warranty_status,
                'domain' => $host,
            ];
    
            ServerAction::updateOrCreate(
                ['server_id' => $service->id],
                $actionData
            );
        }
    
        return response()->json([
            'code' => 200,
            'message' => 'Đồng bộ dịch vụ thành công!',
            'status' => 'SUCCESS',
        ]);
    }

    public function updateSubSitePrice(Request $request)
    {
        if (getDomain() !== env('APP_MAIN_SITE')) {
            $servers = ServiceServer::where('domain', request()->getHost())->get();
            foreach ($servers as $server) {
                $percentMember = site('price') ?? 20;
                $percentCollaborator = site('price_collaborator') ?? 18;
                $percentAgency = site('price_agency') ?? 17;
                $percentDistributor = site('price_distributor') ?? 15;
    
                $priceMember = $this->roundPrice($server->price, $percentMember);
                $priceCollaborator = $this->roundPrice($server->price, $percentCollaborator);
                $priceAgency = $this->roundPrice($server->price, $percentAgency);
                $priceDistributor = $this->roundPrice($server->price, $percentDistributor);
    
                $server->update([
                    'price_member' => $priceMember,
                    'price_collaborator' => $priceCollaborator,
                    'price_agency' => $priceAgency,
                    'price_distributor' => $priceDistributor,
                ]);
            }
        }
        return response()->json([
            'code' => 200,
            'message' => 'Cập nhật giá thành công.',
            'status' => 'SUCCESS',
        ]);
    }

    public function updateSubSiteOrderStatus(Request $request)
    {
        set_time_limit(300);
        
        $batchSize = 50;
        $maxProcessTime = 60; 
        $startTime = time();
        $processedCount = 0;
        $log = [];
        
        try {
            if (!Schema::hasColumn('orders', 'last_status_check')) {
                Schema::table('orders', function ($table) {
                    $table->timestamp('last_status_check')->nullable();
                });
            }
            
            while (time() - $startTime < $maxProcessTime) {
                DB::beginTransaction();
                
                $orders = Order::where("domain", getDomain())
                    ->where("orderProviderName", env("APP_MAIN_SITE"))
                    ->where(function($query) {
                        $query->where("status", "!=", "Completed")
                            ->where("status", "!=", "Cancelled")
                            ->where("status", "!=", "Refunded")
                            ->where("status", "!=", "Failed")
                            ->where("status", "!=", "Partial")
                            ->where("status", "!=", "Success")
                            ->where("status", "!=", "PendingRefundPartial")
                            ->where("status", "!=", "PendingRefundCancel")
                            ->where("status", "!=", "Partially Refunded")
                            ->where("status", "!=", "Partially Completed");
                    })
                    ->orderBy('id', 'desc')
                    ->limit($batchSize)
                    ->lockForUpdate()
                    ->get();
                    
                if ($orders->isEmpty()) {
                    DB::rollBack();
                    break; 
                }
                
                $orderIds = $orders->pluck('id')->toArray();
                Order::whereIn('id', $orderIds)->update(['last_status_check' => now()]);
                    
                DB::commit();
                
                foreach ($orders as $order) {
                    $processedCount++;
                    $originalStatus = $order->status;
                    $orderId = $order["order_id"];
                    
                    try {
                        $parentOrder = Order::where("id", $orderId)->first();
                        
                        if ($parentOrder) {
                            $order->start = $parentOrder->start;
                            $order->buff = $parentOrder->buff;
                            $order->status = $parentOrder->status;
                            
                            if (($order->status == 'Completed' || $order->status == 'Success') && 
                                ($originalStatus != 'Completed' && $originalStatus != 'Success')) {
                                $order->completed_at = now();
                            }
                            
                            $order->save();
                            $log[] = "Đã cập nhật đơn hàng #{$order->id}: {$originalStatus} -> {$order->status}";
                        } else {
                            $log[] = "Không tìm thấy đơn hàng gốc cho #{$order->id}";
                        }
                    } catch (\Exception $e) {
                        $log[] = "Lỗi xử lý đơn hàng #{$order->id}: " . $e->getMessage();
                    }
                    
                    if (time() - $startTime >= $maxProcessTime) {
                        break;
                    }
                }
            }
            
            return response()->json([
                "code" => 200,
                "message" => "Đã cập nhật trạng thái {$processedCount} đơn hàng.",
                "status" => "SUCCESS",
                //"log" => $log
            ]);
        } catch (\Exception $e) {
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
            
            return response()->json([
                "code" => 500,
                "message" => "Lỗi khi cập nhật trạng thái: " . $e->getMessage(),
                "status" => "ERROR",
            ]);
        }
    }
}