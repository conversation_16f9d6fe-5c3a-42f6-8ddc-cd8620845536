<?php

namespace App\Http\Controllers\Api\Service;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TraodoisubController extends Controller
{
    public $username = '';
    public $password = '';
    public $path = "";
    public $server = "";
    public $data = [
        'object_id' => '',
        'quantity' => '',
        'speed' => '',
        'comment' => '',
        'minutes' => '',
        'time' => '',
        'days' => '',
        'post' => '',
        'reaction' => '',
        'server_order' => '', 
        'social' => '',
    ];
    
    public function __construct()
    {
        $this->username = siteValue('account_tds');
        $this->password = md5(siteValue('password_tds'));
    }

    /**
     * Chuyển đổi URL rút gọn của TikTok thành URL đầy đủ
     * 
     * @param string $url URL cần chuyển đổi
     * @return string URL sau khi chuyển đổi
     */
    protected function convertShortTikTokUrl($url) 
    {
        // Nếu không phải URL TikTok rút gọn thì trả về nguyên bản
        if (strpos($url, 'vt.tiktok.com') === false && 
            strpos($url, 'm.tiktok.com') === false && 
            strpos($url, 'vm.tiktok.com') === false) {
            return $url;
        }
        
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_HEADER => true,
                CURLOPT_NOBODY => true,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => false,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            ]);
            
            $result = curl_exec($ch);
            
            // Kiểm tra lỗi CURL
            if ($result === false) {
                Log::error('Lỗi khi chuyển đổi URL TikTok: ' . curl_error($ch));
                curl_close($ch);
                return $url;
            }
            
            // Lấy URL chuyển hướng từ header
            preg_match('/Location: (.*)\r\n/', $result, $matches);
            if (isset($matches[1])) {
                $redirectUrl = trim($matches[1]);
                
                // Nếu URL chuyển hướng vẫn là dạng rút gọn, thử theo dõi một lần nữa
                if (strpos($redirectUrl, 'vt.tiktok.com') !== false || 
                    strpos($redirectUrl, 'm.tiktok.com') !== false || 
                    strpos($redirectUrl, 'vm.tiktok.com') !== false) {
                    
                    curl_setopt($ch, CURLOPT_URL, $redirectUrl);
                    $result = curl_exec($ch);
                    
                    if ($result === false) {
                        Log::error('Lỗi khi chuyển đổi URL TikTok (lần 2): ' . curl_error($ch));
                        curl_close($ch);
                        return $url;
                    }
                    
                    preg_match('/Location: (.*)\r\n/', $result, $matches);
                    if (isset($matches[1])) {
                        $redirectUrl = trim($matches[1]);
                    }
                }
                
                curl_close($ch);
                
                // Đảm bảo URL hoàn chỉnh và có định dạng chuẩn
                if (strpos($redirectUrl, 'www.tiktok.com') !== false && strpos($redirectUrl, '/video/') !== false) {
                    return $redirectUrl;
                } else {
                    Log::warning('URL sau chuyển hướng không phải định dạng TikTok chuẩn: ' . $redirectUrl);
                }
                
                return $redirectUrl;
            }
            
            curl_close($ch);
            return $url;
        } catch (\Exception $e) {
            Log::error('Ngoại lệ khi chuyển đổi URL TikTok: ' . $e->getMessage());
            return $url;
        }
    }

    public function createOrder()
    {
        // Chuyển đổi URL nếu là link TikTok rút gọn
        if (isset($this->data['object_id']) && !empty($this->data['object_id'])) {
            $this->data['object_id'] = $this->convertShortTikTokUrl($this->data['object_id']);
        }
         
        $path = "https://traodoisub.com/api/order/?fields=".$this->path."&user=".$this->username."&token=".$this->password;
        $data = $this->data;
        if (isset($data['comment']) && $data['comment']) {
            $commentsArray = explode("\n", $data['comment']);
            $commentChunks = array_slice($commentsArray, 0, 5);
            $data['comment'] = implode("\n", $commentChunks);
        }
        
        $dataPost = array(
            'id' => $data['object_id'] ?? '',
            'sl' => $data['quantity'] ?? '0',
            'speed' => 1, 
            'sever' => $data['server_order'] ?? '?',
            'time_pack' => $data['days'] ?? '?',
            'post' => $data['post'] ?? '?',
            'packet' => $data['quantity_like'] ?? '50',
            'type' => strtoupper($data['reaction'] ?? 'LIKE'),
            'noidung' => $data['comment'] ?? '',
            'timeLive' => $data['minutes'] ?? '?',
        );
        
        $result = $this->sendRequest($path, $dataPost);
        $result = json_decode($result, true);
        
        if (isset($result['code']) && $result['code'] == 200) {
            return [
                'status' => true,
                'message' => 'Đặt hàng thành công',
                'data' => $result['order_code']
            ];
        } 
        elseif(isset($result['msg']) && $result['msg'] == "Vui lòng thêm đủ dữ liệu đầu vào")
        {
            $curl = curl_init();
            
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://traodoisub.com/api/order/?fields=".$this->path."&type=".strtoupper($data['reaction'] ?? 'LIKE')."&sl=".$data['quantity'] ."&id=".$data['object_id']."&user=".$this->username."&token=".$this->password,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            ));
            
            $response = curl_exec($curl);
            $error = curl_error($curl);
            curl_close($curl);
            
            if ($error) {
                Log::error('Lỗi CURL khi gọi API TraoDoiSub: ' . $error);
                return [
                    'status' => false,
                    'message' => 'Lỗi kết nối đến máy chủ: ' . $error,
                    'data' => null
                ];
            }
            
            $result = json_decode($response, true);
            if (isset($result['code']) && $result['code'] == 200) {
                return [
                    'status' => true,
                    'message' => 'Đặt hàng thành công',
                    'data' => $result['order_code']
                ];
            } 
            else {
                $errorMsg = isset($result['msg']) ? $result['msg'] : 'Lỗi không xác định từ API';
                Log::warning('Lỗi từ API TraoDoiSub: ' . $errorMsg);
                return [
                    'status' => false,
                    'message' => $errorMsg,
                    'data' => $errorMsg
                ];
            }
        }
        elseif(isset($result['msg']) && $result['msg'] == "Dữ liệu không hợp lệ!")
        {
            $curl = curl_init();
            
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://traodoisub.com/api/order/?fields=".$this->path."&time_pack=".$data['days']."&packet=".$data['quantity_like'] ."&id=".$data['object_id']."&sever=".$data['server_order']."&user=".$this->username."&token=".$this->password,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            ));
            
            $response = curl_exec($curl);
            $error = curl_error($curl);
            curl_close($curl);
            
            if ($error) {
                Log::error('Lỗi CURL khi gọi API TraoDoiSub: ' . $error);
                return [
                    'status' => false,
                    'message' => 'Lỗi kết nối đến máy chủ: ' . $error,
                    'data' => null
                ];
            }
            
            $result = json_decode($response, true);
            if (isset($result['code']) && $result['code'] == 200) {
                return [
                    'status' => true,
                    'message' => 'Đặt hàng thành công',
                    'data' => $result['order_code']
                ];
            } 
            else {
                $errorMsg = isset($result['msg']) ? $result['msg'] : 'Lỗi không xác định từ API';
                Log::warning('Lỗi từ API TraoDoiSub: ' . $errorMsg);
                return [
                    'status' => false,
                    'message' => $errorMsg,
                    'data' => $errorMsg
                ];
            }
        }
        
        else {
            $errorMsg = isset($result['msg']) ? $result['msg'] : 'Lỗi không xác định từ API';
            Log::warning('Lỗi từ API TraoDoiSub: ' . $errorMsg);
            return [
                'status' => false,
                'message' => $errorMsg,
                'data' => $errorMsg
            ];
        }
    }
    
    public function order($id)
    {
        $path = "https://traodoisub.com/api/order/info.php?fields=".$this->path."&user=".$this->username."&token=".$this->password."&id=".$id;
        $result = $this->cul($path);
        
        return [
            'status' => true,
            'data' => $result
        ];
    }
  
    public function sendRequest($path, $data)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $path,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);
        
        if ($error) {
            Log::error('Lỗi CURL khi gửi request POST: ' . $error);
        }
        
        return $response;
    }
    
    public function cul($path)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $path,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);
        
        if ($error) {
            Log::error('Lỗi CURL khi gửi request GET: ' . $error);
            return ['error' => $error];
        }
        
        return json_decode($response, true);
    }
}