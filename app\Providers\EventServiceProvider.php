<?php

namespace App\Providers;

use App\Events\ProductDelivered;
use App\Listeners\SendLoginNotification;
use App\Listeners\SendProductDeliveryNotification;
use App\Listeners\SendRegisterNotification;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendRegisterNotification::class,
        ],
        Login::class => [
            SendLoginNotification::class,
        ],
        ProductDelivered::class => [
            SendProductDeliveryNotification::class,
        ],
    ];

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
