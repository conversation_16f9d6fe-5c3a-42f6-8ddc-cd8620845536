@import url(https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200;300;400;500;600;700;800&display=swap);img{max-width:100%}
.form-check-input{border:1px solid #c4c4c4}
.dt-orderable-none{padding-right:.75rem!important}
.dt-orderable-none .dt-column-order{display:none}
.page-wrapper .page-content .page-title-box .page-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
.form-label{color:#000}
.form-servers{max-height:300px;overflow-y:auto;padding:.517rem;background:#f4f6f9}
.form-servers::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);background-color:#F5F5F5}
.form-servers::-webkit-scrollbar{width:9px;background-color:#F5F5F5}
.form-servers::-webkit-scrollbar-thumb{background-color:#ff3636;background-image:-webkit-linear-gradient(45deg,rgba(255,255,255,.2) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.2) 50%,rgba(255,255,255,.2) 75%,transparent 75%,transparent)}
@media (max-width:768px){
  .card-body{padding:1rem}
  .page-wrapper .page-content,.topbar{padding:0}
  .footer{position:unset}
}
#seoContent{font-size:14px;line-height:1.6;font-family:"Roboto", sans-serif;}
#seoContent h2,#seoContent h3,#seoContent h4{position:relative;cursor:pointer}
#seoContent h2::after,#seoContent h3::after,#seoContent h4::after{content:'';position:absolute;width:0;height:2px;background-color:#0d6efd;bottom:0;left:0;transition:width .3s ease}
#seoContent h2:hover::after,#seoContent h3:hover::after,#seoContent h4:hover::after{width:100%}
#showMoreBtn{display:block;margin:0 auto;padding:5px 10px;font-size:12px;border-radius:4px;cursor:pointer}
#seoContent h1{font-size:26px}
#seoContent h2{font-size:24px}
#seoContent h3{font-size:20px}
#seoContent h4{font-size:18px}
#seoContentText{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}
#seoContentText.show-all{display:block}
#seoContent table{width:100%;border:0;margin-bottom:1rem}
#seoContent table tr{border-bottom:1px solid rgb(13 13 13 / 5%)}
#seoContent table td{padding:10px}
#details>div:empty{display:none}
.card{--bs-card-spacer-y:30px;--bs-card-spacer-x:30px;--bs-card-title-spacer-y:0.5rem;--bs-card-title-color:#111c2d;--bs-card-subtitle-color:#707a82;--bs-card-border-width:0;--bs-card-border-color:var(--bs-border-color-translucent);--bs-card-border-radius:1.125rem;--bs-card-box-shadow:0 2px 6px rgba(37,83,185,0.1);--bs-card-inner-border-radius:calc(1.125rem - 0);--bs-card-cap-padding-y:15px;--bs-card-cap-padding-x:30px;--bs-card-cap-bg:rgba(var(--bs-body-color-rgb),0.03);--bs-card-bg:var(--bs-body-bg);--bs-card-img-overlay-padding:1rem;--bs-card-group-margin:12px;position:relative;display:flex;flex-direction:column;min-width:0;height:var(--bs-card-height);color:var(--bs-body-color);word-wrap:break-word;background-color:#fff;background-clip:border-box;border:var(--bs-card-border-width) solid var(--bs-card-border-color);border-radius:var(--bs-card-border-radius);box-shadow:var(--bs-card-box-shadow)}
.card .card-header{border-radius:1.125rem 1.125rem 0 0;background:rgba(49, 103, 243, 0.1)}
.topbar{background:#fff;position:sticky;top:0;padding:0 15px;margin-bottom:15px;box-shadow:0 2px 6px rgba(37,83,185,0.1);border-radius:0 0 1.125rem 1.125rem;width:100%!important}
@media (max-width:768px){
    .topbar{margin-left:calc(1.5rem * -0.5);margin-right:calc(1.5rem * -0.5);width:calc(100% + 1.5rem)!important}
    .topbar .topbar-custom.nav-sticky:last-child ul li:last-child .nav-icon{margin-right:0}
    .topbar .topbar-custom.nav-sticky .nav-link.mobile-menu-btn{margin-left:0}
}
.page-wrapper .page-content{margin-top:0}
body{width:100vw;overflow-x:hidden;font-family:"Plus Jakarta Sans", sans-serif}
.auth-bg{position:relative;overflow:hidden}
.auth-bg:before{content:"";position:absolute;height:100%;width:100%;opacity:0.3;background:radial-gradient(#d2f1df,#d3d7fa,#bad8f4) 0 0 / 400% 400%}
body:has(.auth-header-box) .mx-auto{position:relative}
body:has(.auth-header-box) .mx-auto:after{content:"";position:absolute;left:-80px;bottom:-80px;width:200px;height:200px;border-radius:100%;background-color:#fb977d}
body:has(.auth-header-box) .mx-auto > .card{z-index:1}
body:has(.auth-header-box) .mx-auto:before{content:"";position:absolute;top:-70px;right:-70px;width:200px;height:215px;background-image:url(../images/extra/shap-login.png);background-repeat:no-repeat;background-size:contain}
.dropdown-menu.show:has(.change-language){margin-top:-10px!important}
.startbar{background:#fff;box-shadow:0 2px 6px rgba(37,83,185,0.1);border:none}
.startbar .startbar-menu .navbar-nav .nav-item .nav-link{color:#111c2d}
.startbar .startbar-menu .navbar-nav .nav-item .nav-link.active,.startbar .startbar-menu .navbar-nav .nav-item .nav-link:hover,.startbar .startbar-menu .navbar-nav .nav-item .nav-link.active i,.startbar .startbar-menu .navbar-nav .nav-item .nav-link:hover i{color:#3167f3}
.primary-hover-bg::before{content:"";position:absolute;top:0;bottom:0;left:-16px;width:0;height:100%;z-index:-1;border-radius:0 24px 24px 0;transition:all 0.4s ease-in-out}
.navbar-nav > li > .primary-hover-bg:focus::before, .navbar-nav > li > .primary-hover-bg:hover::before,.navbar-nav > li > .primary-hover-bg.active::before{background:rgba(0,116,186,0.1);width:calc(100% + 16px)}
.nav-pills .nav-link.active, .nav-pills .show>.nav-link{color: var(--bs-nav-pills-link-active-color)!important}
@media (min-width:1200px){body[data-sidebar-size=collapsed] .startbar:not(:hover) .startbar-menu .navbar-nav .nav-item .nav-link img.menu-icon{width:20px;height:20px;max-width:20px}}
@media (min-width:768px){.footer{padding:0 1.5rem}}
.form-select {border-radius:0.5rem;margin:0 0.5rem;background-position: right .5rem center;padding-right:1.5rem;}
.startbar .startbar-menu .navbar-nav .nav-item .nav .nav-item .nav-link{padding: 6px 0 6px 6px}
.icon-middle{position:fixed;right:17px;bottom:80px;transition:all .2s;z-index:3}
.icon-middle .icon{margin-bottom:10px;z-index:1;width:45px;height:45px;background:#3697D7;color:#fff;display:inherit;text-align:center;line-height:43px;cursor:pointer;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;transition:all .3s}
.icon-middle .icon:hover{box-shadow:0 8px 25px -8px #071666}
.icon-middle .icon img{width:auto;max-width:100%}
.icon-middle .icon img.icon-svg{width:calc(100% - 25px);vertical-align:middle}
.mini-icons .icon-middle{right:-60px}
#middle-control{-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:40px;height:40px;position:fixed;bottom:30px;right:20px;cursor:pointer;z-index:90;background:#3697D7;-webkit-box-shadow:0 0 15px 1px rgba(69,65,78,0.2);box-shadow:0 0 15px 1px rgba(69,65,78,0.2);opacity:1;-webkit-transition:all 0.3s;transition:all 0.3s;border-radius:4px;padding:9px}
#middle-control i{font-size:21px;color:#ffffff}
body .select2-container .select2-search--inline .select2-search__field{font-family:inherit}
body .select2-container--classic .select2-search--dropdown .select2-search__field,body .select2-container--default .select2-search--dropdown .select2-search__field,body .select2-container--default.select2-container--focus .select2-selection--multiple,body .select2-dropdown{border:1px solid var(--bs-border-color);border-radius:10px;outline:none}
.select2-container--open .select2-dropdown--below{box-shadow:0 0 10px rgba(0,0,0,0.1)}
body .select2-container--classic .select2-selection--single,body .select2-container--default .select2-selection--multiple,body .select2-container--default .select2-selection--single{border-radius:var(--bs-border-radius)}
body .select2-container--classic .select2-selection--single,body .select2-container--classic .select2-selection--single .select2-selection__arrow{background-color:var(--bs-white);background-image:unset!important}
body .select2-container--classic .select2-selection--single .select2-selection__arrow{border:0}
body .select2-container .select2-search--inline .select2-search__field::-moz-placeholder{color:var(--bs-body-color)}
body .select2-container .select2-search--inline .select2-search__field::placeholder{color:var(--bs-body-color)}
body .select2-container--default .select2-results__option--highlighted.select2-results__option--selectable,body .select2-container--default .select2-results__option--selected{background-color:var(--bs-light);color:var(--bs-primary)}
body .select2-results__option{padding:8px 10px}
body .select2-container--default.select2-container--disabled .select2-selection--multiple{background-color:var(--bs-light)}
body .select2-container--classic .select2-selection--single,body .select2-container--classic .select2-selection--single .select2-selection__arrow,body .select2-container--classic .select2-selection--single .select2-selection__rendered,body .select2-container--default .select2-selection--multiple,body .select2-container--default .select2-selection--single,body .select2-container--default .select2-selection--single,body .select2-container--default .select2-selection--single .select2-selection__rendered{border:none;border-radius:7px;background-color:var(--bs-light);color:var(--bs-body-color);height:auto;line-height:20px;padding:4px 6px;outline:none}
.select2-results__options{max-height:500px!important}
body .select2-container--classic .select2-selection--multiple .select2-selection__choice,body .select2-container--default .select2-selection--multiple .select2-selection__choice,body .select2-container--default .select2-selection--multiple .select2-selection__choice__remove{background-color:var(--bs-primary);border-color:var(--bs-primary);color:#fff}
body .select2-container--default .select2-selection--multiple .select2-selection__choice{border-radius:var(--bs-border-radius)}
body .select2-container--default .select2-selection--single .select2-selection__arrow{background:#f4f6f9}
.table>thead{background:#f4f6f9}
.desc-list{height:47px;padding:0 10px;display:flex;align-items:center;gap:15px;background:#efefef;border-radius:15px;margin-top:5px}
.desc-list .icon{width:32px;height:32px;flex:0 0 32px;color:#28cb69;background-color:rgb(40,203,105,.15);display:flex;align-items:center;justify-content:center;border-radius:50%}
.desc-list .text{white-space:nowrap;text-transform:capitalize}
.text-list{color:#6f767e}
.body-note iframe{width:100%;height:auto;aspect-ratio:1.5}
.card-free{position:relative;border:1px solid #ff807d;box-shadow:0 3px 3px rgb(254 211 210)}
.card-free img{width:80px;position:absolute;top:-15px;right:-10px}
.card-free .form-control:focus{border-color:#ff7f7d}
.bg-affiliate{background:url(/assets/images/extra/info-shap.png) no-repeat top right;border:1px solid #46caeb;box-shadow:0 3px 3px rgb(213 241 249) !important}
@media (max-width:768px) {
  .desc-list{gap:8px}
}
@media (min-width:1200px){
  .page-wrapper .page-content{max-width:calc(100% - var(--bs-startbar-width))}
  body[data-sidebar-size=collapsed] .page-wrapper .page-content{max-width:calc(100% - var(--bs-startbar-collapsed-width))}
}
.toc-section{border:1px solid #aaa;margin-bottom:1.5rem}
#tocContainer{display:none;padding:15px;border-top:1px solid rgba(0,0,0,.125);}
.toc > ol{font-weight:bold;color:#000}
.header-toc{padding:15px;font-size:18px}
.toc{counter-reset:section;list-style:none;padding-left:30px;margin:0}
.toc > li{counter-increment:section;margin-bottom:0.5em;position:relative}
.toc > li::before{content:counter(section) ". ";font-weight:bold;position:absolute;left:-20px}
.toc > li > ol{counter-reset:subsection;list-style:none;padding-left:30px}
.toc > li > ol > li{counter-increment:subsection;position:relative;font-weight:normal}
.toc > li > ol > li::before{content:counter(section) "." counter(subsection) " ";position:absolute;left:-30px}
.toc > li:last-child{margin:0;}
.toc li{line-height:1.6}
.toc li a{color:#000}
.toc li:hover > a,.toc li:focus > a,.toc li:hover:before,.toc li:focus:before{color:#3167f3}
.noti{border-radius:12px;gap:8px;padding:5px 12px;position:relative;background:linear-gradient(90deg,#fee9a5,#ffc814);display:inline-flex;align-items:center;line-height:1.4}
.noti:before{content:'';animation:gift-shake .5s ease-in-out infinite;background-image:url(/assets/images/gitf.svg);background-size:100% 100%;height:20px;left:14px;width:20px}
@keyframes gift-shake{0%{transform:rotate(0)}25%{transform:rotate(5deg)}50%{transform:rotate(-5deg)}75%{transform:rotate(5deg)}to{transform:rotate(0)}}
.reactions input[name="reaction"]:checked+img{box-shadow:0 0 12px #078aff;position:relative;top:-3px;transform:scale(1.2);transition:all 0.3s ease-in-out}
#details p:last-child, .server-detail p:last-child{margin-bottom:0}
.g-recaptcha{display:flex;justify-content:center;}
.modal-header{background:none;}
.modal-header .modal-title{color:#000;margin-left:auto;margin-right:auto;}
.recent-activity-list {
  margin-block-end: 0;
  position: relative;
}
.recent-activity-list:before {
  position: absolute;
  content: "";
  width: 1px;
  background-color: #ecf3fb;
  height: 100%;
  inset-inline-start: 50px;
  inset-block-start: 12px;
}
.recent-activity-list li {
  position: relative;
  margin-block-end: 1.1rem;
  padding-inline-start: 4rem;
}
.recent-activity-list li .activity-time {
  position: absolute;
  inset-inline-start: 0;
  inset-block-start: 2px;
}
.recent-activity-list li:nth-child(5n):before {
    background-color: rgba(92, 103, 247, 1);
    box-shadow: 0px 0px 0px 4px rgba(92, 103, 247, 0.15);
}
.recent-activity-list li:nth-child(5n+1):before {
    background-color: rgba(227, 84, 212, 1);
    box-shadow: 0px 0px 0px 4px rgba(227, 84, 212, 0.15);
}
.recent-activity-list li:nth-child(5n+2):before {
    background-color: rgba(255, 93, 159, 1);
    box-shadow: 0px 0px 0px 4px rgba(255, 93, 159, 0.15);
}
.recent-activity-list li:nth-child(5n+3):before {
    background-color: rgba(255, 142, 111, 1);
    box-shadow: 0px 0px 0px 4px rgba(255, 142, 111, 0.15);
}
.recent-activity-list li:nth-child(5n+4):before {
    background-color: rgba(158, 92, 247, 1);
    box-shadow: 0px 0px 0px 4px rgba(158, 92, 247, 0.15);
}
.recent-activity-list li:nth-child(5n+5):before {
    background-color: rgba(255, 198, 88, 1);
    box-shadow: 0px 0px 0px 4px rgba(255, 198, 88, 0.15);
}
.recent-activity-list li:before {
  position: absolute;
  content: "";
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: #ecf3fb;
  inset-inline-start: 47px;
  inset-block-start: 7px;
}
.recent-activity-list li:last-child {
  margin-block-end: 0;
}
.form-control:disabled, .form-control:readonly {
    background:#e7e7e7;
}
.pulse[data-num="0"]{display:none!important}
@media (max-width: 768px) {
    table.table {
        white-space: nowrap;
    }
}
.btn-orange{background:#fd7906;color:#fff;border:none;}
.btn-orange:hover, .btn-orange:focus{background:#e7720b!important;color:#fff!important;}
.bg-orange{background:#fd7906;}
.bg-green {background:#00c951}
.bg-red {background:#fb2c36}
.text-orange{color:#fd7906;}
.text-green {color:#00c951}
.text-red {color:#fb2c36}