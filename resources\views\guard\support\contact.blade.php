@extends('guard.layouts.app')
@section('title', '<PERSON>ê<PERSON> hệ hỗ trợ')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Thông tin liên hệ hỗ trợ</h5>
                <p class="text-muted mb-0">Thông tin liên hệ được lấy từ cấu hình website</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Facebook Contact -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="bx bxl-facebook-circle text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">Facebook</h5>
                                @if(siteValue('facebook'))
                                    <p class="card-text text-muted mb-3"><PERSON><PERSON><PERSON> <PERSON>ệ qua Facebook</p>
                                    <a href="{{ siteValue('facebook') }}" target="_blank" class="btn btn-primary">
                                        <i class="bx bxl-facebook me-1"></i>
                                        Mở Facebook
                                    </a>
                                    <div class="mt-2">
                                        <small class="text-muted">{{ siteValue('facebook') }}</small>
                                    </div>
                                @else
                                    <p class="card-text text-muted">Chưa cấu hình Facebook</p>
                                    <button class="btn btn-outline-primary" disabled>
                                        <i class="bx bx-cog me-1"></i>
                                        Chưa có thông tin
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Zalo Contact -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="bx bx-message-dots text-success" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">Zalo</h5>
                                @if(siteValue('zalo'))
                                    <p class="card-text text-muted mb-3">Liên hệ qua Zalo</p>
                                    <a href="{{ siteValue('zalo') }}" target="_blank" class="btn btn-success">
                                        <i class="bx bx-message-dots me-1"></i>
                                        Mở Zalo
                                    </a>
                                    <div class="mt-2">
                                        <small class="text-muted">{{ siteValue('zalo') }}</small>
                                    </div>
                                @else
                                    <p class="card-text text-muted">Chưa cấu hình Zalo</p>
                                    <button class="btn btn-outline-success" disabled>
                                        <i class="bx bx-cog me-1"></i>
                                        Chưa có thông tin
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Telegram Contact -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="bx bxl-telegram text-info" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">Telegram</h5>
                                @if(siteValue('telegram'))
                                    <p class="card-text text-muted mb-3">Liên hệ qua Telegram</p>
                                    <a href="{{ siteValue('telegram') }}" target="_blank" class="btn btn-info">
                                        <i class="bx bxl-telegram me-1"></i>
                                        Mở Telegram
                                    </a>
                                    <div class="mt-2">
                                        <small class="text-muted">{{ siteValue('telegram') }}</small>
                                    </div>
                                @else
                                    <p class="card-text text-muted">Chưa cấu hình Telegram</p>
                                    <button class="btn btn-outline-info" disabled>
                                        <i class="bx bx-cog me-1"></i>
                                        Chưa có thông tin
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admin Info -->
                @if(siteValue('nameadmin') || siteValue('avatar_admin'))
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="bx bx-user-circle me-2"></i>
                                    Thông tin Admin
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center">
                                    @if(siteValue('avatar_admin'))
                                    <div class="col-md-2 text-center">
                                        <img src="{{ siteValue('avatar_admin') }}" alt="Admin Avatar"
                                             class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">
                                    </div>
                                    @endif
                                    <div class="col-md-10">
                                        @if(siteValue('nameadmin'))
                                        <h5 class="mb-2">{{ siteValue('nameadmin') }}</h5>
                                        @endif
                                        <p class="text-muted mb-0">
                                            <i class="bx bx-shield-check me-1"></i>
                                           Chuyên Gia Marketing
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card border-secondary">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bx bx-cog me-2"></i>
                                    Thao Tác Nhanh 
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-outline-success" onclick="copyContactInfo()">
                                        <i class="bx bx-copy me-1"></i>
                                        Sao Chép Thông Tin
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="refreshPage()">
                                        <i class="bx bx-refresh me-1"></i>
                                        Làm Mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyContactInfo() {
    let contactInfo = '';

    @if(siteValue('facebook'))
    contactInfo += 'Facebook: {{ siteValue('facebook') }}\n';
    @endif

    @if(siteValue('zalo'))
    contactInfo += 'Zalo: {{ siteValue('zalo') }}\n';
    @endif

    @if(siteValue('telegram'))
    contactInfo += 'Telegram: {{ siteValue('telegram') }}\n';
    @endif

    @if(siteValue('nameadmin'))
    contactInfo += 'Admin: {{ siteValue('nameadmin') }}\n';
    @endif
    
    if (contactInfo) {
        navigator.clipboard.writeText(contactInfo).then(function() {
            alert('Đã sao chép thông tin liên hệ!');
        }).catch(function(err) {
            console.error('Không thể sao chép: ', err);
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = contactInfo;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Đã sao chép thông tin liên hệ!');
        });
    } else {
        alert('Không có thông tin liên hệ để sao chép!');
    }
}

function refreshPage() {
    location.reload();
}
</script>
@endsection
