<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderRefunded extends Model
{
    use HasFactory;
    protected $table = 'orders_refunded';
    protected $fillable = [
        'user_id',
        'order_id',
        'order_code',
        'refund_amount',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }  
    public function orderRF()
    {
        return $this->belongsTo(Order::class, 'order_code');
    }  
}
