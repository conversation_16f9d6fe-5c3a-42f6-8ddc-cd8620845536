<?php $__env->startSection('title', 'Hỗ trợ Ticket'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center gap-3">
                    <h5 class="card-title">Hỗ trợ Ticket</h5>
                </div>
            </div>
            <div class="card-body pc-component">
                <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" href="#pills-home"
                            role="tab" aria-controls="pills-home" aria-selected="true">Danh sách
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" href="#pills-profile"
                            role="tab" aria-controls="pills-profile" aria-selected="false" tabindex="-1">Tạo mới
                        </a>
                    </li>
                </ul>
                <div class="tab-content" id="pills-tabContent">
                    <div class="tab-pane fade active show" id="pills-home" role="tabpanel"
                        aria-labelledby="pills-home-tab">
                        <form action="" class="mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <input type="text" class="form-control" name="search" id="search"
                                            placeholder="Tìm kiếm theo tiêu đề, nội dung, ID..." value="<?php echo e(request('search')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <select class="form-select" name="category" id="category">
                                            <option value="">Tất cả danh mục</option>
                                            <option value="don_hang" <?php echo e(request('category') == 'don_hang' ? 'selected' : ''); ?>>Đơn hàng</option>
                                            <option value="thanh_toan" <?php echo e(request('category') == 'thanh_toan' ? 'selected' : ''); ?>>Thanh toán</option>
                                            <option value="dich_vu" <?php echo e(request('category') == 'dich_vu' ? 'selected' : ''); ?>>Dịch vụ</option>
                                            <option value="webcon" <?php echo e(request('category') == 'webcon' ? 'selected' : ''); ?>>Webcon</option>
                                            <option value="khac" <?php echo e(request('category') == 'khac' ? 'selected' : ''); ?>>Khác</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <select class="form-select" name="status" id="status">
                                            <option value="">Tất cả trạng thái</option>
                                            <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                                            <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Đang xử lý</option>
                                            <option value="success" <?php echo e(request('status') == 'success' ? 'selected' : ''); ?>>Đã xử lý</option>
                                            <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="<?php echo e(route('ticket')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-refresh"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered nowrap dataTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Danh mục</th>
                                        <th>Tiêu đề</th>
                                        <th>Nội dung</th>
                                        <th>Nội dung phản hồi</th>
                                        <th>Trạng thái</th>
                                        <th>Thời gian tạo</th>
                                    </tr>
                                </thead>
                                <tbody class="fw-bold">
                                    <?php if($ticket->isEmpty()): ?>
                                    <?php echo $__env->make('admin.components.table-search-not-found', [
                                    'colspan' => 7,
                                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <?php else: ?>
                                    <?php $__currentLoopData = $ticket; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tickets): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($tickets->id); ?></td>
                                        <td><?php echo categoryTicket($tickets->category); ?></td>
                                        <td>
                                            <strong><?php echo e($tickets->title); ?></strong>
                                        </td>
                                        <td>
                                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                <?php echo Str::limit(strip_tags($tickets->body), 100); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <?php if($tickets->reply): ?>
                                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                    <?php echo Str::limit(strip_tags($tickets->reply), 100); ?>

                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Chưa có phản hồi</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo statusTicket($tickets->status); ?></td>
                                        <td>
                                            <small><?php echo e($tickets->created_at->format('d/m/Y H:i')); ?></small><br>
                                            <small class="text-muted"><?php echo e($tickets->created_at->diffForHumans()); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-center align-items-center">
                                <?php echo e($ticket->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                        <form action="<?php echo e(route('ticket.post')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="form-floating mb-3">
                                <select class="form-control" name="category" required>
                                    <option value="">Chọn mục hỗ trợ</option>
                                    <option value="don_hang" <?php echo e(old('category') == 'don_hang' ? 'selected' : ''); ?>>Đơn hàng</option>
                                    <option value="thanh_toan" <?php echo e(old('category') == 'thanh_toan' ? 'selected' : ''); ?>>Thanh toán</option>
                                    <option value="dich_vu" <?php echo e(old('category') == 'dich_vu' ? 'selected' : ''); ?>>Dịch vụ</option>
                                    <option value="webcon" <?php echo e(old('category') == 'webcon' ? 'selected' : ''); ?>>Webcon</option>
                                    <option value="khac" <?php echo e(old('category') == 'khac' ? 'selected' : ''); ?>>Khác</option>
                                </select>
                                <label for="category">Mục hỗ trợ</label>
                            </div>

                            <div class="form-floating mb-4">
                                <input type="text" class="form-control" name="title" placeholder="Tiêu đề" value="<?php echo e(old('title')); ?>" required>
                                <label for="title">Tiêu đề</label>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="body" name="body" placeholder="Nội dung" style="height: 150px" required><?php echo e(old('body')); ?></textarea>
                                <label for="body">Nội dung</label>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary col-12">
                                    <i class="fas fa-save"></i> Tạo ticket
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script src="/assets/js/plugins/tinymce/tinymce.min.js"></script>
<script>
    tinymce.init({
        height: '200',
        selector: '#body',
        content_style: 'body { font-family: "Inter", sans-serif; }',
        menubar: false,
        toolbar: [
            'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist'
        ],
        plugins: 'lists'
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/guard/ticket.blade.php ENDPATH**/ ?>