@extends('admin.layouts.app')
@section('title', 'Hỗ Trợ Ticket')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">Danh Sách Ticket Hỗ Trợ</h5>
                    <div class="d-flex gap-2">
                        <span class="badge bg-warning">Chờ xử lý: {{ $ticket->where('status', 'pending')->count() }}</span>
                        <span class="badge bg-info"><PERSON><PERSON> xử lý: {{ $ticket->where('status', 'processing')->count() }}</span>
                        <span class="badge bg-success">Đã xử lý: {{ $ticket->where('status', 'success')->count() }}</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <form action="" method="GET" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <input type="text" class="form-control" name="search"
                                    placeholder="Tìm kiếm theo tiêu đề, nội dung, ID..."
                                    value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <select class="form-select" name="category">
                                    <option value="">Tất cả danh mục</option>
                                    <option value="don_hang" {{ request('category') == 'don_hang' ? 'selected' : '' }}>Đơn hàng</option>
                                    <option value="thanh_toan" {{ request('category') == 'thanh_toan' ? 'selected' : '' }}>Thanh toán</option>
                                    <option value="dich_vu" {{ request('category') == 'dich_vu' ? 'selected' : '' }}>Dịch vụ</option>
                                    <option value="webcon" {{ request('category') == 'webcon' ? 'selected' : '' }}>Webcon</option>
                                    <option value="khac" {{ request('category') == 'khac' ? 'selected' : '' }}>Khác</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <select class="form-select" name="status">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ xử lý</option>
                                    <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Đang xử lý</option>
                                    <option value="success" {{ request('status') == 'success' ? 'selected' : '' }}>Đã xử lý</option>
                                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i> Tìm kiếm
                                </button>
                                <a href="{{ route('admin.ticket.ticket') }}" class="btn btn-secondary">
                                    <i class="ti ti-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table text-nowrap table-striped table-hover table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Thao Tác</th>
                                <th>Người tạo</th>
                                <th>Danh mục</th>
                                <th>Tiêu đề</th>
                                <th>Trạng thái</th>
                                <th>Thời gian tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($ticket as $tickets)
                            <tr>
                                <td>{{ $tickets->id }}</td>
                                <td>
                                    <a href="{{ route('admin.ticket.ticket.edit', ['id' => $tickets->id]) }}"
                                        class="btn btn-sm btn-success"
                                        data-bs-toggle="tooltip" title="Xem Chi Tiết">
                                        <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.ticket.ticket.delete', ['id' => $tickets->id]) }}"
                                        class="btn btn-sm btn-danger"
                                        data-bs-toggle="tooltip" title="Xóa"
                                        onclick="return confirm('Bạn có chắc chắn muốn xóa ticket này?')">
                                        <i class="ti ti-trash"></i>
                                    </a>
                                </td>
                                <td>
                                    <strong>{{ $tickets->user->name ?? 'N/A' }}</strong><br>
                                    <small class="text-muted">{{ $tickets->user->email ?? 'N/A' }}</small>
                                </td>
                                <td>{!! categoryTicket($tickets->category) !!}</td>
                                <td>
                                    <strong>{{ $tickets->title }}</strong><br>
                                    <small class="text-muted">
                                        {!! Str::limit(strip_tags($tickets->body), 80) !!}
                                    </small>
                                </td>
                                <td>{!! statusTicket($tickets->status) !!}</td>
                                <td>
                                    <small>{{ $tickets->created_at->format('d/m/Y H:i') }}</small><br>
                                    <small class="text-muted">{{ $tickets->created_at->diffForHumans() }}</small>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="ti ti-inbox fs-3"></i><br>
                                        Không có ticket nào
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($ticket->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $ticket->appends(request()->all())->links('pagination::bootstrap-4') }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    // Khởi tạo tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
</script>
@endsection
