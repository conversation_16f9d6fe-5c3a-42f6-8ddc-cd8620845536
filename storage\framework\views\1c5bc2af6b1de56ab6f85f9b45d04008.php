<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>p Tiền'); ?>
<?php $__env->startSection('content'); ?>
<div class="d-flex align-items-center mb-3">
    <div>
        <button type="button" class="btn btn-outline-primary recharge-btn mb-2 active" data-tab="bankings">
        <i class="fas fa-bank me-2"></i> <PERSON><PERSON>
        </button>
        <button type="button" class="btn btn-outline-primary recharge-btn mb-2" data-tab="cards">
        <i class="fad fa-credit-card me-2"></i> Thẻ Cào
        </button>
    </div>
</div>
<div class="row">
    <div class="col-md-12 recharge-tab" id="bankings">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title text-uppercase"><i class="fas fa-bank fs-5 me-2"></i>NGÂN HÀNG</h5>
            </div>
            <div class="card-body pt-0">
                <form action="" method="GET">
                    <div class="row justify-item-center mb-3">
                        <div class="col-md-2 col-3 mb-2">
                            <label class="form-label">Hiển Thị</label>
                            <select name="per_page" id="per_page" class="form-select form-control" onchange="this.form.submit()">
                                <option value="">--Hiển Thị --</option>
                                <?php $__currentLoopData = [10, 25, 50, 100, 1000, 5000, 10000]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($option); ?>" <?php echo e(request('per_page') == $option ? 'selected' : ''); ?>>
                                - <?php echo e($option); ?> -
                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 col-8 mb-2">
                            <label class="form-label">Từ Ngày</label>
                            <input type="date" name="start_date" class="form-control" value="<?php echo e(request('start_date')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Đến Ngày</label>
                            <input type="date" name="end_date" class="form-control" value="<?php echo e(request('end_date')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Người Dùng</label>
                            <input type="text" name="username" class="form-control" value="<?php echo e(request('username')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Cổng Nạp</label>
                            <input type="text" name="bank_name" class="form-control" value="<?php echo e(request('bank_name')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Giao Dịch</label>
                            <input type="text" name="bank_code" class="form-control" value="<?php echo e(request('bank_code')); ?>">
                        </div>
                        <div class="col-md-3 col-8 mb-2">
                            <label class="form-label">Tìm Kiếm</label>
                            <div class=" input-group">
                                <input type="text" name="search" class="form-control" value="<?php echo e(request('search')); ?>">
                                <button type="submit" class="btn btn-primary"><i class="fad fa-search me-2"></i></button>
                            </div>
                        </div>
                        <div class="col-md-3 col-3 mb-2">
                            <a href="<?php echo e(route('admin.history.payment')); ?>" class="btn btn-secondary mt-4">
                            <i class="fad fa-sync-alt me-1"></i> Làm Mới
                            </a>
                        </div>
                    </div>
                </form>
                <div class="table-responsive">
                    <table class="table table-vcenter table--default text-nowrap table-borderless table-mb w-100">
                        <thead class="text-uppercase">
                            <tr>
                                <th>#</th>
                                <th>Tài Khoản</th>
                                <th>Phương Thức</th>
                                <th>Mã Giao Dịch</th>
                                <th>Số Tiền Nạp</th>
                                <th>Thực Nhận</th>
                                <th>Nội Dung</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($payment->id); ?></td>
                                <td>
                                    <ul>
                                        <li><?php echo e($payment->user->name ?? "Null"); ?></li>
                                        <li>[<?php echo e($payment->user->id ?? 'Null'); ?>] <?php echo e($payment->user->username ?? 'Không Tìm Thấy Người Dùng'); ?></li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li><b>Mã GD: </b><?php echo e($payment->payment_method); ?></li>
                                        <li><b>Thời Gian: </b><?php echo e($payment->created_at); ?></li>
                                    </ul>
                                </td>
                                <td><?php echo e($payment->bank_code); ?></td>
                                <td><span class="text-primary"><?php echo e(number_format($payment->amount)); ?>đ</span></td>
                                <td><span class="text-danger"><?php echo e(number_format($payment->real_amount)); ?>đ</span></td>
                                <td>
                                    <textarea type="text" class="form-control" rows="3" style="min-width: 400px" readonly><?php echo e($payment->note); ?></textarea>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center align-items-center mt-3 pagination-style-1">
                        <?php echo e($payments->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12 recharge-tab" id="cards">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title text-uppercase"><i class="fad fa-credit-card fs-5 me-2"></i>THẺ CÀO</h5>
            </div>
            <div class="card-body pt-0">
                <form action="" method="GET">
                    <input type="hidden" name="tab" value="cards">
                    <div class="row justify-item-center mb-3">
                        <div class="col-md-2 col-2 mb-2">
                            <label class="form-label">Hiển Thị</label>
                            <select name="card_per_page" id="card_per_page" class="form-select" onchange="this.form.submit()">
                                <option value="">--Hiển Thị --</option>
                                <?php $__currentLoopData = [10, 25, 50, 100, 1000, 5000, 10000]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($option); ?>" <?php echo e(request('card_per_page') == $option ? 'selected' : ''); ?>>
                                - <?php echo e($option); ?> -
                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 col-9 mb-2">
                            <label class="form-label">Từ Ngày</label>
                            <input type="date" name="card_start_date" class="form-control" value="<?php echo e(request('card_start_date')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Đến Ngày</label>
                            <input type="date" name="card_end_date" class="form-control" value="<?php echo e(request('card_end_date')); ?>">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Tài Khoản</label>
                            <input type="text" name="card_username" class="form-control" value="<?php echo e(request('card_username')); ?>" placeholder="Tên Người Dùng...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Loại Thẻ</label>
                            <input type="text" name="card_type" class="form-control" value="<?php echo e(request('card_type')); ?>" placeholder="Loại Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Giao Dịch</label>
                            <input type="text" name="card_tranid" class="form-control" value="<?php echo e(request('card_tranid')); ?>" placeholder="Mã Giao Dịch...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Mã Thẻ</label>
                            <input type="text" name="card_code" class="form-control" value="<?php echo e(request('card_code')); ?>" placeholder="Mã Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Seri</label>
                            <input type="text" name="card_serial" class="form-control" value="<?php echo e(request('card_serial')); ?>" placeholder="Seri Thẻ...">
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Trạng Thái</label>
                            <select name="card_status" class="form-select">
                                <option value="">--Tất Cả --</option>
                                <option value="0" <?php echo e(request('card_status') == '0' ? 'selected' : ''); ?>>Đang Xử Lý</option>
                                <option value="1" <?php echo e(request('card_status') == '1' ? 'selected' : ''); ?>>Thành Công</option>
                                <option value="2" <?php echo e(request('card_status') == '2' ? 'selected' : ''); ?>>Thất Bại</option>
                                <option value="3" <?php echo e(request('card_status') == '3' ? 'selected' : ''); ?>>Sai Mệnh Giá</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <label class="form-label">Tìm Kiếm</label>
                            <div class="input-group">
                                <input type="text" name="card_search" class="form-control" value="<?php echo e(request('card_search')); ?>" placeholder="Tìm Kiếm...">
                                <button type="submit" class="btn btn-primary"><i class="fad fa-search me-2"></i></button>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <a href="<?php echo e(route('admin.history.payment')); ?>" class="btn btn-secondary mt-4">
                            <i class="fad fa-sync-alt me-1"></i> Làm Mới
                            </a>
                        </div>
                    </div>
                </form>
                <div class="table-responsive">
                    <table class="table table-vcenter table--default text-nowrap table-borderless table-mb w-100">
                        <thead class="text-uppercase">
                            <tr>
                                <th>#</th>
                                <th>Tài Khoản</th>
                                <th>Mã Giao Dịch</th>
                                <th>Loại</th>
                                <th>Mã Thẻ</th>
                                <th>Seri</th>
                                <th>Số Tiền</th>
                                <th>Thực Nhận</th>
                                <th>Trạng Thái</th>
                                <th>Nội Dung</th>
                                <th>Thời Gian</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($payment->id); ?></td>
                                <td>
                                    <ul>
                                        <li><?php echo e($payment->user->name  ?? "Null"); ?></li>
                                        <li>[<?php echo e($payment->user->id ?? 'Null'); ?>] <?php echo e($payment->user->username ?? 'Không Tìm Thấy Người Dùng'); ?></li>
                                    </ul>
                                </td>
                                <td><?php echo e($payment->tranid); ?></td>
                                <td><?php echo e($payment->card_type); ?></td>
                                <td><?php echo e($payment->card_code); ?></td>
                                <td><?php echo e($payment->card_serial); ?></td>
                                <td><?php echo e(number_format($payment->card_amount)); ?>đ</td>
                                <td><?php echo e(number_format($payment->card_real_amount)); ?>đ</td>
                                <td><?php echo statusCard($payment->status); ?></td>
                                <td>
                                    <textarea type="text" class="form-control" rows="2" style="min-width: 400px" readonly><?php echo e($payment->note); ?></textarea>
                                </td>
                                <td><?php echo e($payment->created_at); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center align-items-center mt-3 pagination-style-1">
                        <?php echo e($cards->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script>
    $(document).ready(function() {
        function showTab(tabId, button) {
            $('.recharge-tab').hide();
            $('#' + tabId).show();
            $('.recharge-btn').removeClass('active');
            $(button).addClass('active');
    
            localStorage.setItem('activeRechargeTab', tabId);
        }
        
        $('.recharge-btn').click(function() {
            const tabId = $(this).data('tab');
            showTab(tabId, this);
        });
    
        const savedTab = localStorage.getItem('activeRechargeTab') || $('.recharge-btn').first().data('tab');
        const savedButton = $('.recharge-btn[data-tab="' + savedTab + '"]');
        
        showTab(savedTab, savedButton);
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/history/recharges.blade.php ENDPATH**/ ?>