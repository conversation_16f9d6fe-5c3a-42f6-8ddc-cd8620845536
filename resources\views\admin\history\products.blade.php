@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON><PERSON> sử sản phẩm')
@section('content')
    <div class="card custom-card shadow">
        <div class="card-header">
            <h4 class="card-title"><PERSON><PERSON>ch sử sản phẩm</h4>
            <div class="ms-auto">
                <button class="btn btn-sm btn-danger-gradient" data-bs-toggle="modal" data-bs-target="#new">
                    <i class="ti ti-plus fw-bold"></i> Hoàn tiền
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table w-100" id="table-orders-product">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>
                                <input type="checkbox" id="check_all" name="checked_all" class="form-check-input">
                            </th>
                            <th><PERSON><PERSON> tác</th>
                            <th><PERSON><PERSON><PERSON> kho<PERSON>n</th>
                            <th><PERSON><PERSON> đơn</th>
                            <th><PERSON><PERSON><PERSON> phẩm</th>
                            <th><PERSON><PERSON> l<PERSON>ợng</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Thông tin</th>
                            <th>Dữ liệu</th>
                            <th>Thời gian</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal Hoàn tiền -->
    <div id="new" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="refundModalLabel">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="refundModalLabel">Hoàn tiền</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card-body mb-3">
                        <ul class="nav bordered-tab d-inline-flex nav-pills gap-2 mb-0" id="pills-tab-six" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="user-set-profile-tab" data-bs-toggle="pill" 
                                        data-bs-target="#user-set-profile" type="button" role="tab" 
                                        aria-controls="user-set-profile" aria-selected="true">
                                    <span class="f-w-500">Hoàn tiền một phần</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="user-set-account-tab" data-bs-toggle="pill" 
                                        data-bs-target="#user-set-account" type="button" role="tab" 
                                        aria-controls="user-set-account" aria-selected="false">
                                    <span class="f-w-500">Hoàn tiền toàn bộ</span>
                                </button>
                            </li>
                        </ul>
                    </div>
                   
                    <div class="tab-content" id="user-set-tabContent">
                        <!-- Tab Hoàn tiền một phần -->
                        <div class="tab-pane fade active show mb-3" id="user-set-profile" role="tabpanel" aria-labelledby="user-set-profile-tab">
                            <div class="card-header mb-3">
                                <h6 class="card-title">Hoàn tiền một phần</h6>
                            </div>
                            <div class="card-body">
                                <form id="partial-refund-form" action="{{ route('admin.history.products.delete.checked') }}" method="POST">
                                    @csrf
                                    <input type="text" class="form-control" name="checked_server" id="checked_server_partial" value="" hidden>
                                    <input type="text" name="refund_type" value="partial" hidden>
                                    
                                    <!-- Input cho số lượng cần hoàn -->
                                    <div class="form-floating mb-3">
                                        <input type="number" id="refund-quantity" name="refund_quantity" class="form-control" 
                                               placeholder="Số lượng cần hoàn" min="1" step="1" required>
                                        <label class="form-label" for="refund-quantity">Số lượng cần hoàn <span class="text-danger">*</span></label>
                                        <div class="invalid-feedback" id="quantity-error"></div>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <textarea id="refund-reason-partial" name="refund_reason" class="form-control" 
                                                  placeholder="Lý do hoàn tiền" required></textarea>
                                        <label class="form-label" for="refund-reason-partial">Lý do hoàn tiền <span class="text-danger">*</span></label>
                                        <div class="invalid-feedback" id="reason-partial-error"></div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" id="refund-total-partial" name="refund_total" class="form-control" value="0đ" readonly>
                                        <label class="form-label" for="refund-total-partial">Tổng số tiền hoàn</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <input type="text" id="account-balance-partial" name="account_count" class="form-control" readonly>
                                        <label class="form-label">Số lượng đơn hàng được chọn</label>
                                    </div>
                                    
                                    <div class="button-group">
                                        <button type="submit" class="btn btn-primary" id="confirm-partial-refund">
                                            <i class="fas fa-check"></i> Xác nhận hoàn tiền
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">
                                            <i class="fas fa-times"></i> Hủy
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Tab Hoàn tiền toàn bộ -->
                        <div class="tab-pane fade mb-3" id="user-set-account" role="tabpanel" aria-labelledby="user-set-account-tab">
                            <div class="card-header mb-3">
                                <h6 class="card-title">Hoàn tiền toàn bộ</h6>
                            </div>
                            <div class="card-body mb-3">
                                <form id="full-refund-form" action="{{ route('admin.history.products.delete.checked') }}" method="POST">
                                    @csrf
                                    <input type="text" class="form-control" name="checked_server" id="checked_server_full" value="" hidden>
                                    <input type="text" name="refund_type" value="full" hidden>
                                    
                                    <div class="form-floating mb-3">
                                        <textarea id="refund-reason-full" name="refund_reason" class="form-control" 
                                                  placeholder="Lý do hoàn tiền" required></textarea>
                                        <label class="form-label" for="refund-reason-full">Lý do hoàn tiền <span class="text-danger">*</span></label>
                                        <div class="invalid-feedback" id="reason-full-error"></div>
                                    </div>

                                    <div class="form-floating mb-3">
                                        <input type="text" id="refund-total-full" name="refund_total" class="form-control" value="0đ" readonly>
                                        <label class="form-label" for="refund-total-full">Tổng số tiền hoàn</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <input type="text" id="account-balance-full" name="account_count" class="form-control" readonly>
                                        <label class="form-label">Số lượng đơn hàng được chọn</label>
                                    </div>
                                    
                                    <div class="button-group">
                                        <button type="submit" class="btn btn-primary" id="confirm-full-refund">
                                            <i class="fas fa-check"></i> Xác nhận hoàn tiền
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">
                                            <i class="fas fa-times"></i> Hủy
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    // Khởi tạo biến global để lưu trữ dữ liệu đơn hàng
    window.ordersData = {};
    let isDataTableInitialized = false;

    // Khởi tạo DataTable
    initializeDataTable();

    // Đăng ký event handlers
    registerEventHandlers();

    function initializeDataTable() {
        console.log('Initializing DataTable for orders-product');

        // Thêm error handling
        $.ajaxSetup({
            error: function(xhr, status, error) {
                console.error('AJAX Error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                Swal.fire('Lỗi', 'Có lỗi xảy ra khi tải dữ liệu: ' + error, 'error');
            }
        });

        loadDatatable('#table-orders-product', 'orders-product', [
            {
                data: 'id',
                name: 'id',
            },
            {
                data: null,
                name: null,
                searchable: false,
                orderable: false,
                render: function(data) {
                    // Lưu trữ dữ liệu đơn hàng
                    window.ordersData[data.id] = {
                        id: data.id,
                        price: parseFloat(data.price) || 0,
                        rate: parseFloat(data.rate) || 0, 
                        payment: parseFloat(data.payment) || 0,
                        quantity: parseInt(data.quantity) || 0,
                        user: data.user,
                        product: data.product
                    };

                    return `<input class="form-check-input checkbox" type="checkbox" name="check_ids[]" value="${data.id}">`;
                }
            },
            {
                data: null,
                name: null,
                searchable: false,
                orderable: false,
                render: function(data) {
                    return `
                        <div class="btn-group" role="group">
                            <a href="/admin/history/products/detail/${data.id}" class="btn btn-sm btn-primary" title="Xem chi tiết">
                                <i class="bi bi-eye"></i>
                            </a>
                            <button class="btn btn-sm btn-danger" onclick="deleteData(${data.id}, 'orders-product')" title="Xóa">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    `;
                }
            },
            {
                data: 'user',
                name: 'user',
                render: function(data, type, row) {
                    return data && data.username ? data.username : 'N/A';
                }
            },
             {
                data: 'order_id',
                name: 'order_id',
                render: function(data, type, row) {
                    return data || ('ORD-' + String(row.id).padStart(6, '0'));
                }
            },
            {
                data: 'product',
                name: 'product',
                render: function(data, type, row) {
                    return data && data.name ? data.name : 'Sản phẩm đã bị xóa';
                }
            },
            {
                data: 'quantity',
                name: 'quantity',
                render: function(data) {
                    return parseInt(data) || 0;
                }
            },
            {
                data: null,
                name: null,
                orderable: false,
                searchable: false,
                width: "200px",
                render: function(data) {
                    const price = parseFloat(data.price) || 0;
                    const rate = parseFloat(data.rate) || 0;
                    const payment = parseFloat(data.payment) || 0;
                    const quantity = parseInt(data.quantity) || 0;
                    const profit = payment - (rate * quantity);

                    return `
                        <ul class="list-unstyled mb-0">
                            <li><small>Rate: ${formatCurrency(price)}</small></li>
                            <li><small>Thành tiền: ${formatCurrency(payment)}</small></li>
                            <li><small>Giá vốn:${formatCurrency(rate * quantity)}  - Lãi đơn hàng: ${formatCurrency(profit)}</small></li>
                        </ul>
                    `;
                }
            },
            {
                data: null,
                name: 'user',
                orderable: false,
                searchable: false,
                render: function(data) {
                    function getStatusBadge(status) {
                        switch(status) {
                            case 'pending':
                                return '<span class="badge bg-warning">Đang chờ</span>';
                            case 'success':
                                return '<span class="badge bg-success">Thành công</span>';
                            case 'Partial_Refunded':
                                return '<span class="badge bg-warning">Hoàn tiền một phần</span>';
                            case 'Refunded':
                                return '<span class="badge bg-danger">Hoàn tiền toàn bộ</span>';
                            default:
                                return '<span class="badge bg-danger">Thất bại</span>';
                        }
                    }

                    return `
                        <ul class="list-unstyled mb-0">
                            <li><small><strong>SĐT:</strong> ${data.phone || "Không có"}</small></li>
                            <li><small><strong>Email:</strong> ${data.email || "Không có"}</small></li>
                            <li><small><strong>Trạng thái:</strong> ${getStatusBadge(data.status)}</small></li>
                        </ul>
                    `;
                }
            },
            {
                data: 'data',
                name: 'data',
                render: function(data, type, row) {
                    // Kiểm tra xem user đã xóa dữ liệu chưa
                    const isDeleted = row.note && row.note.includes('Dữ liệu đã được xóa bởi user');

                    // Lấy dữ liệu gốc từ backup trong note (nếu có)
                    let adminData = data || '';
                    if (isDeleted && row.note) {
                        const match = row.note.match(/ADMIN_DATA_BACKUP: ([A-Za-z0-9+\/=]+)/);
                        if (match) {
                            try {
                                adminData = atob(match[1]); // base64 decode
                            } catch (e) {
                                adminData = data || '';
                            }
                        }
                    }

                    if (adminData.trim() === '') {
                        return `
                            <div class="text-center text-muted">
                                <i class="bi bi-info-circle"></i>
                                <small>Không có dữ liệu</small>
                            </div>
                        `;
                    }

                    let statusBadge = '';
                    if (isDeleted) {
                        statusBadge = `<span class="badge bg-warning mb-2">
                            <i class="bi bi-exclamation-triangle"></i> User đã xóa
                        </span><br>`;
                    } else {
                        statusBadge = `<span class="badge bg-success mb-2">
                            <i class="bi bi-check-circle"></i> Dữ liệu gốc
                        </span><br>`;
                    }

                    return `
                        <div>
                            ${statusBadge}
                            <textarea class="form-control" rows="2" readonly>${adminData}</textarea>
                        </div>
                    `;
                }
            },
            {
                data: 'created_at',
                name: 'created_at',
                render: function(data) {
                    return formatDate(data);
                }
            }
        ]);

        // Đánh dấu DataTable đã được khởi tạo
        isDataTableInitialized = true;
    }

    function registerEventHandlers() {
        // Event handler cho checkbox "Chọn tất cả"
        $(document).off('change', '#check_all').on('change', '#check_all', function() {
            const isChecked = $(this).is(':checked');
            $("input[name='check_ids[]']").prop('checked', isChecked);
            updateCheckedServer();
            calculateRefundAmount();
        });

        // Event handler cho các checkbox đơn lẻ
        $(document).off('change', "input[name='check_ids[]']").on('change', "input[name='check_ids[]']", function() {
            updateCheckedServer();
            updateCheckAllState();
            calculateRefundAmount();
        });

        // Event handler cho input số lượng hoàn tiền - SỬA LẠI PHẦN NÀY
        $(document).off('input keyup change', '#refund-quantity').on('input keyup change', '#refund-quantity', function() {
            console.log('Quantity changed:', $(this).val()); // Debug log
            validateRefundQuantity();
            calculateRefundAmount();
        });

        // Event handler khi chuyển tab
        $('button[data-bs-toggle="pill"]').off('shown.bs.tab').on('shown.bs.tab', function(e) {
            setTimeout(function() {
                calculateRefundAmount();
            }, 100);
        });

        // Event handlers cho form submit
        $('#partial-refund-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            if (validatePartialRefundForm()) {
                submitRefundForm(this, 'partial');
            }
        });

        $('#full-refund-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            if (validateFullRefundForm()) {
                submitRefundForm(this, 'full');
            }
        });
    }

    function updateCheckedServer() {
        const checkedIds = [];
        $("input[name='check_ids[]']:checked").each(function() {
            checkedIds.push($(this).val());
        });

        // Cập nhật hidden inputs
        $('#checked_server_partial').val(checkedIds.join(','));
        $('#checked_server_full').val(checkedIds.join(','));
        
        // Cập nhật số lượng đơn hàng được chọn
        $('#account-balance-partial').val(checkedIds.length + ' đơn hàng');
        $('#account-balance-full').val(checkedIds.length + ' đơn hàng');
    }

    function updateCheckAllState() {
        const totalCheckboxes = $("input[name='check_ids[]']").length;
        const checkedCheckboxes = $("input[name='check_ids[]']:checked").length;
        $('#check_all').prop('checked', totalCheckboxes === checkedCheckboxes && totalCheckboxes > 0);
    }

    function validateRefundQuantity() {
        const quantity = parseInt($('#refund-quantity').val()) || 0;
        const $input = $('#refund-quantity');
        const $error = $('#quantity-error');

        if (quantity <= 0) {
            $input.addClass('is-invalid');
            $error.text('Số lượng phải lớn hơn 0');
            return false;
        }

        $input.removeClass('is-invalid');
        $error.text('');
        return true;
    }

    function validatePartialRefundForm() {
        let isValid = true;

        // Kiểm tra có đơn hàng được chọn không
        if ($("input[name='check_ids[]']:checked").length === 0) {
            showAlert('Vui lòng chọn ít nhất một đơn hàng để hoàn tiền!', 'warning');
            return false;
        }

        // Kiểm tra số lượng hoàn tiền
        if (!validateRefundQuantity()) {
            isValid = false;
        }

        // Kiểm tra lý do hoàn tiền
        const reason = $('#refund-reason-partial').val().trim();
        if (!reason) {
            $('#refund-reason-partial').addClass('is-invalid');
            $('#reason-partial-error').text('Vui lòng nhập lý do hoàn tiền');
            isValid = false;
        } else {
            $('#refund-reason-partial').removeClass('is-invalid');
            $('#reason-partial-error').text('');
        }

        return isValid;
    }

    function validateFullRefundForm() {
        // Kiểm tra có đơn hàng được chọn không
        if ($("input[name='check_ids[]']:checked").length === 0) {
            showAlert('Vui lòng chọn ít nhất một đơn hàng để hoàn tiền!', 'warning');
            return false;
        }

        // Kiểm tra lý do hoàn tiền
        const reason = $('#refund-reason-full').val().trim();
        if (!reason) {
            $('#refund-reason-full').addClass('is-invalid');
            $('#reason-full-error').text('Vui lòng nhập lý do hoàn tiền');
            return false;
        } else {
            $('#refund-reason-full').removeClass('is-invalid');
            $('#reason-full-error').text('');
        }

        return true;
    }

    // SỬA LẠI HÀM calculateRefundAmount
    function calculateRefundAmount() {
        console.log('calculateRefundAmount called'); // Debug log
        console.log('isDataTableInitialized:', isDataTableInitialized); // Debug log
        console.log('ordersData:', window.ordersData); // Debug log

        if (!isDataTableInitialized || !window.ordersData) {
            console.log('DataTable not initialized or no orders data'); // Debug log
            return;
        }

        const checkedIds = [];
        $("input[name='check_ids[]']:checked").each(function() {
            checkedIds.push($(this).val());
        });

        console.log('Checked IDs:', checkedIds); // Debug log

        if (checkedIds.length === 0) {
            // Reset các giá trị khi không có đơn hàng nào được chọn
            $('#refund-total-partial').val('0đ');
            $('#refund-total-full').val('0đ');
            console.log('No orders selected, reset amounts'); // Debug log
            return;
        }

        let totalRefundAmount = 0;
        
        // Kiểm tra tab hiện tại
        const isPartialRefund = $('#user-set-profile').hasClass('active');
        console.log('Is partial refund:', isPartialRefund); // Debug log
        
        if (isPartialRefund) {
            // Hoàn tiền một phần
            const refundQuantity = parseInt($('#refund-quantity').val()) || 0;
            console.log('Refund quantity:', refundQuantity); // Debug log
            
            if (refundQuantity > 0) {
                checkedIds.forEach(function(id) {
                    if (window.ordersData[id]) {
                        const order = window.ordersData[id];
                        const refundAmountForOrder = order.price * refundQuantity;
                        totalRefundAmount += refundAmountForOrder;
                        console.log(`Order ${id}: price=${order.price}, quantity=${refundQuantity}, amount=${refundAmountForOrder}`); // Debug log
                    }
                });
            }
            
            console.log('Total partial refund amount:', totalRefundAmount); // Debug log
            $('#refund-total-partial').val(formatCurrency(totalRefundAmount));
        } else {
            // Hoàn tiền toàn bộ
            checkedIds.forEach(function(id) {
                if (window.ordersData[id]) {
                    const order = window.ordersData[id];
                    totalRefundAmount += order.payment;
                    console.log(`Order ${id}: payment=${order.payment}`); // Debug log
                }
            });
            
            console.log('Total full refund amount:', totalRefundAmount); // Debug log
            $('#refund-total-full').val(formatCurrency(totalRefundAmount));
        }
    }

    function submitRefundForm(form, type) {
        const $form = $(form);
        const $submitBtn = $form.find('button[type="submit"]');
        
        // Disable submit button để tránh double submit
        $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');

        // Thực hiện AJAX submit
        $.ajax({
            url: $form.attr('action'),
            method: 'POST',
            data: $form.serialize(),
            success: function(response) {
                if (response.success) {
                    showAlert('Hoàn tiền thành công!', 'success');
                    $('#new').modal('hide');
                    // Reload DataTable
                    $('#table-orders-product').DataTable().ajax.reload();
                    // Reset form
                    resetRefundForm();
                } else {
                    showAlert(response.message || 'Có lỗi xảy ra khi hoàn tiền!', 'error');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Có lỗi xảy ra khi hoàn tiền!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showAlert(errorMessage, 'error');
            },
            complete: function() {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).html('<i class="fas fa-check"></i> Xác nhận hoàn tiền');
            }
        });
    }

    function resetRefundForm() {
        // Reset tất cả checkbox
        $("input[name='check_ids[]']").prop('checked', false);
        $('#check_all').prop('checked', false);
        
        // Reset form inputs
        $('#refund-quantity').val('');
        $('#refund-reason-partial').val('');
        $('#refund-reason-full').val('');
        $('#refund-total-partial').val('0đ');
        $('#refund-total-full').val('0đ');
        $('#account-balance-partial').val('0 đơn hàng');
        $('#account-balance-full').val('0 đơn hàng');
        
        // Reset validation states
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
        
        // Reset to first tab
        $('#user-set-profile-tab').click();
    }

    function formatCurrency(amount) {
        if (isNaN(amount) || amount === null || amount === undefined) {
            return '0đ';
        }
        return new Intl.NumberFormat('vi-VN').format(amount) + 'đ';
    }

    function showAlert(message, type = 'info') {
        // Sử dụng SweetAlert2 nếu có, hoặc alert thông thường
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: type === 'success' ? 'Thành công!' : (type === 'error' ? 'Lỗi!' : 'Thông báo!'),
                text: message,
                icon: type,
                confirmButtonText: 'OK'
            });
        } else {
            alert(message);
        }
    }

    // Reset form khi modal được đóng
    $('#new').on('hidden.bs.modal', function() {
        resetRefundForm();
    });

    // Event handler khi modal được mở
    $('#new').on('shown.bs.modal', function() {
        // Tính toán lại số tiền khi modal được mở
        setTimeout(function() {
            calculateRefundAmount();
        }, 300);
    });
});
</script>
@endsection