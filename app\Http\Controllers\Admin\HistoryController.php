<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderProduct;
use App\Models\ProducCategories;
use App\Models\User;
use App\Models\Card;
use App\Models\Recharge;
use App\Models\Smm;
use App\Models\PartnerWebsite;
use App\Models\Transaction;

use App\Http\Controllers\Api\Service\SmmController;
use App\Library\TelegramSdk;
use App\Library\DiscordSdk;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HistoryController extends Controller
{
    /**
     * Xóa đơn hàng
     * 
     * @param int $id Mã đơn hàng
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteOrder($id)
    {
        try {
            // Lấy domain hiện tại
            $domain = getDomain();

            // Tìm đơn hàng theo ID và domain
            $order = Order::where('domain', $domain)->find($id);

            // Kiểm tra xem đơn hàng có tồn tại không
            if (!$order) {
                return redirect()->back()->with('error', 'Không tìm thấy đơn hàng với ID: ' . $id);
            }

            // Kiểm tra trạng thái đơn hàng trước khi xóa
            $restrictedStatuses = [
                'Completed',
                'Refunded',
                'Cancelled',
                'Success'
            ];

            if (in_array($order->status, $restrictedStatuses)) {
                return redirect()->back()->with('error', 'Không thể xóa đơn hàng đã hoàn thành, đã hoàn tiền hoặc đã hủy');
            }

            // Lưu thông tin đơn hàng trước khi xóa (để gửi Discord notification)
            $orderCode = $order->order_code;
            $userId = $order->user_id;
            $orderDomain = $order->domain;

            // Xóa các bản ghi liên quan (nếu cần)
            // Ví dụ: xóa các sản phẩm trong đơn hàng nếu có
            // If there are order products linked to the user and domain, delete those
            OrderProduct::where('user_id', $userId)
                       ->where('domain', $orderDomain)
                       ->delete();

            // Xóa đơn hàng
            $order->delete();

            // Gửi thông báo qua Discord
            if (site("discord_webhook_url")) {
                $discord_notify = new DiscordSdk();
                $discord_notify->botNotify()->sendMessage([
                    "text" => "Đơn hàng **#{$orderCode}** đã được xóa bởi admin",
                ]);
            }
            
            // Chuyển hướng và thông báo thành công
            return redirect()->back()->with('success', 'Xóa đơn hàng thành công');
        } catch (\Exception $e) {
            // Ghi log lỗi
            \Log::error('Lỗi xóa đơn hàng: ' . $e->getMessage());

            // Xử lý lỗi
            return redirect()->back()->with('error', 'Không thể xóa đơn hàng: ' . $e->getMessage());
        }
    }
    public function sendOrder($id)
    {
        try {
            $domain = getDomain();
            $order = Order::where('domain', $domain)->find($id);

            if (!$order) {
                return redirect()->back()->with('error', 'Không tìm thấy đơn hàng với ID: ' . $id);
            }
            if ($order->status !== "Pending_Balance") {
                return redirect()->back()->with('error', 'Đơn hàng này đã được gửi từ trước');
            }
            $path = $order['orderProviderName'];
            $smms = Smm::where('name',$path)->where('domain', env('APP_MAIN_SITE'))->first();
            
            $orderDta = json_decode($order->order_data);
            $curlSMM = new SmmController;
            $post = array(
                'key' => $smms['token'],
                'action' => 'add',
                'service' => $order->orderProviderServer,
                'link' => $orderDta->object_id,
                'minutes' => $orderDta->minutes,
                'quantity' => $orderDta->quantity,
                'comments' => $orderDta->comments,
                'reaction' => strtolower($orderDta->reaction) ?? 'like'
            );
            $result = curl_smm($path, $post);
            if (isset($result['order']) && !empty($result['order'])) {
                $order->order_id = $result['order'];
                $order->status = "Processing";
                $order->save();
                return redirect()->back()->with('success', 'Gửi đơn hàng thành công');
            } else { 
                return redirect()->back()->with('error', $result['error'] ?? $result['message']);
            }
             
            
        } catch (\Exception $e) {
            // Ghi log lỗi
            \Log::error('Lỗi xóa đơn hàng: ' . $e->getMessage());

            // Xử lý lỗi
            return redirect()->back()->with('error', 'Không thể gửi đơn hàng: ' . $e->getMessage());
        }
    }
    public function deleteProductChecked(Request $request)
    {
        $request->validate([
            'checked_server' => 'required|string',
            'refund_type' => 'required|in:partial,full',
            'refund_reason' => 'required|string|max:500',
            'refund_quantity' => 'nullable|integer|min:1',
        ]);

        // Parse order IDs
        $idsString = $request->input("checked_server");
        $ids = array_filter(explode(",", $idsString));
        
        if (empty($ids)) {
            return response()->json([
                'success' => false,
                'message' => 'Không có đơn hàng nào được chọn để hoàn tiền'
            ], 400);
        }

        $refundType = $request->input('refund_type');
        $refundReason = $request->input('refund_reason');
        $refundQuantity = $request->input('refund_quantity');

        DB::beginTransaction();

        $totalRefundAmount = 0;
        $refundedOrders = [];
        $failedOrders = [];

        foreach ($ids as $orderId) {
            
                // Lấy thông tin đơn hàng
                $order = DB::table('order_products')->where('id', $orderId)->first();
                
                if (!$order) {
                    $failedOrders[] = [
                        'id' => $orderId,
                        'reason' => 'Không tìm thấy đơn hàng'
                    ];
                    continue;
                }

                // Kiểm tra trạng thái đơn hàng (chỉ hoàn tiền đơn hàng thành công)
                if ($order->status !== 'success') {
                    $failedOrders[] = [
                        'id' => $orderId,
                        'reason' => 'Đơn hàng chưa thành công, không thể hoàn tiền'
                    ];
                    continue;
                }

                // Kiểm tra xem đã hoàn tiền chưa
                $existingRefund = DB::table('order_products')->where('id', $orderId)->where('status','Refunded')->first();
                if ($existingRefund) {
                    $failedOrders[] = [
                        'id' => $orderId,
                        'reason' => 'Đơn hàng đã được hoàn tiền trước đó'
                    ];
                    continue;
                }

                $refundAmount = 0;
                $refundedQuantity = 0;

                if ($refundType === 'full') {
                    // Hoàn tiền toàn bộ
                    $refundAmount = floatval($order->payment);
                    $refundedQuantity = intval($order->quantity);
                } else {
                    // Hoàn tiền một phần
                    if (!$refundQuantity || $refundQuantity <= 0) {
                        $failedOrders[] = [
                            'id' => $orderId,
                            'reason' => 'Số lượng hoàn tiền không hợp lệ'
                        ];
                        continue;
                    }

                    if ($refundQuantity > $order->quantity) {
                        $failedOrders[] = [
                            'id' => $orderId,
                            'reason' => 'Số lượng hoàn tiền không được vượt quá số lượng đã mua (' . $order->quantity . ')'
                        ];
                        continue;
                    }

                    $refundAmount = floatval($order->price) * $refundQuantity;
                    $refundedQuantity = $refundQuantity;
                }

                // Lấy thông tin user để hoàn tiền
                $user = DB::table('users')->where('id', $order->user_id)->first();
                if (!$user) {
                    $failedOrders[] = [
                        'id' => $orderId,
                        'reason' => 'Không tìm thấy thông tin người dùng'
                    ];
                    continue;
                }
                DB::table('transactions')->insert([
                    'user_id' => $order->user_id,
                    'type' => 'refund',
                    'tran_code' => rand(10000000,99999999),
                    'first_balance' => $refundAmount,
                    'before_balance' => $user->balance,
                    'after_balance' => $user->balance + $refundAmount,
                    'note' =>  $refundReason, 
                    'created_at' => now(),
                    'updated_at' => now(),
                    'domain'    => $request->getHost()
                ]);

                // Thực hiện hoàn tiền vào tài khoản user
                DB::table('users')
                    ->where('id', $order->user_id)
                    ->increment('balance', $refundAmount);
 
                // Cập nhật trạng thái đơn hàng
                $newStatus = ($refundType === 'full') ? 'Refunded' : 'Partial_Refunded';
                DB::table('order_products')
                    ->where('id', $orderId)
                    ->update([
                        'status' => $newStatus,
                        'refund_status' => 'success',
                        'updated_at' => now()
                    ]);

                // Nếu hoàn tiền một phần, cập nhật số lượng còn lại
                if ($refundType === 'partial') {
                    $remainingQuantity = $order->quantity - $refundQuantity;
                    $remainingAmount = $order->payment - $refundAmount;
                    
                    DB::table('order_products')
                        ->where('id', $orderId)
                        ->update([
                            'quantity' => $remainingQuantity,
                            'payment' => $remainingAmount,
                            'updated_at' => now()
                        ]);
                }

                $totalRefundAmount += $refundAmount;
                $refundedOrders[] = [
                    'id' => $orderId,
                    'amount' => $refundAmount,
                    'quantity' => $refundedQuantity,
                    'user' => $user->id 
                ];

                 

            
        }

        DB::commit();

        // Tạo response message
        $status_ssssss= false;
        $message = "";
        if (!empty($refundedOrders)) {
            $message .= "Hoàn tiền thành công " . count($refundedOrders) . " đơn hàng. ";
            $message .= "Tổng số tiền hoàn: " . number_format($totalRefundAmount, 0, ',', '.') . "đ. ";
            $status_ssssss= true;
        }

        if (!empty($failedOrders)) {
            $message .= "Có " . count($failedOrders) . " đơn hàng không thể hoàn tiền. ";
            $status_ssssss= false;
        }
 
        return response()->json([
            'success' => $status_ssssss,
            'message' => $message,
            'data' => [
                'refunded_orders' => $refundedOrders,
                'failed_orders' => $failedOrders,
                'total_refund_amount' => $totalRefundAmount,
                'total_orders_processed' => count($ids)
            ]
        ]);
    }
    public function products(){
        return view('admin.history.products');
    }

    public function productDetail($id){

        $order = OrderProduct::where('id', $id)->where('domain', request()->getHost())->first();

        if(!$order){
            return redirect()->route('admin.history.products')->with('error', 'Đơn hàng sản phẩm không tồn tại');
        }

        return view('admin.history.product-detail', compact('order'));
    }

    public function updateOrder(Request $request, $id){
        $order = OrderProduct::where('id', $id)->where('domain', request()->getHost())->first();

        if(!$order){
            return redirect()->route('admin.history.products')->with('error', 'Đơn hàng sản phẩm không tồn tại');
        }

        $oldStatus = $order->status;

        $order->update([
            'status' => $request->status,
            'note' => $request->note,
            'data' => $request->data,
        ]);

        // Gửi email thông báo khi đơn hàng chuyển sang trạng thái "Completed"
        if ($oldStatus !== 'Completed' && $request->status === 'Completed') {
            event(new \App\Events\ProductDelivered($order));
        }

        return redirect()->route('admin.history.products.detail', $id)->with('success', 'Cập nhật đơn hàng sản phẩm thành công');
    }

    public function viewHistoryOrders(Request $request)
    {
        $search = $request->get("search");
        $status = $request->get("status");
        $orderType = $request->get("order_type", 'normal');
        $soluong = $request->get('per_page', 10);
        $orderProviderName = $request->get("orderProviderName");
    
        $domain = $request->getHost();
    
        if (getDomain() != env("APP_MAIN_SITE")) {
            $w = PartnerWebsite::where("name", $domain)->first();
            $orderProviderName = $w ? $w->domain : null;
        }
    
        $ordersQuery = Order::where("domain", $domain);
    
        if ($orderProviderName) {
            $ordersQuery->where("orderProviderName", $orderProviderName);
        }
    
        if ($search) {
            $searchTerms = array_filter(array_map("trim", explode(PHP_EOL, $search)));
            $ordersQuery->where(function ($query) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $query
                        ->orWhere("id", "like", "%" . $term . "%")
                        ->orWhere("order_code", "like", "%" . $term . "%")
                        ->orWhere("order_id", "like", "%" . $term . "%")
                        ->orWhere("object_id", "like", "%" . $term . "%")
                        ->orWhereHas("user", fn($query) => $query->where("username", "like", "%" . $term . "%"))
                        ->orWhereHas("server", fn($query) => $query->where("name", "like", "%" . $term . "%"))
                        ->orWhereHas("service", fn($query) => $query->where("name", "like", "%" . $term . "%"));
                }
            });
        }
    
        if ($status) {
            $ordersQuery->where("status", $status);
        }
    
        if ($orderType === 'dontay') {
            $ordersQuery->where("orderProviderName", "dontay");
        } elseif ($orderType === 'refund') {
            $ordersQuery->where(function ($query) {
                $query->where("status", "PendingRefundPartial")
                    ->orWhere("status", "PendingRefundCancel");
            });
        }
    
        $providers = Order::where("domain", $domain)->groupBy("orderProviderName")->pluck("orderProviderName")->toArray();
    
        // Lấy danh sách trạng thái
        $statuses = [
            'Running', 'Processing', 'Holding', 'Completed', 'Cancelled', 'Refunded', 'Failed', 'Pending',
            'PendingRefundCancel', 'PendingRefundPartial', 'Partially Refunded', 'Partial', 'Partially Completed',
            'WaitingForRefund', 'Expired', 'Success', 'Active',
        ];
    
        // Đếm số lượng đơn theo từng trạng thái
        $countStatusOrder = [];
        foreach ($statuses as $status) {
            $countStatusOrder[$status] = Order::where('domain', $domain)->where('status', $status)->count();
        }
    
        // Số lượng hiển thị
        if ($soluong === 'all') {
            $orders = $ordersQuery->orderBy("id", "desc")->get(); // Lấy tất cả đơn hàng
        } else {
            $orders = $ordersQuery->orderBy("id", "desc")->paginate($soluong);
        }

        return view( "admin.history.orders", compact("orders", "soluong", "providers", "statuses", "countStatusOrder"));
    }
    
    public function viewUserHistory(Request $request)
    {
        $search = $request->get("search");
        $action = $request->get("type");
        $perPage = $request->get("per_page", 10);
        $startDate = $request->get("start_date");
        $endDate = $request->get("end_date");
        $username = $request->get("username");
        $tranCode = $request->get("tran_code");
    
        $transactions = Transaction::where("domain", getDomain())
            ->when($search, function ($query, $search) {
                return $query->where("note", "like", "%" . $search . "%");
            })
            ->when($action, function ($query, $action) {
                return $query->where("type", $action);
            })
            ->when($startDate, function ($query, $startDate) {
                return $query->whereDate("created_at", ">=", $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->whereDate("created_at", "<=", $endDate);
            })
            ->when($username, function ($query, $username) {
                return $query->whereHas("user", function ($q) use ($username) {
                    $q->where("username", "like", "%" . $username . "%");
                });
            })
            ->when($tranCode, function ($query, $tranCode) {
                return $query->where("tran_code", "like", "%" . $tranCode . "%");
            })
            ->orderBy("id", "desc")
            ->paginate($perPage);
    
        return view("admin.history.transactions", compact("transactions", "perPage"));
    }  

    public function viewHistoryPayment(Request $request)
    {
        $search = $request->get("search");
        $perPage = $request->get("per_page", 10); // Số bản ghi mỗi trang, mặc định là 10
        $startDate = $request->get("start_date");
        $endDate = $request->get("end_date");
        $username = $request->get("username");
        $bankName = $request->get("bank_name");
        $bankCode = $request->get("bank_code");
    
        // Truy vấn payments có tìm kiếm
        $payments = Recharge::where("domain", $request->getHost())
            ->when($search, function ($query) use ($search) {
                return $query->where(function ($query) use ($search) {
                    $query->where("id", "like", "%$search%")
                        ->orWhereHas("user", function ($query) use ($search) {
                            $query->where("username", "like", "%$search%");
                        })
                        ->orWhere("bank_code", "like", "%$search%")
                        ->orWhere("bank_name", "like", "%$search%")
                        ->orWhere("amount", "like", "%$search%")
                        ->orWhere("note", "like", "%$search%");
                });
            })
            ->when($username, function ($query) use ($username) {
                return $query->whereHas("user", function ($query) use ($username) {
                    $query->where("username", "like", "%$username%");
                });
            })
            ->when($bankName, function ($query) use ($bankName) {
                return $query->where("bank_name", "like", "%$bankName%");
            })
            ->when($bankCode, function ($query) use ($bankCode) {
                return $query->where("bank_code", "like", "%$bankCode%");
            })
            ->when($startDate, function ($query) use ($startDate) {
                return $query->whereDate("created_at", ">=", $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->whereDate("created_at", "<=", $endDate);
            })
            ->orderBy("id", "desc")
            ->paginate($perPage);
    
        // Lấy các tham số tìm kiếm cho thẻ cào
        $cardSearch = $request->get("card_search");
        $cardPerPage = $request->get("card_per_page", 10);
        $cardStartDate = $request->get("card_start_date");
        $cardEndDate = $request->get("card_end_date");
        $cardUsername = $request->get("card_username");
        $cardType = $request->get("card_type");
        $cardTranid = $request->get("card_tranid");
        $cardCode = $request->get("card_code");
        $cardSerial = $request->get("card_serial");
        $cardStatus = $request->get("card_status");
    
        $cards = Card::where("domain", $request->getHost())
            ->when($cardSearch, function ($query) use ($cardSearch) {
                return $query->where(function ($query) use ($cardSearch) {
                    $query->where("id", "like", "%$cardSearch%")
                        ->orWhere("username", "like", "%$cardSearch%")
                        ->orWhere("tranid", "like", "%$cardSearch%")
                        ->orWhere("card_type", "like", "%$cardSearch%")
                        ->orWhere("card_code", "like", "%$cardSearch%")
                        ->orWhere("card_serial", "like", "%$cardSearch%")
                        ->orWhere("note", "like", "%$cardSearch%");
                });
            })
            ->when($cardUsername, function ($query) use ($cardUsername) {
                return $query->where("username", "like", "%$cardUsername%");
            })
            ->when($cardType, function ($query) use ($cardType) {
                return $query->where("card_type", "like", "%$cardType%");
            })
            ->when($cardTranid, function ($query) use ($cardTranid) {
                return $query->where("tranid", "like", "%$cardTranid%");
            })
            ->when($cardCode, function ($query) use ($cardCode) {
                return $query->where("card_code", "like", "%$cardCode%");
            })
            ->when($cardSerial, function ($query) use ($cardSerial) {
                return $query->where("card_serial", "like", "%$cardSerial%");
            })
            ->when($cardStatus !== null && $cardStatus !== '', function ($query) use ($cardStatus) {
                return $query->where("status", $cardStatus);
            })
            ->when($cardStartDate, function ($query) use ($cardStartDate) {
                return $query->whereDate("created_at", ">=", $cardStartDate);
            })
            ->when($cardEndDate, function ($query) use ($cardEndDate) {
                return $query->whereDate("created_at", "<=", $cardEndDate);
            })
            ->orderBy("id", "desc")
            ->paginate($cardPerPage);

        return view("admin.history.recharges", compact("payments", "cards"));
    }

    public function actionOrder(Request $request, $id) {
        if ($request->action !== 'edit') {
            return redirect()->back()->with('error', 'Hành động không hợp lệ');
        }
    
        $order = Order::where("domain", getDomain())->find($id);
    
        if (!$order) {
            return redirect()->back()->with('error', 'Không tìm thấy đơn hàng');
        }
    
        $valid = Validator::make($request->all(), [
            'status' => 'required|string',
            'start'  => 'required|numeric',
            'buff'   => 'required|numeric',
            'note'   => 'nullable|string',
        ]);
    
        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first());
        }
    
        $order->status = $request->status;
        $order->completed_at = $request->status === 'Completed' ? now() : null;
        $order->start = $request->start;
        $order->buff = $request->buff;
        $order->note = $request->note;
        $order->save();
    
        return redirect()->back()->with('success', 'Cập nhật đơn hàng thành công!');
    }    

    public function refundOrder(Request $request)
    {
        $or = Order::where("order_code", $request->order_code)->first();
        $domain = $request->getHost();
        $user = User::where("id", $or->user_id)
            ->where("domain", $domain)
            ->first();

        if (!$user) {
            return response()->json(
                [
                    "code" => "401",
                    "status" => "error",
                    "message" => "Không tìm thấy tài khoản thích hợp với mã đơn này !",
                ],
                401
            );
        }

        if ($user->status !== "active") {
            return response()->json(
                [
                    "code" => "401",
                    "status" => "error",
                    "message" => "Tài khoản của khách hàng hiện tại không được phép thực hiện hành động này !",
                ],
                401
            );
        }

        $order = Order::where("order_code", $request->order_code)
            ->where("user_id", $user->id)
            ->where("domain", $domain)
            ->first();
        if (!$order) {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Không tìm thấy đơn hàng cần hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Refunded") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã được hoàn tiền trước đó !",
                ],
                400
            );
        }

        if ($order->status === "WaitingForRefund") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đang chờ hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Completed") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã hoàn thành không thể hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Cancelled") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã bị hủy không thể hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Failed") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã thất bại không thể hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Partially Refunded") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã được hoàn tiền một phần không thể hoàn tiền !",
                ],
                400
            );
        }

        if ($order->status === "Partially Completed") {
            return response()->json(
                [
                    "code" => "400",
                    "status" => "error",
                    "message" => "Đơn hàng này đã hoàn thành một phần không thể hoàn tiền !",
                ],
                400
            );
        }

        $server = $order->server;

        $orderData = json_decode($order->order_data);
        $quantity = $orderData->quantity;
        $price = $orderData->price;
        $lam = $quantity - ($quantity * $server->percents) / 100;
        if ($order->status == "PendingRefundCancel") {
            $returned = $quantity;
        }
        if ($order->status == "PendingRefundPartial") {
            $returned = $quantity - $order->buff + $lam;
        }

        $order->status = "Refunded";

        $tranCode = site("madon") . "_" . time() . rand(1000, 9999);
        Transaction::create([
            "user_id" => $order->user_id,
            "tran_code" => $tranCode,
            "type" => "refund",
            "action" => "add",
            "first_balance" => ceil($returned * $price),
            "before_balance" => $order->user->balance,
            "after_balance" => $order->user->balance + ceil($returned * $price),
            "note" => "Hoàn tiền đơn hàng #" . $order->order_code,
            "ip" => $request->ip(),
            "domain" => $order->domain,
        ]);

        $order->user->balance += ceil($returned * $price);
        $order->user->save();

        if (site("discord_webhook_url")) {
            $discord_notify = new DiscordSdk();
            $discord_notify->botNotify()->sendMessage([
                "text" => "Đơn hàng **#{$order->order_code}** đã được hoàn tiền với số lượng **{$returned}** tương ứng **" . number_format(ceil($returned * $price)) . "đ**",
            ]);
        }
        $order->save();

        return response()->json(
            [
                "code" => "200",
                "status" => "success",
                "message" => "Hoàn tiền thành công !",
            ],
            200
        );
    }
    
    public function viewHistoryProductOrders(Request $request)
    {
        $categories = ProducCategories::where('domain', request()->getHost())->get();
        $totalProductSelling = Product::where('domain', request()->getHost())->where('status', 'selling')->count();
        $totalProductOrder = OrderProduct::where('domain', request()->getHost())->where('status', 'success')->count();
        $totalProductProfit = OrderProduct::where('domain', request()->getHost())->sum('price');

        $products = OrderProduct::where('domain', request()->getHost())
        ->when($request->search, function ($query) use ($request) {
            return $query->whereHas('product', function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            });
        })->orderBy('id', 'desc')->paginate(10);
        return view('admin.history.products', compact('products', 'categories', 'totalProductSelling', 'totalProductOrder', 'totalProductProfit'));
    }
}




