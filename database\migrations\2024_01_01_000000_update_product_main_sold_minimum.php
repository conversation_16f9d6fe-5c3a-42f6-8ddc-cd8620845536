<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\ProductMain;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Cập nhật tất cả ProductMain có sold < 100
        $products = ProductMain::where('sold', '<', 100)->get();

        foreach ($products as $product) {
            $randomSold = rand(100, 500); // Random từ 100-500
            $product->update(['sold' => $randomSold]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần rollback vì đây là cập nhật dữ liệu
    }
};
