<?php

namespace App\Library;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class DiscordSdk
{
    protected $webhook_url = '';
    protected $webhook_product_url = '';
    protected $webhook_box_url = '';
    protected $webhook_withdraw_url = '';

    public function __construct()
    {
        $this->webhook_url = site('discord_webhook_url') ?? '';
        $this->webhook_product_url = siteValue('discord_webhook_product_url') ?? '';
        $this->webhook_box_url = site('discord_webhook_box_url') ?? '';
        $this->webhook_withdraw_url = site('discord_webhook_withdraw_url') ?? '';
    }

    public function botNotify()
    {
        $this->webhook_url = $this->webhook_url;
        return $this;
    }

    public function botProduct()
    {
        $this->webhook_url = $this->webhook_product_url;
        return $this;
    }

    public function botBox()
    {
        $this->webhook_url = $this->webhook_box_url;
        return $this;
    }

    public function botWithdraw()
    {
        $this->webhook_url = $this->webhook_withdraw_url;
        return $this;
    }

    public function sendMessage($params)
    {
        if (empty($this->webhook_url)) {
            return ['ok' => false, 'error' => 'Webhook URL is not set'];
        }

        try {
            // Convert Telegram format to Discord format
            $content = $params['text'] ?? '';
            
            // Replace HTML formatting with Discord markdown
            $content = str_replace(['<b>', '</b>'], ['**', '**'], $content);
            $content = str_replace(['<i>', '</i>'], ['*', '*'], $content);
            $content = str_replace(['<code>', '</code>'], ['`', '`'], $content);
            $content = str_replace(['<pre>', '</pre>'], ['```', '```'], $content);
            
            $data = [
                'content' => $content,
                'username' => $params['username'] ?? 'Notification Bot',
            ];

            // Nếu có embeds, thêm vào data
            if (isset($params['embeds'])) {
                $data['embeds'] = $params['embeds'];
            }

            $options = [
                'http' => [
                    'header' => "Content-Type: application/json\r\n",
                    'method' => 'POST',
                    'content' => json_encode($data),
                ],
            ];

            $context = stream_context_create($options);
            $result = file_get_contents($this->webhook_url, false, $context);

            return ['ok' => ($result !== false), 'result' => $result];
        } catch (\Exception $e) {
            Log::error('Discord webhook error: ' . $e->getMessage());
            return ['ok' => false, 'error' => $e->getMessage()];
        }
    }

    public function sendProductMessage($params)
    {
        $temp = $this->webhook_url;
        $this->webhook_url = $this->webhook_product_url;
        $result = $this->sendMessage($params);
        $this->webhook_url = $temp;
        return $result;
    }

    public function sendWebhookMessage($webhook_url, $params = [])
    {
        try {
            $response = Http::post($webhook_url, [
                'content' => $params['text'] ?? '',
            ]);
            
            if ($response->successful()) {
                return [
                    'ok' => true,
                ];
            } else {
                return [
                    'ok' => false,
                    'error' => 'Không thể gửi tin nhắn đến Discord: ' . $response->body(),
                ];
            }
        } catch (\Exception $e) {
            return [
                'ok' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}



