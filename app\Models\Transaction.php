<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'tran_code',
        'type',
        'action',
        'first_balance',
        'before_balance',
        'after_balance',
        'note',
        'ip',
        'domain',
    ];

    /**
     * <PERSON>uan hệ với model User.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Accessor để lấy username người dùng.
     */
    public function getUsernameAttribute()
    {
        return $this->user ? $this->user->username : 'UserName Đã Bị Xóa';
    }
}
