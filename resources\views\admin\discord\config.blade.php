@extends('admin.layouts.app')
@section('title', 'Cấu hình Discord')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Cấu hình Discord</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.website.update') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">Cấu hình Webhook Thông báo</h5>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="discord_webhook_url"
                                    name="discord_webhook_url" placeholder="Nhập dữ liệu"
                                    value="{{ siteValue('discord_webhook_url') }}">
                                <label for="discord_webhook_url">Webhook URL Thông báo chung</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="discord_webhook_product_url"
                                    name="discord_webhook_product_url" placeholder="Nhập dữ liệu"
                                    value="{{ siteValue('discord_webhook_product_url') }}">
                                <label for="discord_webhook_product_url">Webhook Thông Báo Đơn Hàng Sản Phẩm (kho và apitaphoammo)</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="discord_webhook_box_url" name="discord_webhook_box_url"
                                    placeholder="Nhập dữ liệu" value="{{ site('discord_webhook_box_url') }}">
                                <label for="discord_webhook_box_url">Webhook URL nhận thông báo cập nhật giá</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="discord_webhook_withdraw_url" name="discord_webhook_withdraw_url"
                                    placeholder="Nhập dữ liệu" value="{{ site('discord_webhook_withdraw_url') }}">
                                <label for="discord_webhook_withdraw_url">Webhook URL nhận thông báo rút tiền</label>
                            </div>
                            <div class="">
                                <button type="submit" class="btn btn-primary-gradient">Lưu cấu hình</button>
                                <button type="button" class="btn btn-primary" id="btn-testWebhook">Test Webhook Chung</button>
                                <button type="button" class="btn btn-success" id="btn-testWebhookProduct">Test Webhook Đơn Hàng</button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">Hướng dẫn cấu hình Discord</h5>
                            <ol>
                                <li>Tạo một Discord server (nếu chưa có)</li>
                                <li>Tạo một channel để nhận thông báo</li>
                                <li>Vào Settings của channel > Integrations > Webhooks > New Webhook</li>
                                <li>Đặt tên cho webhook và copy webhook URL</li>
                                <li>Dán webhook URL vào ô Webhook URL ở bên trái</li>
                                <li>Nhấn "Lưu cấu hình" để lưu lại</li>
                            </ol>
                            <h5 class="mb-3 mt-4">Mô tả các loại webhook</h5>
                            <ul>
                                <li><strong>Webhook Thông báo chung:</strong> Nhận thông báo nạp tiền, rút tiền</li>
                                <li><strong>Webhook Đơn Hàng Sản Phẩm:</strong> Nhận thông báo đơn hàng từ kho và apitaphoammo</li>
                                <li><strong>Webhook Cập nhật giá:</strong> Nhận thông báo khi cập nhật giá sản phẩm</li>
                                <li><strong>Webhook Rút tiền:</strong> Nhận thông báo yêu cầu rút tiền</li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        $('#btn-testWebhook').click(function() {
            $.ajax({
                url: "{{ route('admin.discord.test-webhook') }}",
                type: 'GET',
                dataType: 'json',
                beforeSend: function() {
                    $('#btn-testWebhook').html('Đang xử lý...');
                },
                complete: function() {
                    $('#btn-testWebhook').html('Test Webhook');
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công',
                            text: response.message,
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Thất bại',
                            text: response.message,
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Thất bại',
                        text: 'Có lỗi xảy ra, vui lòng thử lại sau',
                    });
                }
            });
        });

        $('#btn-testWebhookProduct').click(function() {
            $.ajax({
                url: "{{ route('admin.discord.test-webhook-product') }}",
                type: 'GET',
                dataType: 'json',
                beforeSend: function() {
                    $('#btn-testWebhookProduct').html('Đang xử lý...');
                },
                complete: function() {
                    $('#btn-testWebhookProduct').html('Test Webhook Đơn Hàng');
                },
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thành công',
                            text: response.message,
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Thất bại',
                            text: response.message,
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Thất bại',
                        text: 'Có lỗi xảy ra, vui lòng thử lại sau',
                    });
                }
            });
        });
    });
</script>
@endsection

