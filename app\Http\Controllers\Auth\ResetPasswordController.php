<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Validator;

class ResetPasswordController extends Controller
{

    /**
     * Where to redirect users after resetting their password.
     */
    protected $redirectTo = '/home';

    /**
     * Display the password reset view for the given token.
     */
    public function showResetForm(Request $request, $token = null)
    {
        return view('auth.passwords.reset')->with(
            ['token' => $token, 'email' => $request->email]
        );
    }

    /**
     * Reset the given user's password.
     */
    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // <PERSON><PERSON>m tra token có hợp lệ không
        $passwordReset = \DB::table('password_reset_tokens')
            ->where('email', $request->email)
            ->where('token', $request->token)
            ->first();

        if (!$passwordReset) {
            return back()->withErrors(['email' => __('passwords.token')]);
        }

        // Kiểm tra token có hết hạn không (60 phút)
        if (now()->diffInMinutes($passwordReset->created_at) > 60) {
            return back()->withErrors(['email' => __('passwords.token')]);
        }

        // Tìm user
        $user = User::where('email', $request->email)
            ->where('domain', request()->getHost())
            ->first();

        if (!$user) {
            return back()->withErrors(['email' => __('passwords.user')]);
        }

        // Reset password với custom hashing
        $this->resetPassword($user, $request->password);

        // Xóa token đã sử dụng
        \DB::table('password_reset_tokens')->where('email', $request->email)->delete();

        return redirect()->route('login')->with('status', __('passwords.reset'));
    }

    /**
     * Reset the given user's password.
     */
    protected function resetPassword($user, $password)
    {
        $konkac = Str::random(16);
        $daubuoi = env('BUOM_TO');
        $saltedPassword = $konkac . $password;
        $hashedPassword = password_hash($saltedPassword . $daubuoi, PASSWORD_ARGON2ID);

        $user->forceFill([
            'password' => $hashedPassword,
            'konkac' => $konkac,
        ])->save();

        event(new PasswordReset($user));
    }
}
