<?php

namespace Database\Factories;

use App\Models\ProductMain;
use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductMainFactory extends Factory
{
    protected $model = ProductMain::class;

    public function definition()
    {
        return [
            'category_id' => ProductCategory::factory(),
            'name' => $this->faker->words(3, true),
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'image' => $this->faker->imageUrl(),
            'status' => 'active',
            'sold' => $this->faker->numberBetween(0, 1000),
            'view' => $this->faker->numberBetween(0, 5000),
            'domain' => 'test.com',
        ];
    }
}
