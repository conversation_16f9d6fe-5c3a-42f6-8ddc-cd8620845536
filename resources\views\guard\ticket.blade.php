@extends('guard.layouts.app')
@section('title', 'Hỗ trợ Ticket')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center gap-3">
                    <h5 class="card-title">Hỗ trợ Ticket</h5>
                </div>
            </div>
            <div class="card-body pc-component">
                <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" href="#pills-home"
                            role="tab" aria-controls="pills-home" aria-selected="true">Danh sách
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" href="#pills-profile"
                            role="tab" aria-controls="pills-profile" aria-selected="false" tabindex="-1">Tạo mới
                        </a>
                    </li>
                </ul>
                <div class="tab-content" id="pills-tabContent">
                    <div class="tab-pane fade active show" id="pills-home" role="tabpanel"
                        aria-labelledby="pills-home-tab">
                        <form action="" class="mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <input type="text" class="form-control" name="search" id="search"
                                            placeholder="Tìm kiếm theo tiêu đề, nội dung, ID..." value="{{ request('search') }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <select class="form-select" name="category" id="category">
                                            <option value="">Tất cả danh mục</option>
                                            <option value="don_hang" {{ request('category') == 'don_hang' ? 'selected' : '' }}>Đơn hàng</option>
                                            <option value="thanh_toan" {{ request('category') == 'thanh_toan' ? 'selected' : '' }}>Thanh toán</option>
                                            <option value="dich_vu" {{ request('category') == 'dich_vu' ? 'selected' : '' }}>Dịch vụ</option>
                                            <option value="webcon" {{ request('category') == 'webcon' ? 'selected' : '' }}>Webcon</option>
                                            <option value="khac" {{ request('category') == 'khac' ? 'selected' : '' }}>Khác</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <select class="form-select" name="status" id="status">
                                            <option value="">Tất cả trạng thái</option>
                                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ xử lý</option>
                                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Đang xử lý</option>
                                            <option value="success" {{ request('status') == 'success' ? 'selected' : '' }}>Đã xử lý</option>
                                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="{{ route('ticket') }}" class="btn btn-secondary">
                                        <i class="fas fa-refresh"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered nowrap dataTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Danh mục</th>
                                        <th>Tiêu đề</th>
                                        <th>Nội dung</th>
                                        <th>Nội dung phản hồi</th>
                                        <th>Trạng thái</th>
                                        <th>Thời gian tạo</th>
                                    </tr>
                                </thead>
                                <tbody class="fw-bold">
                                    @if ($ticket->isEmpty())
                                    @include('admin.components.table-search-not-found', [
                                    'colspan' => 7,
                                    ])
                                    @else
                                    @foreach ($ticket as $tickets)
                                    <tr>
                                        <td>{{ $tickets->id }}</td>
                                        <td>{!! categoryTicket($tickets->category) !!}</td>
                                        <td>
                                            <strong>{{ $tickets->title }}</strong>
                                        </td>
                                        <td>
                                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                {!! Str::limit(strip_tags($tickets->body), 100) !!}
                                            </div>
                                        </td>
                                        <td>
                                            @if($tickets->reply)
                                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                    {!! Str::limit(strip_tags($tickets->reply), 100) !!}
                                                </div>
                                            @else
                                                <span class="text-muted">Chưa có phản hồi</span>
                                            @endif
                                        </td>
                                        <td>{!! statusTicket($tickets->status) !!}</td>
                                        <td>
                                            <small>{{ $tickets->created_at->format('d/m/Y H:i') }}</small><br>
                                            <small class="text-muted">{{ $tickets->created_at->diffForHumans() }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                    @endif
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-center align-items-center">
                                {{ $ticket->appends(request()->all())->links('pagination::bootstrap-4') }}
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                        <form action="{{ route('ticket.post') }}" method="POST">
                            @csrf
                            <div class="form-floating mb-3">
                                <select class="form-control" name="category" required>
                                    <option value="">Chọn mục hỗ trợ</option>
                                    <option value="don_hang" {{ old('category') == 'don_hang' ? 'selected' : '' }}>Đơn hàng</option>
                                    <option value="thanh_toan" {{ old('category') == 'thanh_toan' ? 'selected' : '' }}>Thanh toán</option>
                                    <option value="dich_vu" {{ old('category') == 'dich_vu' ? 'selected' : '' }}>Dịch vụ</option>
                                    <option value="webcon" {{ old('category') == 'webcon' ? 'selected' : '' }}>Webcon</option>
                                    <option value="khac" {{ old('category') == 'khac' ? 'selected' : '' }}>Khác</option>
                                </select>
                                <label for="category">Mục hỗ trợ</label>
                            </div>

                            <div class="form-floating mb-4">
                                <input type="text" class="form-control" name="title" placeholder="Tiêu đề" value="{{ old('title') }}" required>
                                <label for="title">Tiêu đề</label>
                            </div>

                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="body" name="body" placeholder="Nội dung" style="height: 150px" required>{{ old('body') }}</textarea>
                                <label for="body">Nội dung</label>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary col-12">
                                    <i class="fas fa-save"></i> Tạo ticket
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script src="/assets/js/plugins/tinymce/tinymce.min.js"></script>
<script>
    tinymce.init({
        height: '200',
        selector: '#body',
        content_style: 'body { font-family: "Inter", sans-serif; }',
        menubar: false,
        toolbar: [
            'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist'
        ],
        plugins: 'lists'
    });
</script>
@endsection