<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RechargePromotion extends Model
{
    protected $table = 'recharge_promotions';

    protected $fillable = [
        'amount',
        'percent',
        'start_time',
        'end_time',
        'status',
        'domain',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('start_time', '<=', now())->where('end_time', '>=', now());
    }
}
