@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON> sách dịch vụ ')
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card">
                <div class="card-header">
                    <h5 class="card-title"><PERSON><PERSON> sách dịch vụ</h5>
                </div>
                <div class="card-body">
                    <div class="form-group d-flex justify-content-between align-items-center gap-2 mb-3">
                        <a href="{{ route('admin.service') }}" class="btn btn-primary-gradient">
                            <svg class="fs-2" xmlns="http://www.w3.org/2000/svg" width="20px" height="20px"
                                viewBox="0 0 24 24">
                                <path fill="currentColor"
                                    d="M4 20v-2h2.75l-.4-.35q-1.225-1.225-1.787-2.662T4 12.05q0-2.775 1.663-4.937T10 4.25v2.1Q8.2 7 7.1 8.563T6 12.05q0 1.125.425 2.188T7.75 16.2l.25.25V14h2v6zm10-.25v-2.1q1.8-.65 2.9-2.212T18 11.95q0-1.125-.425-2.187T16.25 7.8L16 7.55V10h-2V4h6v2h-2.75l.4.35q1.225 1.225 1.788 2.663T20 11.95q0 2.775-1.662 4.938T14 19.75" />
                            </svg>
                            Làm mới
                        </a>
                        <button class="btn btn-danger-gradient" data-bs-toggle="modal" data-bs-target="#new">
                            <svg xmlns="http://www.w3.org/2000/svg" data-bs-toggle="tooltip" title="Thêm mới" width="20px"
                                height="20px" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M20 14h-6v6h-4v-6H4v-4h6V4h4v6h6z" />
                            </svg>
                        </button>
                    </div>
                    <div class="table-responsive table-wrapper mb-3 table-wrapper mb-3">
                        <table id="data-table" class="table table-hover table-vcenter text-center mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Thao tác</th>
                                    <th>Tên nền tảng</th>
                                    <th>Tên dịch vụ</th>
                                    <th>Đường đẫn</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody class="fw-bold">
                                @foreach ($services as $service)
                                    <tr>
                                        <td>{{ $service->id }}</td>
                                        <td>
                                            <a href="{{ route('admin.service.edit', ['id' => $service->id]) }}"
                                                class="btn btn-sm btn-primary-gradient" data-bs-toggle="tooltip"
                                                title="Chỉnh sửa">
                                                <i class="ti ti-pencil"></i>
                                            </a>
                                            <a href="{{ route('admin.service.delete', ['id' => $service->id]) }}"
                                                class="btn btn-sm btn-danger-gradient" data-bs-toggle="tooltip"
                                                title="Xóa">
                                                <i class="ti ti-trash"></i>
                                            </a>
                                        </td>
                                        <td>{{ $service->platform->name }}</td>
                                        <td>{{ $service->name }}</td>
                                        <td>{{ $service->slug }}</td>
                                        <td>
                                            @if ($service->status == 'active')
                                                <span class="badge bg-success-gradient">Hoạt động</span>
                                            @else
                                                <span class="badge bg-danger-gradient">Không hoạt động</span>
                                            @endif
                                        </td>
                                        <td>{{ $service->created_at }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body pc-component">
        <div id="new" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="new">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="new">Thêm mới dịch vụ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="{{ route('admin.service.create') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select name="platform_id" id="platform" class="form-select">
                                            <option value="">-- Chọn nền tảng --</option>
                                            @foreach (\App\Models\ServicePlatform::where('domain', env('APP_MAIN_SITE'))->orderBy('order', 'asc')->get() as $platform)
                                                <option value="{{ $platform->id }}"
                                                    @if (old('platform_id') == $platform->id) selected @endif>{{ $platform->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <label for="platform">Chọn nền tảng</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="name" name="name"
                                            placeholder="Tên dịch vụ" value="{{ old('name') }}">
                                        <label for="name">Tên dịch vụ</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="image" name="image"
                                            placeholder="Ảnh dịch vụ" value="{{ old('image') }}">
                                        <label for="image">Image</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="slug" name="slug"
                                            placeholder="Đường dẫn" value="{{ old('slug') }}">
                                        <label for="slug">Đường dẫn</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <select name="status" id="status" class="form-select">
                                            <option value="active" @if (old('status') == 'active') selected @endif>Hoạt
                                                động</option>
                                            <option value="inactive" @if (old('status') == 'inactive') selected @endif>
                                                Không hoạt động</option>
                                        </select>
                                        <label for="status">Trạng thái</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-floating mb-3">
                                <textarea name="notice" id="notice" class="form-control" cols="30" rows="4" style="height: 120px;"></textarea>
                                <label for="note">Thông báo nổi</label>
                            </div>
                            <div class="form-floating mb-3">
                                <textarea name="note" id="note" class="form-control" cols="30" rows="4" style="height: 120px;"><p>Đơn Hàng Không Làm Đúng Yêu Cầu Của Thông Tin Máy Chủ, Không Đúng Định Dạng.</p>
                                <p>Cố Tình Đè Đơn Với Máy Chủ Không Cho Phép.</p>
                                <p>Thay Đổi Cấu Trúc Link Trong Quá Trình Đơn Đang Hoạt Động.</p>
                                <p>Tài Khoản Die Hoặc Không Bật Full Công Khai.</p></textarea>
                                <label for="note">Ghi chú</label>
                            </div>
                            <div class="form-floating mb-3">
                                <textarea name="details" id="details" class="form-control" cols="30" rows="4" style="height: 180px;"><p><strong>Những Gói Sale Siêu Rẻ Không Bảo Hành</strong> Chúng Tôi Sẽ Không Đảm Bảo Lúc Nào Cũng Lên Đều Ổn Định Và Không Thiếu Tụt. Sẽ Có Lúc Delay Cực Chậm Và Bị Thiếu Tụt Cao. Đã Xác Định Mua Thì Phải Chấp Nhận. Nhưng Tùy Lúc Nó Cũng Rất Nhanh Và Ổn Định Do Nền Tảng Nhả. Nếu Quý Khách Muốn Chắc Chắn Hãy Order Gói Giá Tốt Có Bảo Hành!</p>
<p><strong>Các Dịch vụ Siêu RẺ Không Bảo Hành "Có Thể" Sẽ Bị Lên Thiếu, Tụt Cao Từ 10-50% Hoặc Có Thể Hơn</strong> (Tùy Lúc, Tùy Gói Sẽ Không Thiếu/Tụt.)</p>

<p><strong>Trường Hợp Đơn Cài Sai Thông Tin</strong>, Sai Dạng Link Quy Định, Sai ID, Không Công Khai, Die, Không Mở Nút Follow Hoặc Đổi Username Trong Quá Trình Tăng Sẽ Không Được Hoàn Lại Tiền.</p>

<p><strong>Các Dịch vụ Siêu RẺ, Siêu Rẻ Thường Sẽ Không Hỗ Trợ Về Kích Đơn, Đẩy Đơn</strong> (Nếu Nghẽn Chỉ Có Thể Chờ.)</p>

<p><strong>Các Trường Hợp Mua Gói Có Bảo Hành Nhưng Đơn Bị Tụt Quá 30% Hãy Inbox Admin Hỗ Trợ Bảo Hành</strong> (Dưới Mức 30% Thì Chưa Được Chính Sách Bảo Hành.)</p></textarea>
                                <label for="details">Lưu ý</label>
                            </div>
                            <div class="form-floating mb-3" hidden>
                                <textarea name="blogs" id="blogs" class="form-control" cols="30" rows="8" style="height: 120px;"></textarea>
                                <label for="blogs">Bài viết</label>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-primary-gradient col-12">
                                    <i class="fas fa-save"></i> Thêm mới
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@section('script')
    <script>
        document.getElementById('createService').addEventListener('submit', function(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);

            fetch("{{ route('admin.service.create') }}", {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    },
                    body: formData,
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        Swal.fire({
                            title: 'Thành công!',
                            text: 'Dịch vụ đã được tạo thành công.',
                            icon: 'success',
                            showCancelButton: true,
                            confirmButtonText: 'Thêm tiếp',
                            cancelButtonText: 'Đóng',
                        }).then((result) => {
                            if (result.isConfirmed) {} else {
                                location.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Lỗi!',
                            text: data.errors.join(', '),
                            icon: 'error',
                            confirmButtonText: 'Đóng'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });
    </script>
    <script src="/app/js/plugins/tinymce/tinymce.min.js"></script>
    <script>
        tinymce.init({
            height: '400',
            selector: '#blogs',
            content_style: 'body { font-family: "Inter", sans-serif; }',
            menubar: false,
            toolbar: [
                'styleselect fontselect fontsizeselect',
                'undo redo | cut copy paste | bold italic | link image | alignleft aligncenter alignright alignjustify | forecolor backcolor |',
                'bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | lists charmap | print preview |  code'
            ],
            plugins: 'advlist autolink link image lists charmap print preview code'
        });
    </script>
@endsection
@endsection
