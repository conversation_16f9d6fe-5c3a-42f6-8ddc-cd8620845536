<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'image',
        'order',
        'parent_id',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'status',
        'domain'
    ];

    public function products()
    {
        return $this->hasMany(ProductMain::class, 'category_id');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
            ->orWhere('description', 'like', '%' . $search . '%')
            ->orWhere('slug', 'like', '%' . $search . '%');
    }
}
