<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductMain;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition()
    {
        return [
            'product_main_id' => ProductMain::factory(),
            'name' => $this->faker->words(3, true),
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'price' => $this->faker->numberBetween(1000, 100000),
            'rate' => $this->faker->numberBetween(500, 50000),
            'min' => 1,
            'max' => 1000,
            'type' => 'manual',
            'status' => 'active',
            'domain' => 'test.com',
        ];
    }
}
