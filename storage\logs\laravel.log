[2025-07-12 22:11:04] local.ERROR: explode(): Argument #1 ($separator) cannot be empty {"exception":"[object] (ValueError(code: 0): explode(): Argument #1 ($separator) cannot be empty at /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php:197)
[stacktrace]
#0 /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php(197): explode()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\CronJob\\RechargeCronJobController->payment()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#33 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#34 {main}
"} 
[2025-07-12 22:18:39] local.ERROR: explode(): Argument #1 ($separator) cannot be empty {"exception":"[object] (ValueError(code: 0): explode(): Argument #1 ($separator) cannot be empty at /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php:197)
[stacktrace]
#0 /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php(197): explode()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\CronJob\\RechargeCronJobController->payment()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#33 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#34 {main}
"} 
[2025-07-12 22:19:03] local.ERROR: explode(): Argument #1 ($separator) cannot be empty {"exception":"[object] (ValueError(code: 0): explode(): Argument #1 ($separator) cannot be empty at /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php:197)
[stacktrace]
#0 /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php(197): explode()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\CronJob\\RechargeCronJobController->payment()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#33 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#34 {main}
"} 
[2025-07-12 22:19:29] local.ERROR: explode(): Argument #1 ($separator) cannot be empty {"exception":"[object] (ValueError(code: 0): explode(): Argument #1 ($separator) cannot be empty at /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php:197)
[stacktrace]
#0 /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php(197): explode()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\CronJob\\RechargeCronJobController->payment()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#33 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#34 {main}
"} 
[2025-07-12 22:29:26] local.ERROR: explode(): Argument #1 ($separator) cannot be empty {"exception":"[object] (ValueError(code: 0): explode(): Argument #1 ($separator) cannot be empty at /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php:197)
[stacktrace]
#0 /www/wwwroot/muasub.online/app/Http/Controllers/CronJob/RechargeCronJobController.php(197): explode()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\CronJob\\RechargeCronJobController->payment()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#33 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#34 {main}
"} 
[2025-07-12 22:52:14] local.INFO: Đơn hàng web mẹ được tạo thành công {"order_code":"_17523355342879","user_id":1,"service_id":1,"server_id":1,"total":50.0,"provider":"dontay","domain":"muasub.online"} 
[2025-07-12 22:59:12] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'used_count' in 'SET' (Connection: mysql, SQL: update `vouchers` set `used_count` = `used_count` + 1, `vouchers`.`updated_at` = 2025-07-12 22:59:12 where `id` = 3) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'used_count' in 'SET' (Connection: mysql, SQL: update `vouchers` set `used_count` = `used_count` + 1, `vouchers`.`updated_at` = 2025-07-12 22:59:12 where `id` = 3) at /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(584): Illuminate\\Database\\Connection->run()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(536): Illuminate\\Database\\Connection->affectingStatement()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3866): Illuminate\\Database\\Connection->update()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(4003): Illuminate\\Database\\Query\\Builder->update()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3979): Illuminate\\Database\\Query\\Builder->incrementEach()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1191): Illuminate\\Database\\Query\\Builder->increment()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(991): Illuminate\\Database\\Eloquent\\Builder->increment()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(946): Illuminate\\Database\\Eloquent\\Model->incrementOrDecrement()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2355): Illuminate\\Database\\Eloquent\\Model->increment()
#10 /www/wwwroot/muasub.online/app/Http/Controllers/Api/Order/OrderController.php(656): Illuminate\\Database\\Eloquent\\Model->__call()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\Order\\OrderController->createOrder()
#12 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /www/wwwroot/muasub.online/app/Http/Middleware/XssSite.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\XssSite->handle()
#18 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#33 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#35 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#37 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#39 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#41 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#43 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#46 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'used_count' in 'SET' at /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php:592)
[stacktrace]
#0 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(592): PDO->prepare()
#1 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#3 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(584): Illuminate\\Database\\Connection->run()
#4 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Connection.php(536): Illuminate\\Database\\Connection->affectingStatement()
#5 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3866): Illuminate\\Database\\Connection->update()
#6 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(4003): Illuminate\\Database\\Query\\Builder->update()
#7 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3979): Illuminate\\Database\\Query\\Builder->incrementEach()
#8 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1191): Illuminate\\Database\\Query\\Builder->increment()
#9 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(991): Illuminate\\Database\\Eloquent\\Builder->increment()
#10 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(946): Illuminate\\Database\\Eloquent\\Model->incrementOrDecrement()
#11 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2355): Illuminate\\Database\\Eloquent\\Model->increment()
#12 /www/wwwroot/muasub.online/app/Http/Controllers/Api/Order/OrderController.php(656): Illuminate\\Database\\Eloquent\\Model->__call()
#13 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\Order\\OrderController->createOrder()
#14 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#16 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#17 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 /www/wwwroot/muasub.online/app/Http/Middleware/XssSite.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\XssSite->handle()
#20 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#28 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#29 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#45 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 /www/wwwroot/muasub.online/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 /www/wwwroot/muasub.online/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#48 {main}
"} 
[2025-07-12 23:00:19] local.INFO: Đơn hàng web mẹ được tạo thành công {"order_code":"_17523360197461","user_id":1,"service_id":1,"server_id":1,"total":0.0,"provider":"dontay","domain":"muasub.online"} 
[2025-07-12 23:00:52] local.INFO: Đơn hàng web mẹ được tạo thành công {"order_code":"_17523360526242","user_id":1,"service_id":1,"server_id":1,"total":0.0,"provider":"dontay","domain":"muasub.online"} 
[2025-07-12 23:05:10] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:58] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:05:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) (Connection: mysql, SQL: select * from `config_sites` where `domain` = maxsocial.vn and `status` = active limit 1) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#10 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#18 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#26 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'hihi'@'localhost' (using password: YES) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /home/<USER>/public_html/app/Helpers.php(11): Illuminate\\Database\\Eloquent\\Builder->first()
#20 /home/<USER>/public_html/app/Providers/AppServiceProvider.php(26): siteValue()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1121): Illuminate\\Container\\Container->call()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1102): Illuminate\\Foundation\\Application->bootProvider()
#28 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1101): array_walk()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
[2025-07-12 23:11:37] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:21:59] local.INFO: Admin OrderProduct query result {"total_records":2,"filtered_records":2,"data_count":2,"domain":"maxsocial.vn","search":""} 
[2025-07-12 23:24:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:27:31] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:28:00] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-12 23:28:23] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-12 23:28:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:29:04] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:29:48] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:30:07] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:30:18] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:34:05] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:49:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-12 23:56:01] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:05:48] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:10:00] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:15:42] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:17:05] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 00:26:14] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:32:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:33:19] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:36:40] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 00:39:29] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_read: Connection reset by peer, errno 104  
[2025-07-13 00:40:40] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:44:30] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 00:50:45] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 00:57:03] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:09:57] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:12:00] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:22:09] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:32:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:33:37] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:33:50] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:37:31] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:39:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:40:36] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:41:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:44:42] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:49:27] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:49:45] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:54:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:55:03] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 01:55:35] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 01:58:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:01:27] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 02:03:45] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:04:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:05:44] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:09:16] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 02:09:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:12:31] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 bytes received  
[2025-07-13 02:19:32] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:23:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:29:40] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:30:21] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:31:04] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:34:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:35:29] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:38:55] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:46:11] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:48:31] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:49:59] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:50:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 02:50:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:00:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:01:51] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:03:44] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:04:02] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 03:12:43] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:16:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 03:18:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:26:25] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:35:29] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:40:29] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 03:41:06] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:45:42] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 bytes received  
[2025-07-13 03:47:08] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:47:29] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:47:38] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:49:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:50:41] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:58:48] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:59:19] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:59:28] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 03:59:55] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:08:22] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:08:52] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 04:10:43] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:11:57] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:14:57] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 bytes received  
[2025-07-13 04:15:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:16:01] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 04:16:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:19:41] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:25:54] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:37:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:38:13] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 04:53:35] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:54:35] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 04:57:23] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 05:00:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:05:08] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:08:55] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:09:07] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_read: Connection reset by peer, errno 104  
[2025-07-13 05:12:15] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:13:51] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:15:13] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_connect: Connection reset by peer in connection to traodoisub.com:443   
[2025-07-13 05:16:40] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:27:08] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:28:01] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 05:35:53] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30002 milliseconds with 0 bytes received  
[2025-07-13 05:36:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:46:19] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 05:47:07] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:48:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:50:08] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 05:51:20] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 05:57:37] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 06:00:18] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:07:48] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 06:10:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:14:44] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:15:25] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:15:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:16:48] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:17:27] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:19:28] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 06:22:21] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:23:36] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:24:58] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:25:36] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:26:54] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:29:33] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:46:57] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:47:48] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:48:18] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:48:44] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:48:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 06:51:57] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 06:58:58] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 06:59:59] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:07:18] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:07:50] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30002 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:08:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:18:02] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:24:41] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:26:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 07:28:35] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 07:31:02] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:36:32] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 07:39:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 07:49:21] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30000 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:01:56] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 08:07:12] local.ERROR: Lỗi CURL khi gửi request GET: OpenSSL SSL_read: Connection reset by peer, errno 104  
[2025-07-13 08:08:43] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:15:17] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 08:26:01] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:34:11] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:41:43] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:44:36] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:46:36] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:53:22] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:53:39] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 08:58:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 09:02:12] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:07:44] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:08:14] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:08:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:08:45] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:09:04] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:09:30] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:09:34] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:23:37] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 09:23:42] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'product' in 'ORDER BY' (Connection: mysql, SQL: select * from `order_products` where `user_id` = 1 and `domain` = maxsocial.vn and (`email` like %% or `phone` like %% or `note` like %% or `data` like %%) order by `product` asc limit 10 offset 0) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'product' in 'ORDER BY' (Connection: mysql, SQL: select * from `order_products` where `user_id` = 1 and `domain` = maxsocial.vn and (`email` like %% or `phone` like %% or `note` like %% or `data` like %%) order by `product` asc limit 10 offset 0) at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /home/<USER>/public_html/app/Http/Controllers/Guard/ViewGuardController.php(62): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Guard\\ViewGuardController->GetData()
#10 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 /home/<USER>/public_html/app/Http/Middleware/AuthSite.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\AuthSite->handle()
#16 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#48 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#59 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'product' in 'ORDER BY' at /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:407)
[stacktrace]
#0 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): PDO->prepare()
#1 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3133): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(759): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 /home/<USER>/public_html/app/Http/Controllers/Guard/ViewGuardController.php(62): Illuminate\\Database\\Eloquent\\Builder->get()
#11 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Guard\\ViewGuardController->GetData()
#12 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /home/<USER>/public_html/app/Http/Middleware/AuthSite.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\AuthSite->handle()
#18 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#22 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#39 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#41 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#45 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#48 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#50 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#52 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#54 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#56 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#58 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#59 /home/<USER>/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#60 /home/<USER>/public_html/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#61 {main}
"} 
[2025-07-13 09:26:48] local.ERROR: Lỗi CURL khi gửi request GET: Empty reply from server  
[2025-07-13 09:28:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:29:19] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:31:45] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 bytes received  
[2025-07-13 09:32:18] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:33:49] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:35:09] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:36:32] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
[2025-07-13 09:37:21] local.ERROR: Lỗi CURL khi gửi request GET: Operation timed out after 30001 milliseconds with 0 out of 0 bytes received  
