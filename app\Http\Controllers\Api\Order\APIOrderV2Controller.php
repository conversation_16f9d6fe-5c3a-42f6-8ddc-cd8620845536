<?php

namespace App\Http\Controllers\Api\Order;

use App\Http\Controllers\Api\Service\SmmController;
use App\Http\Controllers\Controller;
use App\Library\TelegramSdk;
use App\Library\DiscordSdk;
use App\Models\Order;
use App\Models\ServerAction;
use App\Models\ServiceServer;
use App\Models\Smm;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class APIOrderV2Controller extends Controller
{
    public function APIOrderV2(Request $request) {
        try {
            if (site('maintain') === 'on') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Hệ thống đang bảo trì, vui lòng quay lại sau !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'key' => 'required',
                'action' => 'required',
            ], [
                'key.required' => 'Key is required!',
                'action.required' => 'Action is required!',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'error' => "Missing Data!"
                ]);
            }

            $key = $request->key;
            $action = $request->action;

            $user = User::where('api_token', $key)->where('domain', $request->getHost())->first();

            if (!$user) {
                return response()->json([
                    'error' => "API Key is invalid!"
                ]);
            }

            if ($action == 'balance') {
                return response()->json([
                    'balance' => $user->balance ?? 0,
                    'currency' => 'VND',
                ]);
            }

            if ($action == 'add') {
                return $this->add($request, $user);
            }

            if ($action == 'services') {
                return $this->loadServices($request, $user);
            }

            if ($action == 'status') {
                return $this->loadOrderStatus($request, $user);
            }

            if ($action == 'refill') {
                if ($request->has('orders')) {
                    return $this->loadCreateMultiRefill($request, $user);
                } else {
                    return $this->loadCreateRefill($request, $user);
                }
            }

            if ($action == 'refill_status') {
                return $this->loadRefillStatus($request, $user);
            }

            if ($action == 'cancel') {
                return $this->loadCreateCancel($request, $user);
            }

            return response()->json([
                'error' => "Invalid Action!"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ]);
        }
    }

    public function loadCreateCancel(Request $request, User $user)
    {
        if (empty($request->orders)) {
            return response()->json([
                'error' => "Orders is required!"
            ]);
        }

        $orders = Order::where('domain', $request->getHost())->whereIn('id', explode(',', $request->orders))
            ->where('user_id', $user->id)
            ->where('status', '!=', 'Completed')
            ->where('status', '!=', 'Processing')
            ->where('status', '!=', 'Refund')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Canceled')
            ->where('status', '!=', 'Error')
            ->where('status', '!=', 'Partial')
            ->limit(100)->get();

        $data = [];

        foreach ($orders as $order) {
            if ($order->server->cancel) {
                $smm = Smm::where('name', $order->server->providerName)->first();

                if ($smm) {
                    $api = new SmmController();

                    $api->api_url = $order->server->providerLink;
                    $api->api_key = $smm->api_token;

                    $response = $api->cancel($order->order_id);

                    if (isset($response['cancel'])) {
                        if ($response['cancel']) {
                            $data[] = [
                                'order' => $order->id,
                                'cancel' => $response['cancel']
                            ];
                        } else {
                            $data[] = [
                                'order' => $order->id,
                                'cancel' => [
                                    'error' => "Error Cancel!"
                                ]
                            ];
                        }
                    }
                }
            } else {
                $data[] = [
                    'order' => $order->id,
                    'cancel' => [
                        'error' => "Service not support cancel!"
                    ]
                ];
            }
        }
    }

    public function loadRefillStatus(Request $request, User $user)
    {
        if (empty($request->refills)) {
            return response()->json([
                'error' => "Refills is required!"
            ]);
        }

        $refills = Order::where('domain', $request->getHost())->whereIn('id', explode(',', $request->refills))
            ->where('user_id', $user->id)
            ->where('status', '!=', 'Completed')
            ->where('status', '!=', 'Processing')
            ->where('status', '!=', 'Refund')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Canceled')
            ->where('status', '!=', 'Error')
            ->where('status', '!=', 'Partial')
            ->limit(100)->get();

        $data = [];
        foreach ($refills as $refill) {
            if ($refill) {
                $smm = Smm::where('name', $refill->server->providerName)->first();

                if ($smm) {

                    $api = new SmmController();

                    $api->api_url = $refill->server->providerLink;
                    $api->api_key = $smm->api_token;
                    $response = $api->refillStatus($refill->order_id);

                    if (isset($reponse['refill'])) {
                        if ($response['refill']) {
                            $data[] = [
                                'refill' => $refill->id,
                                'status' => $response['status']
                            ];
                        } else {
                            $data[] = [
                                'refill' => $refill->id,
                                'status' => [
                                    'error' => "Error Refill!"
                                ]
                            ];
                        }
                    }
                }
            } else {
                $data[] = [
                    'refill' => $refill->id,
                    'status' => [
                        'error' => "Service not support refill!"
                    ]
                ];
            }
        }
    }

    public function loadCreateMultiRefill(Request $request, User $user)
    {
        if (empty($request->orders)) {
            return response()->json([
                'error' => "Orders is required!"
            ]);
        }

        $orders = Order::where('domain', $request->getHost())->whereIn('id', explode(',', $request->orders))
            ->where('status', '!=', 'Completed')
            ->where('status', '!=', 'Processing')
            ->where('status', '!=', 'Refund')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Canceled')
            ->where('status', '!=', 'Error')
            ->where('status', '!=', 'Partial')->where('user_id', $user->id)->limit(100)->get();

        $data = [];
        foreach ($orders as $order) {
            if ($order->server->refill) {
                $smm = Smm::where('name', $order->server->providerName)->first();

                if ($smm) {

                    $api = new SmmController();

                    $api->api_url = $order->server->providerLink;
                    $api->api_key = $smm->api_token;
                    $response = $api->refill($order->order_id);

                    if (isset($reponse['refill'])) {
                        if ($response['refill']) {
                            $data[] = [
                                'order' => $order->id,
                                'refill' => $response['refill']
                            ];
                        } else {
                            $data[] = [
                                'order' => $order->id,
                                'refill' => [
                                    'error' => "Error Refill!"
                                ]
                            ];
                        }
                    }
                }
            } else {
                $data[] = [
                    'order' => $order->id,
                    'refill' => [
                        'error' => "Service not support refill!"
                    ]
                ];
            }
        }

        return response()->json($data);
    }

    public function loadCreateRefill(Request $request, User $user)
    {


        if (empty($request->order)) {
            return response()->json([
                'error' => "Order is required!"
            ]);
        }

        $order = Order::where('domain', $request->getHost())->where('id', $request->order)
            ->where('status', '!=', 'Completed')
            ->where('status', '!=', 'Processing')
            ->where('status', '!=', 'Refund')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Canceled')
            ->where('status', '!=', 'Error')
            ->where('status', '!=', 'Partial')->where('user_id', $user->id)->first();
        if (!$order) {
            return response()->json([
                'error' => "Icorrect Order! ID"
            ]);
        }

        if (!$order->server->refill) {
            return response()->json([
                'error' => "Service not support refill!"
            ]);
        }



        $smm = Smm::where('name', $order->server->providerName)->first();

        if (!$smm) {
            return response()->json([
                'error' => "SMM Panel not found!"
            ]);
        }

        $api = new SmmController();
        $api->api_url = $order->server->providerLink;
        $api->api_key = $smm->api_token;


        $response = $api->refill($order->order_id);

        if (isset($reponse['refill'])) {
            if ($response['refill']) {
                return response()->json([
                    'refill' => $response['refill']
                ]);
            } else {
                return response()->json([
                    'error' => "Error Refill!"
                ]);
            }
        }
    }

    public function loadOrderStatus(Request $request, User $user)
    {

        if ($request->has('orders')) {
            return $this->loadOrderMultiStatus($request, $user);
        }

        if (empty($request->order)) {
            return response()->json([
                'error' => "Order is required!"
            ]);
        }

        $order = Order::where('domain', $request->getHost())->where('id', $request->order)->where('user_id', $user->id)->first();

        if (!$order) {
            return response()->json([]);
        }

        return response()->json([
            'charge' => $order->payment,
            'currency' => 'VND',
            'status' => $order->status,
            'start_count' => $order->start,
            'remains' => $order->buff,
        ]);
    }

    public function loadOrderMultiStatus(Request $request, User $user)
    {

        if (empty($request->orders)) {
            return response()->json([
                'error' => "Orders is required!"
            ]);
        }

        $orders = Order::where('domain', $request->getHost())->whereIn('id', explode(',', $request->orders))->where('user_id', $user->id)->limit(100)->get();

        $data = [];
        foreach ($orders as $order) {
            $data[$order->id] = [
                'charge' => (string) $order->payment,
                'start_count' => (string) $order->start,
                'status' => $order->status,
                'remains' => (string) $order->buff,
                'currency' => 'VND',
            ];
        }

        return response()->json($data);
    }

    public function loadServices(Request $request, User $user)
    {

        $services = ServiceServer::where('domain', $request->getHost())->where('status', 'active')->get();

        $data = [];

        foreach ($services as $service) {
            $server_action = ServerAction::where('server_id', $service->id)->first();
            $type = ($server_action->comments_status == 'on') ? 'Custom Comments' : 'Default';

            $data[] = [
                'service' => $service->id,
                'name' => $service->name,
                'rate' => $service->levelPrice($user->level),
                "type" => $type, 
                'platform' => $service->service->platform->name,
                'category' => $service->service->platform->name . '|' . $service->service->name,
                'package_name' => $service->service->package,
                'description' => $service->details,
                'min' => $service->min,
                'max' => $service->max,
                'limit_day' => ($service->limit_day == 0) ? 'Unlimited' : $service->limit_day,
            ];
        }
        

        return response()->json($data);
    }

    private function add(Request $request, User $user)
    {
        $valid = Validator::make($request->all(), [
            'service' => 'required|integer',
        ], [
            'service.required' => 'Service is required!',
            'service.integer' => 'Service must be an integer!',
        ]);

        if ($valid->fails()) {
            return response()->json([
                'error' => "Missing Data!"
            ]);
        } else {
            $id_service = $request->service;

            $service = ServiceServer::where('id', $id_service)->where("domain", $user->domain)->first();

            if (!$service) {
                return response()->json([
                    'error' => "Service not found!"
                ]);
            }

            $option_service = $service->action;

            if (!$option_service) {
                return response()->json([
                    'error' => "Service not found!"
                ]);
            }
            if ($option_service->quantity_status == 'on' && empty($request->link)) {
                return response()->json([
                    'error' => "Link is required!"
                ]);
            }
            if ($option_service->quantity_status == 'on' && empty($request->quantity)) {
                return response()->json([
                    'error' => "Quantity is required!"
                ]);
            }

            if ($option_service->comments_status == 'on' && empty($request->comments)) {
                return response()->json([
                    'error' => "Comments is required!"
                ]);
            }

            if (isset($request->comments) && !empty($request->comments) && $option_service->comments_status == 'on') {
                $comments = explode("\n", $request->comments);
                $comments = array_filter($comments, function ($value) {
                    return !is_null($value) && $value !== '';
                });
                $request->merge(['quantity' => count($comments)]);
            }

            if ($service->status != 'active') {
                return response()->json([
                    'error' => "Service is inactive!"
                ]);
            }

            if ($service->visibility != 'public') {
                return response()->json([
                    'error' => "Service is private!"
                ]);
            }

            $price = $service->levelPrice($user->level);

            $total = $price * $request->quantity;
            $total_profit = $service->price * $request->quantity;

            if ($user->balance < ceil($total)) {
                return response()->json([
                    'error' => "Not enough balance!"
                ]);
            }

            if (env('APP_MAIN_SITE') == $request->getHost()) {

                $serviceMain = $service->service;
                $platformMain = $service->platform;

                $urlOrder = "https://" . $request->getHost() . "/api/v1/create/order";


                $dataSend = array(
                    'provider_package' => $serviceMain->package,
                    'provider_server' => $service->package_id,
                    'object_id' => $request->link,
                    'quantity' => $request->quantity,
                    'reaction' => $request->reaction,
                    'comments' => $request->comments,
                    'minutes' => $request->minutes,
                );

                $headers = array(
                    'X-Access-Token: ' . $user->api_token,
                    'Content-Type: application/json'
                );

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $urlOrder,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($dataSend),
                    CURLOPT_HTTPHEADER => $headers,
                ));

                $response = curl_exec($curl);

                curl_close($curl);

                $result = json_decode($response, true);

                if (isset($result) && $result['status'] == 'success') {
                    return response()->json([
                        'order' => $result['data']['id'],
                    ]);
                } else {
                    return response()->json([
                        'error' => $result['message'] ?? "Error Mising Data!"
                    ]);
                }
            } else {
                $admin = User::where('username', site('admin_username'))->where('domain', site('is_domain'))->first();
                $domain = $request->getHost();
                if (!$admin) {
                    return response()->json([
                        'error' => "Admin not found!"
                    ]);
                }

                $urlOrder = "https://" . site('is_domain') . "/api/v2";

                $dataSend = array(
                    'key' => $admin->api_token,
                    'action' => 'add',
                    'service' => $service->id,
                    'link' => $request->link,
                    'quantity' => $request->quantity,
                    'comments' => $request->comments,
                    'note' => $request->getHost() . ' - Khởi tạo đơn hàng từ API',
                );

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $urlOrder,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($dataSend),
                    CURLOPT_HTTPHEADER => array(
                        'X-Access-Token: ' . $admin->api_token,
                        'Content-Type: application/json'
                    ),
                ));

                $response = curl_exec($curl);

                curl_close($curl);
                $result = json_decode($response, true);
                if (isset($result['order'])) {
                    $orderID = $result['order'];
                    $orderCode = site('madon') . '_' . time() . rand(1000, 9999);

                    $orderData = [
                        "user_id" => $user->id,
                        "service_id" => $service->service->id ?? null,
                        "server_id" => $service->id,
                        "order_code" => $orderCode,
                        "object_id" => $request->link,
                        "quantity" => $request->quantity,
                        "reaction" => $request->reaction,
                        "comments" => htmlentities($request->comments),
                        "minutes" => $request->minutes,
                        "price" => $price,
                        'payment' => $total,
                        'note' => $request->note,
                    ];

                    $order = new Order();
                    $order->user_id = $user->id;
                    $order->service_id = $service->service->id;
                    $order->server_id = $service->id;
                    $order->orderProviderName = $service->providerName;
                    $order->orderProviderServer = $service->providerServer;
                    $order->order_package = $service->package;
                    $order->object_server = $request->provider_server;
                    $order->object_id = $request->link;
                    $order->order_id = $orderID;
                    $order->order_code = $orderCode;
                    $order->order_data = json_encode($orderData);
                    $order->start = 0;
                    $order->buff = 0;
                    $order->duration = $request->duration;
                    $order->remaining = $request->duration;
                    $order->posts = 0;
                    $order->price = $price;
                    $order->payment = $total;
                    $order->total_profit = $total - $total_profit;
                    $order->status = 'Processing';
                    $order->ip = $request->ip();
                    $order->note = $request->note;
                    $order->time = now();
                    $order->time_order = $request->time_order ?? now();
                    $order->domain = $domain;
                    $order->save();

                    if ($order) {

                        // nếu số dư của user nhỏ hơn total thì block user
                        if ($user->balance < $total) {
                            $user->status = 'banned';
                            $user->save();
                            return response()->json([
                                'error' => "Not enough balance! Your account has been blocked!"
                            ], 400);
                        }

                        $transaction = new Transaction();
                        $transaction->user_id = $user->id;
                        $transaction->tran_code = $orderCode;
                        $transaction->type = 'order';
                        $transaction->action = 'sub';
                        $transaction->first_balance = $user->balance;
                        $transaction->before_balance = $total;
                        $transaction->after_balance = $user->balance - $total;
                        $transaction->note = 'Thanh toán đơn hàng ' . $orderCode;
                        $transaction->ip = $request->ip();
                        $transaction->domain = $domain;
                        $transaction->save();

                        $user->decrement('balance', $total);

                        if (site('telegram_bot_token') && site('telegram_chat_id')) {
                            $bot_notify = new TelegramSdk();
                            $bot_notify->botNotify()->sendMessage([
                                'chat_id' => site('telegram_chat_id'),
                                'text' => '🛒 <b>Đơn hàng mới được tạo từ website ' . $domain . ' !' . "</b>\n\n" .
                                    '📦 <b>Gói dịch vụ:</b> ' . $service->platform->name . " - " . $service->name .
                                    '🔗 <b>Link hoặc UID:</b> ' . $request->object_id . "\n" .
                                    '🔢 <b>Số lượng:</b> ' . number_format($request->quantity) . "\n" .
                                    '🔗 <b>Máy chủ:</b> ' . $service->package_id . "\n" .
                                    '💰 <b>Giá tiền:</b> ' . $price . 'đ' . "\n" .
                                    '💵 <b>Thanh toán:</b> ' . $total . 'đ' . "\n" .
                                    '📝 <b>Ghi chú:</b> ' . $request->note . "\n",
                                'parse_mode' => 'HTML',
                            ]);
                        }

                        // Gửi thông báo Discord
                        if (site('discord_webhook_url')) {
                            try {
                                $notifyMessage = '🛒 **Đơn Hàng Mới Được Tạo Từ Website ' . $domain . '!**' . PHP_EOL . PHP_EOL .
                                    '👤 **Khách Hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                                    '🧾 **Mã Đơn Hàng:** ' . $orderCode . PHP_EOL .
                                    '📦 **Gói Dịch Vụ:** ' . $service->platform->name . " - " . $service->name . PHP_EOL .
                                    '🔗 **Link Hoặc UID:** ' . $request->link . PHP_EOL .
                                    '🔢 **Số Lượng:** ' . number_format($request->quantity) . PHP_EOL .
                                    '🔗 **Máy Chủ:** ' . $service->package_id . PHP_EOL .
                                    '💰 **Giá Tiền:** ' . number_format($price) . 'đ' . PHP_EOL .
                                    '💵 **Thanh Toán:** ' . number_format($total) . 'đ' . PHP_EOL .
                                    '📝 **Ghi Chú:** ' . ($request->note ?? 'Không có ghi chú') . PHP_EOL;

                                $discord_notify = new DiscordSdk();
                                $discord_notify->botNotify()->sendMessage([
                                    'text' => $notifyMessage,
                                ]);
                            } catch (\Exception $e) {
                                \Log::error('Lỗi gửi thông báo Discord cho API v2: ' . $e->getMessage());
                            }
                        }

                        if ($user->telegram_id !== null && $user->telegram_id !== '' && $user->notification_telegram == 'yes') {
                            $bot_notify = new TelegramSdk();
                            $bot_notify->botChat()->sendMessage([
                                'chat_id' => $user->telegram_id,
                                'text' => '🛒 <b>Bạn vừa tạo đơn hàng mới từ website ' . $domain . ' !' . "</b>\n\n" .
                                    '📦 <b>Gói dịch vụ:</b> ' . $service->platform->name . " - " . $service->name .
                                    '🔗 <b>Link hoặc UID:</b> ' . $request->object_id . "\n" .
                                    '🔢 <b>Số lượng:</b> ' . number_format($request->quantity) . "\n" .
                                    '🔗 <b>Máy chủ:</b> ' . $service->package_id . "\n" .
                                    '💰 <b>Giá tiền:</b> ' . $price . 'đ' . "\n" .
                                    '💵 <b>Thanh toán:</b> ' . $total . 'đ' . "\n" .
                                    '📝 <b>Ghi chú:</b> ' . $request->note . "\n",
                                'parse_mode' => 'HTML',
                            ]);
                        }

                        return response()->json([
                            'order' => $order->id,
                        ], 200);
                    }
                } else {
                    return response()->json([
                        'error' => $result['error'] ?? "Error Mising Data!"
                    ]);
                }
            }
        }
    }
}
