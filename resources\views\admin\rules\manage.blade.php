@extends('admin.layouts.app')
@section('title', 'Quản lý quy tắc')
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Quản lý quy tắc & đi<PERSON><PERSON></h5>
                <p class="text-muted mb-0">Cấu hình nội dung quy tắc sẽ hiển thị cho user</p>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.rules.update') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Editor Section -->
                            <div class="mb-4">
                                <label for="rule" class="form-label fw-bold">
                                    <i class="bx bx-edit me-2"></i>Nội dung quy tắc & đ<PERSON><PERSON><PERSON>
                                </label>
                                <div class="editor-container">
                                    <textarea class="form-control" id="rule" name="rule"
                                              placeholder="Nhập nội dung quy tắc..."
                                              style="height: 500px;">{{ siteValue('rule') }}</textarea>
                                </div>
                            </div>

                            <!-- Instructions Section -->
                            <div class="alert alert-info mb-4">
                                <h6 class="alert-heading">
                                    <i class="bx bx-info-circle me-2"></i>Hướng dẫn sử dụng:
                                </h6>
                                <hr>
                                <ul class="mb-0">
                                    <li class="mb-1">Sử dụng trình soạn thảo để định dạng nội dung</li>
                                    <li class="mb-1">Có thể sử dụng các thẻ: <code>&lt;h1&gt;</code>, <code>&lt;h2&gt;</code>, <code>&lt;p&gt;</code>, <code>&lt;ul&gt;</code>, <code>&lt;li&gt;</code>, <code>&lt;strong&gt;</code>, <code>&lt;em&gt;</code></li>
                                    <li class="mb-1">Nội dung sẽ hiển thị trên trang "Quy tắc" của user</li>
                                    <li class="mb-0">Để trống nếu muốn sử dụng nội dung mặc định</li>
                                </ul>
                            </div>

                            <!-- Action Buttons Section -->
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bx bx-save me-1"></i>
                                    Lưu quy tắc
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewRules()">
                                    <i class="bx bx-show me-1"></i>
                                    Xem trước
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="resetToDefault()">
                                    <i class="bx bx-reset me-1"></i>
                                    Khôi phục mặc định
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="openUserRulesPage()">
                                    <i class="bx bx-link-external me-1"></i>
                                    Xem trang user
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Xem trước quy tắc</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent" class="border p-3" style="max-height: 400px; overflow-y: auto;">
                    <!-- Preview content will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script src="/app/js/plugins/tinymce/tinymce.min.js"></script>
<script>
    tinymce.init({
        height: '500',
        selector: '#rule',
        content_style: 'body { font-family: "Inter", sans-serif; }',
        menubar: false,
        toolbar: [
            'styleselect fontselect fontsizeselect',
            'undo redo | cut copy paste | bold italic | link | alignleft aligncenter alignright alignjustify',
            'bullist numlist | outdent indent | blockquote subscript superscript | code'
        ],
        plugins: 'lists link code',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }
    });

    function previewRules() {
        // Get content from TinyMCE
        const content = tinymce.get('rule').getContent();
        
        if (!content.trim()) {
            alert('Vui lòng nhập nội dung quy tắc để xem trước');
            return;
        }
        
        // Insert content into preview modal
        document.getElementById('previewContent').innerHTML = content;
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        modal.show();
    }

    function resetToDefault() {
        if (confirm('Bạn có chắc chắn muốn khôi phục nội dung mặc định? Thao tác này sẽ xóa toàn bộ nội dung hiện tại.')) {
            const defaultContent = `
                <h5 style="color: #dc3545; text-align: center; font-weight: bold;">KHI BẠN SỬ DỤNG WEBSITE {{site('name_site')}}</h5>
                <h5 style="text-align: center; font-weight: bold;">Có nghĩa là bạn đồng ý với các điều khoản dưới đây!</h5>
                <br>

                <div>
                    <h6 style="font-weight: bold;">QUY ĐỊNH VỀ TIỀN TRÊN WEBSITE</h6>
                    <ul>
                        <li>Giá trị quy đổi tương đương tiền tệ VNĐ, tỉ giá giao động theo thị trường.</li>
                        <li>Hình thức nạp tiền vào website là hoàn toàn tự nguyện, dùng để sử dụng các dịch vụ được cung cấp trên website.</li>
                        <li>Khi nạp tiền vào website người dùng sẽ nhận được số tiền tương ứng (có thể thêm khuyến mãi từ admin) và số tiền này không thể quy đổi ngược lại.</li>
                    </ul>
                </div>

                <div>
                    <h6 style="font-weight: bold;">QUY ĐỊNH ĐƠN HÀNG</h6>
                    <ul>
                        <li>Nên chạy test số lượng nhỏ để chọn dịch vụ phù hợp.</li>
                        <li>Bắt buộc đọc kĩ thông tin máy chủ (chọn server sẽ hiện).</li>
                        <li>Yêu cầu cài dịch vụ đúng thông tin máy chủ yêu cầu.</li>
                        <li>Phần tốc độ chỉ để tham khảo, KHÔNG chính xác 100%.</li>
                        <li>Mỗi dịch vụ có các quy định khác nhau, xem chi tiết tại thông tin máy chủ của dịch vụ trước khi sử dụng.</li>
                        <li>Các dịch vụ có quy định và cách thức hoạt động có thể thay đổi theo thời gian.</li>
                        <li>Các đơn hàng báo lỗi, báo huỷ liên hệ admin để được kiểm tra và xử lý.</li>
                    </ul>
                </div>

                <div>
                    <h6 style="font-weight: bold;">QUY ĐỊNH CHẤT LƯỢNG DỊCH VỤ</h6>
                    <ul>
                        <li>Website chúng tôi nghiên về hướng cung cấp dịch vụ GIÁ RẺ, có các loại như sau:</li>
                        <li><strong>Không Bảo Hành</strong>: Dịch vụ có rủi ro, đơn có thể không chạy hoặc không đạt yêu cầu (không hỗ trợ hoàn tiền).</li>
                        <li><strong>Có Bảo Hành</strong>: Dịch vụ có bảo hành trong thời gian ghi trên thông tin máy chủ (lên thiếu hoặc không lên có thể yêu cầu bảo hành).</li>
                        <li>Bạn có thể yêu cầu thêm bảo hành cho dịch vụ không có bảo hành.</li>
                        <li>Đơn hàng không bảo hành nhưng gửi admin có thể xem xét hỗ trợ hoàn tiền.</li>
                        <li>Mọi quyết định cuối cùng đều do admin quyết định.</li>
                    </ul>
                </div>
            `;
            
            tinymce.get('rule').setContent(defaultContent);
        }
    }

    function openUserRulesPage() {
        window.open('/rule', '_blank');
    }
</script>

<style>
/* Custom CSS for better spacing and layout */
.editor-container {
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

.editor-container .tox-tinymce {
    border: none !important;
}

.alert-info {
    border-left: 4px solid #0dcaf0;
}

.alert-info .alert-heading {
    color: #055160;
    margin-bottom: 0.5rem;
}

.alert-info hr {
    margin: 0.5rem 0;
    border-color: #b8daff;
}

.alert-info code {
    background-color: #e7f3ff;
    color: #0c5460;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

.btn-group-custom {
    gap: 0.5rem;
}

.form-label {
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

/* Ensure proper spacing between sections */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

/* TinyMCE specific adjustments */
.tox .tox-editor-header {
    border-bottom: 1px solid #dee2e6 !important;
}

.tox .tox-toolbar {
    background: #f8f9fa !important;
}

/* Preview modal styling */
#previewContent {
    background: #fff;
    border-radius: 0.375rem;
}

#previewContent h1, #previewContent h2, #previewContent h3,
#previewContent h4, #previewContent h5, #previewContent h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

#previewContent ul, #previewContent ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

#previewContent li {
    margin-bottom: 0.25rem;
}

#previewContent p {
    margin-bottom: 1rem;
}
</style>
@endsection
