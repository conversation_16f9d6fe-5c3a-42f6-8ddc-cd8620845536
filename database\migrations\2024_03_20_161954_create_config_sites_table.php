<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('config_sites', function (Blueprint $table) {
            $table->id();
            $table->string('name_site')->nullable();
            $table->string('title')->nullable();
            $table->longText('description')->nullable();
            $table->longText('keywords')->nullable();
            $table->longText('author')->nullable();
            $table->string('thumbnail')->nullable();

            $table->string('logo')->nullable();
            $table->string('favicon')->nullable();

            $table->string('facebook')->nullable();
            $table->string('zalo')->nullable();
            $table->string('discord')->nullable();
            $table->enum('maintenance', ['on', 'off'])->default('off');

            /*  */
            $table->longText('collaborator')->nullable()->comment('<PERSON><PERSON><PERSON> nạp cộng tác viên');
            $table->longText('agency')->nullable()->comment('Mức nạp đại lý');
            $table->longText('distributor')->nullable()->comment('Mức nạp nhà phân phối');

            // recharge
            $table->string('start_promotion')->nullable(); // bắt đầu khuyến mãi
            $table->string('end_promotion')->nullable(); // kết thúc khuyến mãi
            $table->string('percent_promotion')->nullable(); // khuyến mãi
            $table->string('transfer_code')->nullable(); // mã chuyển tiền

            // discord webhooks
            $table->string('discord_webhook_url')->nullable();
            $table->string('discord_webhook_chat_url')->nullable();
            $table->string('discord_webhook_dontay_url')->nullable();
            $table->string('discord_webhook_box_url')->nullable();
            $table->string('discord_webhook_withdraw_url')->nullable();
            $table->string('discord_server_invite')->nullable();


            // notice
            $table->longText('notice')->nullable();

            // script
            $table->longText('script_head')->nullable();
            $table->longText('script_body')->nullable();
            $table->longText('script_footer')->nullable();

            // auth admin site
            $table->string('admin_username')->nullable();
            $table->longText('site_token')->nullable();
            $table->enum('status', ['active', 'pending', 'inactive'])->nullable();

            $table->timestamps();
            $table->string('domain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('config_sites');
    }
};
