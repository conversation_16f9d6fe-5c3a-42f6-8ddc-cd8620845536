<!DOCTYPE html>
<html lang="{{ str_replace('-', '_', app()->getLocale()) }}">
<meta http-equiv="content-type" content="text/html;charset=utf-8" />

<head>
    <title>{{ site('name_site') }} - @yield('title')</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="logo" content="{{ site('logo') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="icon" href="{{ siteValue('favicon') }}" type="image/x-icon">
    @if (Auth::check())
        <meta name="access-token" content="{{ Auth::user()->api_token }}">
    @endif
    <meta name="description" content="{{ siteValue('description') }}">
    <meta name="keywords" content="{{ siteValue('keywords') }}">
    <meta name="author" content="{{ siteValue('author') }}">
    <meta property="og:title" content="{{ siteValue('title') }}">
    <meta property="og:description" content="{{ siteValue('description') }}">
    <meta property="og:image" content="{{ siteValue('thumbnail') }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="{{ siteValue('title') }}">
     
<!-- App css -->
    <link href="/theme/css/bootstrap.min.css?v=1.0.2" rel="stylesheet" type="text/css" />
    <link href="/theme/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/theme/css/app.min.css?v=1.0.2" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/2.2.0/css/dataTables.bootstrap5.css" rel="stylesheet" type="text/css" />
    <link href="/theme/css/styles.css?time=1750607681" rel="stylesheet" type="text/css" />

    <script data-host="https://analytics.vsm.vn" data-dnt="false" src="https://analytics.vsm.vn/js/script.js" id="ZwSg9rf6GA" async defer></script>

        <style>
        .list-input-radio {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .list-input-radio input[type="radio"] {
            display: none;
        }

        .list-input-radio label {
            display: block;
            width: auto;
            padding: 2px 13px;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-size: 14px;
            font-weight: 700;
        }

        .list-input-radio input[type="radio"]:checked+label {
            background-color: #47991f;
            color: #fff;
            border-color: #47991f;
        }
        .box-image-product {
            margin-top: -6.5rem;
        }
        
        .list-products-item .img {
            width: 60px;
            height: 60px;
            flex: 0 0 60px;
        }
        
        @media (min-width: 992px) {
            .w-lg-auto {
                width: auto!important;
            }
        }
    </style>
    <style>
.imgbb-control{position:relative;height:40px;line-height:40px;overflow:hidden;background:#e7e7e7;color:#FFF;border-radius:0.4rem;}
.imgbb-iconSelect{background-position:0 -16px;display:inline-block;background:url(https://i.imgur.com/e9GpBzF.png) no-repeat center center;width:16px;height:16px;float:left;margin:12px}
.imgbb-add{cursor:pointer;position:absolute;width:140px;height:40px;overflow:hidden;background:#27ad60;left:0;top:0;z-index:1}
.imgbb-choose{cursor:pointer;z-index:10;opacity:0;filter:alpha(opacity=0);-moz-opacity:0;font-size:300px;height:1000px;right:0;top:0;position:absolute;cursor:pointer}
.imgbb-list{white-space:nowrap;overflow-x:auto;margin-top:5px}
.imgbb-list .imgbb-i{vertical-align:top;width:100px;height:100px;border:1px solid #eee;display:inline-flex;padding:10px;position:relative;margin:0 10px 10px 0}
.imgbb-list .imgbb-i img{margin:auto;max-width:100%;max-height:100%}
.imgbb-remove{position:absolute;top:0;right:0;width:15px;height:15px;line-height:15px;text-align:center;color:#fff;background:red;cursor:pointer}
.imgbb-loading{position:absolute;right:10px;display:none}
</style>
     
    </script>
<style>
.startbar{display:none}.startbar:has(.simplebar-wrapper){display:block}
</style>

    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/11.7.12/sweetalert2.min.css">
    {!! site('script_head') !!}
    
</head>

<body>
    {!! site('script_body') !!}
    <!-- leftbar-tab-menu -->
<div class="startbar d-print-none">
    <!--start brand-->
    <div class="brand" style="width:100%;display:flex;align-items:center;justify-content:center;overflow:hidden">
        <a href="{{ route('home') }}" class="logo">
            <span>
                <img src="{{ siteValue('logo') }}" alt="{{ siteValue('name_site') }}" class="logo-xxl" style="display:flex;">
            </span>
            
        </a>
    </div>

    <div class="startbar-menu">
        <div class="startbar-collapse" id="startbarCollapse" data-simplebar>
            <div class="d-flex align-items-start flex-column w-100">
                <ul class="navbar-nav mb-auto w-100">
                    @if (Auth::check() && Auth::user()->role === 'admin')
                    
                    <li class="menu-label">
                        <span>Quản Trị</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('admin.dashboard') }}" title="Trang Quản Trị">
                            <i class="iconoir-home-alt menu-icon"></i>

                            <span class="">Trang Quản Trị</span>
                        </a>
                    </li>
                @endif
                    
                    <li class="menu-label">
                        <span>Menu</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('home') }}" title="Trang Chủ">
                            <i class="iconoir-home-alt menu-icon"></i>

                            <span class="">Trang Chủ</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="#info" data-bs-toggle="collapse" role="button"
                            aria-expanded="false" aria-controls="info">
                            <i class="iconoir-group menu-icon"></i>

                            <span>Thông Tin</span>
                        </a>
                        <div class="collapse" id="info">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('account.profile') }}"
                                        title="Thông Tin Tài Khoản">
                                        <i class="iconoir-user menu-icon"></i>

                                        <span class="">Thông Tin Tài Khoản</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('account.transactions') }}"
                                        title="Lịch Sử Giao Dịch">
                                        <i class="iconoir-reports menu-icon"></i>

                                        <span class="">Lịch Sử Giao Dịch</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('account.progress') }}"
                                        title="Đơn Hàng Đã Mua">
                                        <i class="iconoir-stats-down-square menu-icon"></i>

                                        <span class="">Đơn Hàng Đã Mua</span>
                                    </a>
                                </li>
                                
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('client.product.bought') }}"
                                        title="Sản Phẩm Đã Mua">
                                        <i class="iconoir-cart-alt menu-icon"></i>
                                        <span class="">Sản Phẩm Đã Mua</span>
                                    </a>
                                </li>
                      
                                 
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="#api_partners" data-bs-toggle="collapse"
                            role="button" aria-expanded="false" aria-controls="api_partners">
                            <i class="iconoir-community menu-icon"></i>

                            <span>API &amp; Đối Tác</span>
                        </a>
                        <div class="collapse" id="api_partners">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('account.services') }}"
                                        title="Bảng Giá &amp; Cấp Bậc">
                                        <i class="iconoir-network menu-icon"></i>

                                        <span class="">Bảng Giá &amp; Cấp Bậc</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('create.website') }}"
                                        title="Tạo WebSite Đại Lý">
                                        <i class="iconoir-hexagon-dice menu-icon"></i>

                                        <span class="">Tạo WebSite Đại Lý</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('api') }}"
                                        title="Tài Liệu API V2">
                                        <i class="iconoir-mobile-voice menu-icon"></i>

                                        <span class="">Tài Liệu API V2</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('account.recharge') }}"
                            title="Nạp Tiền Tài Khoản">
                            <i class="iconoir-bank menu-icon"></i>

                            <span class="">Nạp Tiền Tài Khoản</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('ticket') }}"
                            title="Phiếu Hỗ Trợ">

                            <i class="iconoir-page-flip menu-icon"></i>
                            <span class="">Phiếu Hỗ Trợ</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('rule') }}"
                            title="Điều Khoản & Quy Tắc">

                            <i class="iconoir-book menu-icon"></i>
                            <span class="">Điều Khoản & Quy Tắc</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="#mmo" data-bs-toggle="collapse" role="button"
                            aria-expanded="false" aria-controls="mmo">
                            <i class="iconoir-dollar-circle menu-icon"></i>
                            <span>Kiếm Tiền Online</span>
                        </a>
                        <div class="collapse" id="mmo">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg" href="{{ route('affiliate') }}"
                                        title="Tiếp Thị Liên Kết">
                                        <i class="iconoir-coins menu-icon"></i>

                                        <span class="">Tiếp Thị Liên Kết</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="menu-label mt-2">
                        <span>Dịch Vụ</span>
                    </li>
                    
                    
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('client.product.categories') }}"
                            title="Sản Phẩm">
                            <i class="iconoir-cart-alt menu-icon"></i>
                            <span class="">Sản Phẩm</span>
                        </a>
                    </li>
                   
                     @if (site('status_massorder') == 'on')
                     <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="{{ route('mass') }}"
                            title="Sản Phẩm">
                            <i class="iconoir-flower menu-icon"></i>
                            <span class="">Đặt Đơn Số Lượng Lớn</span>
                        </a>
                    </li>
                     
                    @endif
                    
                    @foreach (\App\Models\ServicePlatform::where('domain', env('APP_MAIN_SITE'))->where('status', 'active')->orderBy('order', 'asc')->get() as $platform)
                    <li class="nav-item">
                        <a class="nav-link primary-hover-bg" href="#{{ $platform->image }}" data-bs-toggle="collapse" role="button"
                            aria-expanded="false" aria-controls="{{ $platform->image }}">
                            <img src="{{ $platform->image }}" alt="{{ siteValue('name_site') }}" class="menu-icon" width="25"
                                height="25" loading="lazy" />
                            <span>{{ $platform->name }}</span>
                        </a>
                        <div class="collapse" id="{{ $platform->image }}">
                            <ul class="nav flex-column">
                               @foreach ($platform->services as $service)
                                <li class="nav-item">
                                    <a class="nav-link primary-hover-bg"
                                        href="{{ route('service', ['service' => $service->slug, 'platform' => $platform->slug]) }}" title="{{ $service->name }}">
                                        <span class="">{{ $service->name }}</span>
                                    </a>
                                </li>
                                
                                @endforeach
                                
                            </ul>
                        </div>
                    </li>
                                @endforeach
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="startbar-overlay d-print-none"></div>
<!-- end leftbar-tab-menu-->

<div class="page-wrapper min-vh-100">

    <!-- Page Content-->
    <div class="page-content">
        <div class="container-fluid">
            <!-- Top Bar Start -->
            <div class="topbar d-print-none">
                <div class="container-fluid">
                    <nav class="topbar-custom d-flex justify-content-between" id="topbar-custom">
                        <ul class="topbar-item list-unstyled d-inline-flex align-items-center mb-0">
                            <li>
                                <button class="nav-link mobile-menu-btn nav-icon" id="togglemenu" aria-label="Menu">
                                    <i class="iconoir-menu"></i>
                                </button>
                            </li>
                        </ul>
                        <ul class="topbar-item list-unstyled d-inline-flex align-items-center mb-0">


 
                            @if(Auth::user())
                            <li class="topbar-item">
                                <div class="text-end">
                                    <p class="mb-0">Xin chào, <strong>{{Auth::user()->username ?? 'Bạn chưa đăng nhập' }}</strong></p>
                                    <small>Số dư: <strong>{{ number_format(Auth::user()->balance ?? 0) ?: '0₫' }} </strong></small>
                                </div>
                            </li>
                            <li class="dropdown topbar-item">
                                <a class="nav-link dropdown-toggle arrow-none nav-icon" data-bs-toggle="dropdown"
                                    href="#" role="button" aria-haspopup="false" aria-expanded="false"
                                    data-bs-offset="0,19">
                                    <img src="https://ui-avatars.com/api/?name={{Auth::user()->username ?? 'Bạn chưa đăng nhập' }}" alt="Avatar"
                                        class="thumb-md rounded-circle">
                                </a>
                                <div class="dropdown-menu dropdown-menu-end py-0">
                                    <div class="d-flex align-items-center dropdown-item py-2 bg-secondary-subtle">
                                        <div class="flex-shrink-0">
                                            <img src="https://ui-avatars.com/api/?name={{Auth::user()->username ?? 'Bạn chưa đăng nhập' }}" alt="Avatar"
                                                class="thumb-md rounded-circle">
                                        </div>
                                        <div class="flex-grow-1 ms-2 text-truncate align-self-center">
                                            <h6 class="my-0 fw-medium text-dark fs-13">{{Auth::user()->username ?? 'Bạn chưa đăng nhập' }}</h6>
                                            <small class="text-muted mb-0">{{ levelUser(Auth::user()->level ?? 'Thành viên') }}</small>
                                        </div>
                                    </div>
                                    <div class="dropdown-divider mt-0"></div>
                                    <small class="text-muted px-2 pb-1 d-block">Tài khoản</small>
                                    <a class="dropdown-item" href="{{ route('home') }}/user/profile"><i
                                            class="las la-user fs-18 me-1 align-text-bottom"></i> Thông tin tài
                                        khoản</a>
                                    <a class="dropdown-item" href="{{ route('account.profile') }}"><i
                                            class="las la-wallet fs-18 me-1 align-text-bottom"></i> Nạp tiền tài
                                        khoản</a>
                                    <div class="dropdown-divider mb-0"></div>
                                    <a class="dropdown-item text-danger" href="{{ route('logout') }}"><i
                                            class="las la-power-off fs-18 me-1 align-text-bottom"></i> Đăng xuất</a>
                                </div>
                            </li>
                            @else
                            <li class="topbar-item">
                            <div class="text-end">
                                <a class="btn btn-outline-primary me-1" href="/auth/login">Đăng nhập</a>
                                 <a class="btn btn-outline-warning" href="/auth/register">Đăng ký</a>
                                                        </div>
                        </li>
                            @endif

                            

                        </ul><!--end topbar-nav-->
                    </nav>
                    <!-- end navbar-->
                </div>
            </div>
            <!-- Top Bar End -->
            <div class="row pb-3">
                <div class="col-sm-12 d-none">
                    <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                        <div class="page-title">Phiếu hỗ trợ</div>
                        <div class="d-none">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="{{ route('home') }}">{{ siteValue('name_site') }}</a>
                                </li><!--end nav-item-->
                                <li class="breadcrumb-item active">Phiếu hỗ trợ</li>
                            </ol>
                        </div>
                    </div><!--end page-title-box-->
                </div><!--end col-->
            </div><!--end row-->
            @yield('content')
        </div><!-- container -->


        <div class="pb-2"></div>
        <footer class="footer text-center text-sm-start d-print-none">
            <center>
                <div class="container-fluids">
                    <div class="card mb-0 rounded-bottom-0 border-bottom:0">
                        <div class="card-body">
                            <p class="text-muted mb-0">
                                ©
                                <script>
                                    document.write(new Date().getFullYear())
                                </script>
                                - {{ siteValue('name_site') }} - All Rights Reserved.
                            </p>
                        </div>
                    </div>
                </div>
            </center>
        </footer>
        <!--end footer-->
    </div>
    <!-- end page content -->
</div>
<!-- end page-wrapper -->

     
     
     
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="/theme/js/bootstrap.bundle.min.js"></script>
    <script src="/theme/js/simplebar.min.js"></script>

    <script src="//cdn.datatables.net/2.2.0/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.2.0/js/dataTables.bootstrap5.js"></script>

    <script src="/theme/js/app.js?v=1.0.2"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script src="/app/js/plugins/dataTables.min.js"></script>
    <script src="/app/js/plugins/dataTables.bootstrap5.min.js"></script>
    
<script src="/app/js/app.js?duy-time={{ time() }}"></script> 
    

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
    {!! site('script_footer') !!}
    @if (session('success'))
        <script>
            Swal.fire({
                title: 'Thành công',
                text: '{{ session('success') }}',
                icon: 'success',
                confirmButtonText: 'Đóng',
                
            })
        </script>
    @endif
    @if (session('error'))
        <script>
            Swal.fire({
                title: 'Lỗi',
                text: '{{ session('error') }}',
                icon: 'error',
                confirmButtonText: 'Đóng',
                
            })
        </script>
    @endif

    @if (Auth::check())
        <script>
            const handleLoadRecharged = () => {
                $.ajax({
                    url: "/account/action-mode?action=recharged",
                    type: "GET",
                    dataType: "json",
                    success: function(response) {
                        if (response.status == 'success') {
                            Swal.fire({
                                title: 'Thành công',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'Đóng',
                                 
                            })
                        }
                    }
                })
            }
        </script>
<script>
   

const loadTable = (elm, key, columns, url = null) => {
    const dtable = $(elm).DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            url: url ? url : '/user/get-data?model=' + key,
            type: 'GET',
            data: {
                key: key
            },
            error: function (xhr, error, code) {
                Swal.fire("Error", "Có lỗi xảy ra khi tải dữ liệu, vui lòng thử lại sau", "error");
            }
        },
        columns: columns,
        "order": [[0, "desc"]],
        "language": {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sSearch": "Tìm kiếm:",
            "sEmptyTable": "Không có dữ liệu",
        },
        // "scrollX": true,
        "lengthMenu": [
            [10, 25, 50, 100, -1],
            [10, 25, 50, 100, "Tất cả"]
        ],
    });


    return dtable;
}  
const formatCurrency = (value) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value);
}

const formatDate = (date) => {
    return new Date(date).toLocaleString();
}

const formatNumber = (value) => {
    return new Intl.NumberFormat('vi-VN').format(value);
}
</script>
        <script>
            $(document).ready(function() {
                setInterval(() => {
                    handleLoadRecharged()
                }, 5000);
            })
        </script>
    @endif


    @yield('script')
    <script>
        $('#data-table').DataTable();
        $('#jsource-table').DataTable({
            data: dataSet,
        });
    </script>
</body>

</html>
