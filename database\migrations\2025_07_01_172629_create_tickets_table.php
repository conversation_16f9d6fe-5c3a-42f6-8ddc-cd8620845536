<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('username')->comment('ID của user tạo ticket');
            $table->string('title')->comment('Tiêu đề ticket');
            $table->longText('body')->comment('Nội dung ticket');
            $table->enum('level', ['Thấp', 'Trung Bình', 'Cao', 'Khẩn Cấp'])->default('Thấp')->comment('Độ ưu tiên');
            $table->enum('category', ['thanh_toan', 'dich_vu', 'don_hang', 'webcon', 'khac'])
                  ->default('khac')
                  ->comment('Danh mục hỗ trợ: thanh_toan, dich_vu, don_hang, webcon, khac');
            $table->unsignedBigInteger('order_id')->nullable()->comment('ID đơn hàng (chỉ dùng khi category = don_hang)');
            $table->enum('status', ['pending', 'processing', 'success', 'cancelled'])->default('pending')->comment('Trạng thái ticket');
            $table->longText('reply')->nullable()->comment('Nội dung phản hồi từ admin');
            $table->string('domain')->nullable()->comment('Domain của website');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('username')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');

            // Indexes
            $table->index(['username', 'domain']);
            $table->index(['category', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
