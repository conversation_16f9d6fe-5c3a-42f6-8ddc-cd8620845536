@extends('admin.layouts.app')
@section('title', 'Quản lí sản phẩm')
@section('content')
    <div class="card custom-card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title"><PERSON><PERSON> sách sản phẩm</h4>
            
                            @if (request()->getHost() === env('APP_MAIN_SITE'))
                <a href="{{ route('admin.products.create') }}" class="btn btn-primary">Thêm sản phẩm</a>
            @endif
        </div>
        <div class="card-body">
            <div class="table-repsonsive">
                <table class="table w-100" id="table-products-main">
                    <thead>
                        <tr>
                            <th>#</th>
                            
                            @if (request()->getHost() === env('APP_MAIN_SITE'))
                                <th><PERSON><PERSON> tác</th>
                            @endif
                            <th>T<PERSON><PERSON> sản phẩm</th>
                            <th>Tr<PERSON>ng thái</th>
                            <th><PERSON><PERSON> b<PERSON></th>
                            <th><PERSON><PERSON><PERSON>t xem</th>
                            <th>Th<PERSON><PERSON> gian</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        $(document).ready(function() {
            loadDatatable('#table-products-main', 'products-main', [{
                    data: 'id',
                    name: 'id'
                },
                
                            @if (request()->getHost() === env('APP_MAIN_SITE'))

                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return `
                            <a href="{{ url('admin/products') }}/edit/${row.id}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-danger btn-delete" onclick="deleteData(${row.id}, 'products-main')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                        }
                    },
                @endif {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        return data == 'active' ? '<span class="badge bg-success">Hiển thị</span>' :
                            '<span class="badge bg-danger">Ẩn</span>';
                    }
                },
                {
                    data: 'sold',
                    name: 'sold',
                    render: function(data) {
                        return formatNumber(data);
                    }
                },
                {
                    data: 'view',
                    name: 'view',
                    render: function(data) {
                        return formatNumber(data);
                    }
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY HH:mm:ss');
                    }
                }
            ])
        });
    </script>
@endsection
