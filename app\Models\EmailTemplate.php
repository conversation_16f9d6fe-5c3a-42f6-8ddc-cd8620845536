<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'domain',
        'type',
        'subject',
        'content',
        'placeholders',
        'status'
    ];

    protected $casts = [
        'placeholders' => 'array'
    ];

    // Các loại email template
    const TYPE_LOGIN_NOTIFICATION = 'login_notification';
    const TYPE_PASSWORD_RESET = 'password_reset';
    const TYPE_PRODUCT_DELIVERY = 'product_delivery';

    // Các placeholder mặc định cho từng loại
    const DEFAULT_PLACEHOLDERS = [
        self::TYPE_LOGIN_NOTIFICATION => [
            '{domain}' => 'Link Website',
            '{title}' => 'Tên website',
            '{username}' => 'Tên khách hàng',
            '{ip}' => 'Địa chỉ IP',
            '{device}' => 'Thiết bị',
            '{time}' => 'Thời gian'
        ],
        self::TYPE_PASSWORD_RESET => [
            '{domain}' => 'Link Website',
            '{title}' => 'Tên website',
            '{username}' => 'Tên khách hàng',
            '{link}' => 'Link xác minh',
            '{ip}' => 'Địa chỉ IP',
            '{device}' => 'Thiết bị',
            '{time}' => 'Thời gian'
        ],
        self::TYPE_PRODUCT_DELIVERY => [
            '{domain}' => 'Link Website',
            '{title}' => 'Tên website',
            '{username}' => 'Tên khách hàng',
            '{product_name}' => 'Tên sản phẩm',
            '{product_data}' => 'Dữ liệu sản phẩm',
            '{order_id}' => 'Mã đơn hàng',
            '{time}' => 'Thời gian'
        ]
    ];

    // Nội dung mặc định cho từng loại
    const DEFAULT_CONTENT = [
        self::TYPE_LOGIN_NOTIFICATION => [
            'subject' => 'Cảnh báo đăng nhập tài khoản - {title}',
            'content' => '<div style="background-color:#f5f5f5; color:#333333; font-family:\'Roboto\',sans-serif; line-height:1.6; padding:20px">
<div style="background:white; border-radius:8px; box-shadow:0 2px 10px rgba(0,0,0,0.1); margin-bottom:auto; margin-left:auto; margin-right:auto; margin-top:auto; max-width:600px; overflow:hidden">
<div style="padding:20px">
<h2>Thông báo đăng nhập mới</h2>

<p>Xin chào <strong>{username}</strong>,</p>

<p>Chúng tôi muốn thông báo cho bạn rằng tài khoản của bạn trên <strong>{domain}</strong> vừa có một đăng nhập mới.</p>

<p><strong>Thông tin đăng nhập:</strong></p>

<ul style="list-style-type:none">
	<li><strong>Địa chỉ IP:</strong> {ip}</li>
	<li><strong>Thiết bị:</strong> {device}</li>
	<li><strong>Thời gian:</strong> {time}</li>
</ul>

<p>Nếu bạn không thực hiện đăng nhập này, xin vui lòng kiểm tra tài khoản của bạn ngay lập tức. Để bảo vệ tài khoản của bạn, chúng tôi khuyến nghị bạn thay đổi mật khẩu ngay.</p>

<p>Bạn cũng có thể xem lịch sử đăng nhập và quản lý thiết bị từ trang quản lý tài khoản của bạn.</p>

<p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>

<p>Trân trọng,<br />
Đội ngũ hỗ trợ của {domain}</p>
</div>
</div>
</div>'
        ],
        self::TYPE_PASSWORD_RESET => [
            'subject' => 'Xác nhận khôi phục mật khẩu website - {title}',
            'content' => '<div style="background-color:#f5f5f5; font-family:\'Roboto\',sans-serif; padding:20px">
<div style="background-color:#ffffff; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1); margin-bottom:auto; margin-left:auto; margin-right:auto; margin-top:auto; max-width:600px; padding:30px">
<h2>Chào {username},</h2>

<p>Chúng tôi đã nhận được yêu cầu đặt lại mật khẩu của bạn trên <strong>{domain}</strong>. Để hoàn tất quá trình này, vui lòng nhấn vào liên kết bên dưới để đặt lại mật khẩu của bạn:</p>

<div style="margin-bottom:30px; text-align:center"><a href="{link}" style="background-color: #2196F3; color: #ffffff; text-decoration: none; padding: 12px 24px; border-radius: 4px; font-weight: 500; font-size: 16px; transition: background-color 0.3s;">Đặt lại mật khẩu </a></div>

<p>Lưu ý:</p>

<ul style="margin-left:0; margin-right:0">
	<li>Liên kết này chỉ có hiệu lực trong vòng 30 phút kể từ thời điểm gửi email.</li>
	<li>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này để đảm bảo an toàn cho tài khoản của bạn.</li>
	<li>Không chia sẻ mã hoặc liên kết này với bất kỳ ai để tránh rủi ro về bảo mật.</li>
</ul>

<p>&nbsp;</p>

<p>Thông tin truy cập của bạn:</p>

<ul style="margin-left:0; margin-right:0">
	<li>IP: {ip}</li>
	<li>Thiết bị: {device}</li>
	<li>Thời gian: {time}</li>
</ul>

<p>&nbsp;</p>

<p>Trân trọng,<br />
Đội ngũ hỗ trợ của {domain}</p>
</div>
</div>'
        ],
        self::TYPE_PRODUCT_DELIVERY => [
            'subject' => 'Thông báo giao hàng sản phẩm - {title}',
            'content' => '<div style="background-color:#f5f5f5; font-family:\'Roboto\',sans-serif; padding:20px">
<div style="background-color:#ffffff; border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1); margin-bottom:auto; margin-left:auto; margin-right:auto; margin-top:auto; max-width:600px; padding:30px">
<h2>Chào {username},</h2>

<p>Đơn hàng <strong>#{order_id}</strong> của bạn đã được xử lý thành công!</p>

<p><strong>Thông tin sản phẩm:</strong></p>
<p>Tên sản phẩm: <strong>{product_name}</strong></p>
<p>Dữ liệu: {product_data}</p>

<p>Thời gian giao hàng: {time}</p>

<p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>

<p>Trân trọng,<br />
Đội ngũ hỗ trợ của {domain}</p>
</div>
</div>'
        ]
    ];

    /**
     * Lấy template theo domain và type
     */
    public static function getTemplate($domain, $type)
    {
        return self::where('domain', $domain)
            ->where('type', $type)
            ->where('status', 'active')
            ->first();
    }

    /**
     * Lấy hoặc tạo template mặc định
     */
    public static function getOrCreateDefault($domain, $type)
    {
        $template = self::getTemplate($domain, $type);
        
        if (!$template) {
            $defaultContent = self::DEFAULT_CONTENT[$type] ?? null;
            if ($defaultContent) {
                $template = self::create([
                    'domain' => $domain,
                    'type' => $type,
                    'subject' => $defaultContent['subject'],
                    'content' => $defaultContent['content'],
                    'placeholders' => self::DEFAULT_PLACEHOLDERS[$type] ?? [],
                    'status' => 'active'
                ]);
            }
        }
        
        return $template;
    }

    /**
     * Thay thế placeholders trong content
     */
    public function replacePlaceholders($data)
    {
        $subject = $this->subject;
        $content = $this->content;
        
        foreach ($data as $placeholder => $value) {
            $subject = str_replace($placeholder, $value, $subject);
            $content = str_replace($placeholder, $value, $content);
        }
        
        return [
            'subject' => $subject,
            'content' => $content
        ];
    }

    /**
     * Kiểm tra template có được bật không (subject không null)
     */
    public function isEnabled()
    {
        return !empty($this->subject);
    }
}
