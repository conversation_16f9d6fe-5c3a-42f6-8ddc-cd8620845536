 
<?php $__env->startSection('title', 'Sản phẩm'); ?>
<?php $__env->startSection('style'); ?>
    <style>
        .card-hover-top {
            transition: 0.4s;
        }

        .card-hover-top:hover {
            transform: translateY(-8px);
            transition: 0.4s;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productMain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-3 col-xxl-2">
                <a href="<?php echo e(route('client.product', ['slug' => $productMain->slug])); ?>">
                    <div class="card shadow card-hover-top overflow-hidden">
                        <img src="<?php echo e(asset($productMain->image)); ?>" class="card-img-top" alt="<?php echo e($productMain->name); ?>">
                        <div class="card-body text-center p-2">
                            <h5 class="card-title fs-14 mb-2 twoline">
                                                                <?php echo e($productMain->name); ?>

                                                            </h5>
                            <strong class="text-primary fs-12">
                                <?php
                                    $activeProducts = $productMain->product->where('domain', request()->getHost())->where('status', 'active');
                                ?>
                                <span class="text-danger">
                                    <?php if($activeProducts->count() > 1): ?>
                                        <span class="text-danger"><?php echo e(number_format($productMain->getPriceStart())); ?> đ</span> -
                                        <span class="text-danger"><?php echo e(number_format($productMain->getPriceEnd() ?? 0)); ?> đ</span>
                                    <?php elseif($activeProducts->count() == 1): ?>
                                        <span class="text-danger"><?php echo e(number_format($productMain->getPriceStart() ?? 0)); ?> đ</span>
                                    <?php else: ?>
                                        <span class="text-muted">Liên hệ</span>
                                    <?php endif; ?>
                                </span>
                            </strong>
                        </div>
                    </div>
                </a>
            </div>
            
             
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/muasub.online/resources/views/guard/product/categories.blade.php ENDPATH**/ ?>