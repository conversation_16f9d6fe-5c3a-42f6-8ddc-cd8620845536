<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique('email');
                $table->string('username')->unique('username');
                $table->string('password');
                $table->enum('role', ['member', 'admin'])->default('member')->comment('Chức vụ của người dùng');
                $table->enum('level', ['member', 'collaborator', 'agency', 'distributor'])->default('member')->comment('Cấp độ của người dùng');
                $table->longText('balance')->comment('Số dư tài khoản');
                $table->longText('total_recharge')->comment('Tổng số dư tài khoản');
                $table->enum('status', ['active', 'inactive', 'banned'])->default('active')->comment('Trạng thái của người dùng');
                $table->string('facebook')->nullable()->comment('Đường dẫn tới facebook của người dùng');
                $table->string('discord_link')->nullable()->comment('Đường dẫn tới discord của người dùng');
                $table->string('discord_id')->nullable()->comment('ID discord của người dùng');
                $table->enum('notification_email', ['yes', 'no'])->default('yes')->comment('Cho phép gửi thông báo qua email');
                $table->enum('notification_discord', ['yes', 'no'])->default('yes')->comment('Cho phép gửi thông báo qua discord');
                $table->enum('two_factor_auth', ['yes', 'no'])->default('no')->comment('Cho phép xác thực 2 yếu tố');
                $table->string('two_factor_secret')->nullable()->comment('Mã bí mật xác thực 2 yếu tố');
                $table->string('avatar')->nullable()->comment('Ảnh đại diện của người dùng');
                $table->longText('api_token')->nullable()->comment('Token của người dùng');
                $table->string('last_login')->nullable()->comment('Thời gian đăng nhập cuối cùng');
                $table->string('last_ip')->nullable()->comment('Địa chỉ IP đăng nhập cuối cùng');
                $table->rememberToken();
                $table->timestamps();
                $table->string('domain')->nullable()->comment('Tên miền của người dùng');
            });
        }

        if (!Schema::hasTable('password_reset_tokens')) {
            Schema::create('password_reset_tokens', function (Blueprint $table) {
                $table->string('email')->primary();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
            });
        }

        if (!Schema::hasTable('sessions')) {
            Schema::create('sessions', function (Blueprint $table) {
                $table->string('id')->primary();
                $table->foreignId('user_id')->nullable()->index();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->longText('payload');
                $table->integer('last_activity')->index();
            });
        }

        if (!Schema::hasTable('email_templates')) {
            Schema::create('email_templates', function (Blueprint $table) {
                $table->id();
                $table->string('domain'); // Domain của website
                $table->string('type'); // login_notification, password_reset, product_delivery
                $table->string('subject')->nullable(); // Tiêu đề email, null = tắt thông báo
                $table->longText('content')->nullable(); // Nội dung HTML email
                $table->json('placeholders')->nullable(); // Danh sách các placeholder có thể dùng
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();

                $table->unique(['domain', 'type']); // Mỗi domain chỉ có 1 template cho mỗi type
                $table->index(['domain', 'status']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};

