<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_main_id',
        'name',
        'price',
        'rate',
        'child_id',
        'status',
        'order',
        'type',
        'stock',
        'providerToken',
        'productKey',
        'domain'
    ];

    public function productMain(){
        return $this->belongsTo(ProductMain::class);
    }

    public function inventory(){
        return $this->hasMany(ProductInventory::class);
    }


    public function scopeSearch($query, $search){
        return $query->where('name', 'like', '%'.$search.'%');
    }
}
