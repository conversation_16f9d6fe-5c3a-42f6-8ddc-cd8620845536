/**
 * TinyMCE version 6.8.3 (2024-02-08)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>I(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},V=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},H=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(H(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},Y=(e,t)=>q(L(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},K=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Mo=/^\s+|\s+$/g,e=>e.replace(Mo,"")),De=e=>e.length>0,Be=e=>!De(e),Ie=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Re=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Ne=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Ve=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},ze=Fe,Le="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:Le;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Pe=Object.getPrototypeOf,Ue=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Pe(e).constructor.name))},We=e=>e.dom.nodeName.toLowerCase(),je=e=>t=>(e=>e.dom.nodeType)(t)===e,Ge=e=>$e(e)&&Ue(e.dom),$e=je(1),qe=je(3),Ye=je(9),Xe=je(11),Ke=e=>t=>$e(t)&&We(t)===e,Je=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ze=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>e.dom===t.dom,et=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},tt=e=>ze(e.dom.ownerDocument),ot=e=>Ye(e)?e:tt(e),nt=e=>ze(ot(e).dom.documentElement),st=e=>ze(ot(e).dom.defaultView),rt=e=>A.from(e.dom.parentNode).map(ze),at=e=>A.from(e.dom.parentElement).map(ze),it=e=>A.from(e.dom.offsetParent).map(ze),lt=e=>L(e.dom.childNodes,ze),ct=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},dt=e=>ct(e,0),ut=(e,t)=>({element:e,offset:t}),mt=(e,t)=>{const o=lt(e);return o.length>0&&t<o.length?ut(o[t],0):ut(e,t)},gt=e=>Xe(e)&&g(e.dom.host),pt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ht=x(pt),ft=pt?e=>ze(e.dom.getRootNode()):ot,bt=e=>gt(e)?e:ze(ot(e).dom.body),vt=e=>{const t=ft(e);return gt(t)?A.some(t):A.none()},yt=e=>ze(e.dom.host),xt=e=>{const t=qe(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return vt(ze(t)).fold((()=>o.body.contains(t)),(n=xt,s=yt,e=>n(s(e))));var n,s},wt=()=>St(ze(document)),St=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},kt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Ct=(e,t,o)=>{kt(e.dom,t,o)},Ot=(e,t)=>{const o=e.dom;le(t,((e,t)=>{kt(o,t,e)}))},_t=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Tt=(e,t)=>A.from(_t(e,t)),Et=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},At=(e,t)=>{e.dom.removeAttribute(t)},Mt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ie(e)&&e.style.setProperty(t,o)},Dt=(e,t)=>{Ie(e)&&e.style.removeProperty(t)},Bt=(e,t,o)=>{const n=e.dom;Mt(n,t,o)},It=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Mt(o,t,e)}))},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Dt(o,t)}),(e=>{Mt(o,t,e)}))}))},Rt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||xt(e)?n:Nt(o,t)},Nt=(e,t)=>Ie(e)?e.style.getPropertyValue(t):"",Vt=(e,t)=>{const o=e.dom,n=Nt(o,t);return A.from(n).filter((e=>e.length>0))},zt=e=>{const t={},o=e.dom;if(Ie(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Lt=(e,t,o)=>{const n=Ne(e);return Bt(n,t,o),Vt(n,t).isSome()},Ht=(e,t)=>{const o=e.dom;Dt(o,t),xe(Tt(e,"style").map(Me),"")&&At(e,"style")},Pt=e=>e.dom.offsetWidth,Ut=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Rt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Rt(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ie(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Wt=Ut("height",(e=>{const t=e.dom;return xt(e)?t.getBoundingClientRect().height:t.offsetHeight})),jt=e=>Wt.get(e),Gt=e=>Wt.getOuter(e),$t=(e,t)=>({left:e,top:t,translate:(o,n)=>$t(e+o,t+n)}),qt=$t,Yt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Xt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return qt(o.offsetLeft,o.offsetTop);const r=Yt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Yt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Yt(s.clientTop,o.clientTop),l=Yt(s.clientLeft,o.clientLeft);return Kt(e).translate(a-l,r-i)},Kt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?qt(o.offsetLeft,o.offsetTop):xt(e)?(e=>{const t=e.getBoundingClientRect();return qt(t.left,t.top)})(t):qt(0,0)},Jt=Ut("width",(e=>e.dom.offsetWidth)),Zt=e=>Jt.get(e),Qt=e=>Jt.getOuter(e),eo=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},to=()=>oo(0,0),oo=(e,t)=>({major:e,minor:t}),no={nu:oo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?to():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return oo(n(1),n(2))})(e,o)},unknown:to},so=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},ro=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ao=e=>t=>Te(t,e),io=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ro],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[ro,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ao("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ao("firefox")},{name:"Safari",versionRegexes:[ro,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],lo=[{name:"Windows",search:ao("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ao("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ao("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ao("linux"),versionRegexes:[]},{name:"Solaris",search:ao("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ao("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ao("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],co={browsers:x(io),oses:x(lo)},uo="Edge",mo="Chromium",go="Opera",po="Firefox",ho="Safari",fo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(uo),isChromium:n(mo),isIE:n("IE"),isOpera:n(go),isFirefox:n(po),isSafari:n(ho)}},bo=()=>fo({current:void 0,version:no.unknown()}),vo=fo,yo=(x(uo),x(mo),x("IE"),x(go),x(po),x(ho),"Windows"),xo="Android",wo="Linux",So="macOS",ko="Solaris",Co="FreeBSD",Oo="ChromeOS",_o=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(yo),isiOS:n("iOS"),isAndroid:n(xo),isMacOS:n(So),isLinux:n(wo),isSolaris:n(ko),isFreeBSD:n(Co),isChromeOS:n(Oo)}},To=()=>_o({current:void 0,version:no.unknown()}),Eo=_o,Ao=(x(yo),x("iOS"),x(xo),x(wo),x(So),x(ko),x(Co),x(Oo),e=>window.matchMedia(e).matches);var Mo;let Do=eo((()=>((e,t,o)=>{const n=co.browsers(),s=co.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:no.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>so(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(bo,vo),a=((e,t)=>so(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(To,Eo),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Ao)));const Bo=()=>Do(),Io=e=>{const t=ze((e=>{if(ht()&&g(e.target)){const t=ze(e.target);if($e(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Fo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Io(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(Ro,e,t,r,s)}},Ro=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},No=(e,t)=>{rt(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Vo=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{rt(e).each((e=>{Lo(e,t)}))}),(e=>{No(e,t)}))},zo=(e,t)=>{dt(e).fold((()=>{Lo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Lo=(e,t)=>{e.dom.appendChild(t.dom)},Ho=(e,t)=>{H(t,(t=>{Lo(e,t)}))},Po=e=>{e.dom.textContent="",H(lt(e),(e=>{Uo(e)}))},Uo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Wo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return qt(o,n)},jo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Go=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),$o=e=>{const t=void 0===e?window:e,o=t.document,n=Wo(ze(o));return(e=>{const t=void 0===e?window:e;return Bo().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Go(n.left,n.top,o,s)}),(e=>Go(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},qo=()=>ze(document),Yo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=Yo(e,o);return[t].concat(n)}));var Xo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>tt(e)});const Ko=e=>{const t=qo(),o=Wo(t),n=((e,t)=>{const o=t.owner(e),n=Yo(t,o);return A.some(n)})(e,Xo);return n.fold(k(Xt,e),(t=>{const n=Kt(e),s=W(t,((e,t)=>{const o=Kt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return qt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Jo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Zo=e=>{const t=Xt(e),o=Qt(e),n=Gt(e);return Jo(t.left,t.top,o,n)},Qo=e=>{const t=Ko(e),o=Qt(e),n=Gt(e);return Jo(t.left,t.top,o,n)},en=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Jo(o,n,s-o,r-n)},tn=()=>$o(window);var on=tinymce.util.Tools.resolve("tinymce.ThemeManager");const nn=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>rn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},sn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>rn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},rn={value:nn,error:sn,fromOption:(e,t)=>e.fold((()=>sn(t)),nn)};var an;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(an||(an={}));const ln=(e,t,o)=>e.stype===an.Error?t(e.serror):o(e.svalue),cn=e=>({stype:an.Value,svalue:e}),dn=e=>({stype:an.Error,serror:e}),un=cn,mn=dn,gn=ln,pn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),hn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},fn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},bn=fn(((e,t)=>i(e)&&i(t)?bn(e,t):t)),vn=fn(((e,t)=>t)),yn=e=>({tag:"defaultedThunk",process:e}),xn=e=>yn(x(e)),wn=e=>({tag:"mergeWithThunk",process:e}),Sn=e=>{const t=(e=>{const t=[],o=[];return H(e,(e=>{ln(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(mn,q)(o)):un(t.values);var o},kn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),Cn=(e,t)=>mn([{path:e,getErrorInfo:t}]),On=e=>({extract:(t,o)=>((e,t)=>e.stype===an.Error?t(e.serror):e)(e(o),(e=>((e,t)=>Cn(e,x(t)))(t,e))),toString:x("val")}),_n=On(un),Tn=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),En=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>un(A.none())),(e=>((e,t)=>e.stype===an.Value?{stype:an.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>Cn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+kn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return Tn(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return Tn(o,n,x({}),(t=>{const n=bn(e.process(o),t);return r(n)}))}},An=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Mn=e=>ae(ge(e,g)),Dn=e=>{const t=Bn(e),o=W(e,((e,t)=>hn(t,(t=>bn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Mn(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>Cn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Bn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)hn(r,((o,r,a,i)=>{const l=En(a,e,t,o,i);gn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?mn(s):un(n)})(t,o,e),toString:()=>{const t=L(e,(e=>hn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),In=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return Sn(n)},toString:()=>"array("+e.toString()+")"}),Fn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===an.Value)return{stype:an.Value,svalue:o(e.svalue)};s.push(e)}return Sn(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},Rn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>In(On(e)).extract(t,o))(o,s);return((e,t)=>e.stype===an.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>pn(e,e,{tag:"required",process:{}},t)));return Bn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Nn=y(In,Bn),Vn=x(_n),zn=(e,t)=>On((o=>{const n=typeof o;return e(o)?un(o):mn(`Expected type: ${t} but got: ${n}`)})),Ln=zn(h,"number"),Hn=zn(r,"string"),Pn=zn(d,"boolean"),Un=zn(p,"function"),Wn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Wn(e[t])));default:return!1}},jn=On((e=>Wn(e)?un(e):mn("Expected value to be acceptable for sending via postMessage"))),Gn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>Cn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>Cn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+kn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),$n=e=>On((t=>e(t).fold(mn,un))),qn=(e,t)=>Rn((t=>e(t).fold(dn,cn)),t),Yn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===an.Error?{stype:an.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),ln(n,rn.error,rn.value);var n},Xn=e=>e.fold((e=>{throw new Error(Jn(e))}),w),Kn=(e,t,o)=>Xn(Yn(e,t,o)),Jn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+kn(e.input),Zn=(e,t)=>Gn(e,ce(t,Bn)),Qn=(e,t)=>((e,t)=>{const o=eo(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),es=pn,ts=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),os=e=>$n((t=>R(e,t)?rn.value(t):rn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),ns=e=>es(e,e,{tag:"required",process:{}},Vn()),ss=(e,t)=>es(e,e,{tag:"required",process:{}},t),rs=e=>ss(e,Ln),as=e=>ss(e,Hn),is=(e,t)=>es(e,e,{tag:"required",process:{}},os(t)),ls=e=>ss(e,Un),cs=(e,t)=>es(e,e,{tag:"required",process:{}},Bn(t)),ds=(e,t)=>es(e,e,{tag:"required",process:{}},Nn(t)),us=(e,t)=>es(e,e,{tag:"required",process:{}},In(t)),ms=e=>es(e,e,{tag:"option",process:{}},Vn()),gs=(e,t)=>es(e,e,{tag:"option",process:{}},t),ps=e=>gs(e,Ln),hs=e=>gs(e,Hn),fs=(e,t)=>gs(e,os(t)),bs=e=>gs(e,Un),vs=(e,t)=>gs(e,In(t)),ys=(e,t)=>gs(e,Bn(t)),xs=(e,t)=>es(e,e,xn(t),Vn()),ws=(e,t,o)=>es(e,e,xn(t),o),Ss=(e,t)=>ws(e,t,Ln),ks=(e,t)=>ws(e,t,Hn),Cs=(e,t,o)=>ws(e,t,os(o)),Os=(e,t)=>ws(e,t,Pn),_s=(e,t)=>ws(e,t,Un),Ts=(e,t,o)=>ws(e,t,In(o)),Es=(e,t,o)=>ws(e,t,Bn(o)),As=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Ms=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return H(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Ms([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ds=(e,t)=>((e,t)=>({[e]:t}))(e,t),Bs=e=>(e=>{const t={};return H(e,(e=>{t[e.key]=e.value})),t})(e),Is=e=>p(e)?e:T,Fs=(e,t,o)=>{let n=e.dom;const s=Is(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Rs=(e,t,o)=>{const n=t(e),s=Is(o);return n.orThunk((()=>s(e)?A.none():Fs(e,t,s)))},Ns=(e,t)=>Qe(e.element,t.event.target),Vs={can:E,abort:T,run:b},zs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Vs,...e}},Ls=x,Hs=Ls("touchstart"),Ps=Ls("touchmove"),Us=Ls("touchend"),Ws=Ls("touchcancel"),js=Ls("mousedown"),Gs=Ls("mousemove"),$s=Ls("mouseout"),qs=Ls("mouseup"),Ys=Ls("mouseover"),Xs=Ls("focusin"),Ks=Ls("focusout"),Js=Ls("keydown"),Zs=Ls("keyup"),Qs=Ls("input"),er=Ls("change"),tr=Ls("click"),or=Ls("transitioncancel"),nr=Ls("transitionend"),sr=Ls("transitionstart"),rr=Ls("selectstart"),ar=e=>x("alloy."+e),ir={tap:ar("tap")},lr=ar("focus"),cr=ar("blur.post"),dr=ar("paste.post"),ur=ar("receive"),mr=ar("execute"),gr=ar("focus.item"),pr=ir.tap,hr=ar("longpress"),fr=ar("sandbox.close"),br=ar("typeahead.cancel"),vr=ar("system.init"),yr=ar("system.touchmove"),xr=ar("system.touchend"),wr=ar("system.scroll"),Sr=ar("system.resize"),kr=ar("system.attached"),Cr=ar("system.detached"),Or=ar("system.dismissRequested"),_r=ar("system.repositionRequested"),Tr=ar("focusmanager.shifted"),Er=ar("slotcontainer.visibility"),Ar=ar("system.external.element.scroll"),Mr=ar("change.tab"),Dr=ar("dismiss.tab"),Br=ar("highlight"),Ir=ar("dehighlight"),Fr=(e,t)=>{zr(e,e.element,t,{})},Rr=(e,t,o)=>{zr(e,e.element,t,o)},Nr=e=>{Fr(e,mr())},Vr=(e,t,o)=>{zr(e,t,o,{})},zr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Lr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hr=e=>Bs(e),Pr=(e,t)=>({key:e,value:zs({abort:t})}),Ur=e=>({key:e,value:zs({run:(e,t)=>{t.event.prevent()}})}),Wr=(e,t)=>({key:e,value:zs({run:t})}),jr=(e,t,o)=>({key:e,value:zs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Gr=e=>t=>({key:e,value:zs({run:(e,o)=>{Ns(e,o)&&t(e,o)}})}),$r=(e,t,o)=>((e,t)=>Wr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Lr(t,t.element,e,n)}))})))(e,t.partUids[o]),qr=(e,t)=>Wr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Rs(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Yr=e=>Wr(e,((e,t)=>{t.cut()})),Xr=e=>Wr(e,((e,t)=>{t.stop()})),Kr=(e,t)=>Gr(e)(t),Jr=Gr(kr()),Zr=Gr(Cr()),Qr=Gr(vr()),ea=(aa=mr(),e=>Wr(aa,e)),ta=e=>e.dom.innerHTML,oa=(e,t)=>{const o=tt(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,lt(ze(o))})(t,o);Ho(n,s),Po(e),Lo(e,n)},na=(e,t)=>ze(e.dom.cloneNode(t)),sa=e=>(e=>{if(gt(e))return"#shadow-root";{const t=(e=>na(e,!1))(e);return(e=>{const t=Ne("div"),o=ze(e.dom.cloneNode(!0));return Lo(t,o),ta(t)})(t)}})(e),ra=Hr([((e,t)=>({key:e,value:zs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Qe(t,e.element)&&!Qe(t,o))(e,n,s)||(console.warn(lr()+" did not get interpreted by the desired target. \nOriginator: "+sa(n)+"\nTarget: "+sa(s)+"\nCheck the "+lr()+" event handlers"),!1)}})}))(lr())]);var aa,ia=Object.freeze({__proto__:null,events:ra});let la=0;const ca=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return la++,e+"_"+o+la+String(t)},da=x("alloy-id-"),ua=x("data-alloy-id"),ma=da(),ga=ua(),pa=(e,t)=>{Object.defineProperty(e.dom,ga,{value:t,writable:!0})},ha=e=>{const t=$e(e)?e.dom[ga]:null;return A.from(t)},fa=e=>ca(e),ba=w,va=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+sa(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},ya=va(),xa=e=>L(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),wa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:xa(r)}),e},Sa=ca("alloy-premade"),ka=e=>(Object.defineProperty(e.element.dom,Sa,{value:e.uid,writable:!0}),Ds(Sa,e)),Ca=e=>be(e,Sa),Oa=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:xa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),_a={init:()=>Ta({readState:x("No State required")})},Ta=e=>e,Ea=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Aa=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Ma=e=>e.cHandler,Da=(e,t)=>({name:e,handler:t}),Ba=(e,t)=>{const o={};return H(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ia=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return rn.value(s)}catch(e){return rn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{H(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>rn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},Fa=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return H(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,rn.error(q(n))):((e,t)=>0===e.length?rn.value(t):rn.value(bn(t,vn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?rn.value(e[0].handler):Ia(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ds(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Ra="alloy.base.behaviour",Na=Bn([es("dom","dom",{tag:"required",process:{}},Bn([ns("tag"),xs("styles",{}),xs("classes",[]),xs("attributes",{}),ms("value"),ms("innerHtml")])),ns("components"),ns("uid"),xs("events",{}),xs("apis",{}),es("eventOrder","eventOrder",(ui={[mr()]:["disabling",Ra,"toggling","typeaheadevents"],[lr()]:[Ra,"focusing","keying"],[vr()]:[Ra,"disabling","toggling","representing"],[Qs()]:[Ra,"representing","streaming","invalidating"],[Cr()]:[Ra,"representing","item-events","tooltipping"],[js()]:["focusing",Ra,"item-type-events"],[Hs()]:["focusing",Ra,"item-type-events"],[Ys()]:["item-type-events","tooltipping"],[ur()]:["receiving","reflecting","tooltipping"]},wn(x(ui))),Vn()),ms("domModification")]),Va=e=>e.events,za=(e,t)=>{const o=_t(e,t);return void 0===o||""===o?[]:o.split(" ")},La=e=>void 0!==e.dom.classList,Ha=e=>za(e,"class"),Pa=(e,t)=>((e,t,o)=>{const n=za(e,t).concat([o]);return Ct(e,t,n.join(" ")),!0})(e,"class",t),Ua=(e,t)=>((e,t,o)=>{const n=U(za(e,t),(e=>e!==o));return n.length>0?Ct(e,t,n.join(" ")):At(e,t),!1})(e,"class",t),Wa=(e,t)=>{La(e)?e.dom.classList.add(t):Pa(e,t)},ja=e=>{0===(La(e)?e.dom.classList:Ha(e)).length&&At(e,"class")},Ga=(e,t)=>{La(e)?e.dom.classList.remove(t):Ua(e,t),ja(e)},$a=(e,t)=>La(e)&&e.dom.classList.contains(t),qa=(e,t)=>{H(t,(t=>{Wa(e,t)}))},Ya=(e,t)=>{H(t,(t=>{Ga(e,t)}))},Xa=e=>La(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ha(e),Ka=e=>e.dom.value,Ja=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Za=(e,t,o)=>{o.fold((()=>Lo(e,t)),(e=>{Qe(e,t)||(No(e,t),Uo(e))}))},Qa=(e,t,o)=>{const n=L(t,o),s=lt(e);return H(s.slice(n.length),Uo),n},ei=(e,t,o,n)=>{const s=ct(e,t),r=n(o,s),a=((e,t,o)=>ct(e,t).map((e=>{if(o.exists((t=>!Qe(t,e)))){const t=o.map(We).getOr("span"),n=Ne(t);return No(e,n),n}return e})))(e,t,s);return Za(e,r.element,a),r},ti=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},oi=(e,t)=>{const o=t.filter((t=>We(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,Sa))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=ti(e.attributes,s),i=zt(t),{toSet:l,toRemove:c}=ti(e.styles,i),d=Xa(t),u=J(d,e.classes),m=J(e.classes,d);return H(a,(e=>At(t,e))),Ot(t,r),qa(t,m),Ya(t,u),H(c,(e=>Ht(t,e))),It(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Qa(e,t,((t,o)=>{const n=ct(e,o);return Za(e,t,n),t}))})(t,o)}),(e=>{oa(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Ka(o)&&Ja(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Ne(e.tag);Ot(t,e.attributes),qa(t,e.classes),It(t,e.styles),e.innerHtml.each((e=>oa(t,e)));const o=e.domChildren;return Ho(t,o),e.value.each((e=>{Ja(t,e)})),t})(e)));return pa(o,e.uid),o},ni=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return Y(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>ys(e.name(),[ns("config"),xs("state",_a)]))),n=Yn("component.behaviours",Bn(o),e.behaviours).fold((t=>{throw new Error(Jn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},si=(e,t)=>{const o=()=>m,n=As(ya),s=Xn((e=>Yn("custom.definition",Na,e))(e)),r=ni(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Aa({})),Aa))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};H(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Ea(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Aa({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=oi(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Va(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Ba(t,e)};return Ea(n,Da)})(e,o,n);return Fa(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=As(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(va(o))},element:c,syncComponents:()=>{const e=lt(c),t=Y(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ri=e=>{const t=Ve(e);return ai({element:t})},ai=e=>{const t=Kn("external.component",Dn([ns("element"),ms("uid")]),e),o=As(va()),n=t.uid.getOrThunk((()=>fa("external")));pa(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(va((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return ka(s)},ii=fa,li=(e,t)=>Ca(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=ba(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>L(o,ci)),(e=>L(o,((t,o)=>li(t,ct(e,o))))))})(n,t),r={...n,events:{...ia,...o},components:s};return rn.value(si(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:ii(""),...e},t).getOrDie())),ci=e=>li(e,A.none()),di=ka;var ui,mi=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const gi=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},pi=(e,t,o)=>mi(((e,t)=>t(e)),gi,e,t,o),hi=(e,t,o)=>pi(e,t,o).isSome(),fi=(e,t,o)=>gi(e,(e=>Je(e,t)),o),bi=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=ze(e),Je(o,t);var o})).map(ze))(e),vi=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?A.none():A.from(o.querySelector(e)).map(ze)})(t,e),yi=(e,t,o)=>mi(((e,t)=>Je(e,t)),fi,e,t,o),xi="aria-controls",wi=()=>{const e=ca(xi);return{id:e,link:t=>{Ct(t,xi,e)},unlink:e=>{At(e,xi)}}},Si=(e,t)=>hi(t,(t=>Qe(t,e.element)),T)||((e,t)=>(e=>pi(e,(e=>{if(!$e(e))return!1;const t=_t(e,"id");return void 0!==t&&t.indexOf(xi)>-1})).bind((e=>{const t=_t(e,"id"),o=ft(e);return vi(o,`[${xi}="${t}"]`)})))(t).exists((t=>Si(e,t))))(e,t);var ki;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ki||(ki={}));const Ci=As({}),Oi=["alloy/data/Fields","alloy/debugging/Debugging"],_i=(e,t,o)=>((e,t,o)=>{switch(be(Ci.get(),e).orThunk((()=>{const t=ae(Ci.get());return re(t,(t=>e.indexOf(t)>-1?A.some(Ci.get()[t]):A.none()))})).getOr(ki.NORMAL)){case ki.NORMAL:return o(Ti());case ki.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",vr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+sa(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ki.STOP:return!0}})(e,t,o),Ti=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Ei=x([ns("menu"),ns("selectedMenu")]),Ai=x([ns("item"),ns("selectedItem")]);x(Bn(Ai().concat(Ei())));const Mi=x(Bn(Ai())),Di=cs("initSize",[ns("numColumns"),ns("numRows")]),Bi=()=>cs("markers",[ns("backgroundMenu")].concat(Ei()).concat(Ai())),Ii=e=>cs("markers",L(e,ns)),Fi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(Oi,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),es(t,t,o,$n((e=>rn.value(((...t)=>e.apply(void 0,t))))))),Ri=e=>Fi(0,e,xn(b)),Ni=e=>Fi(0,e,xn(A.none)),Vi=e=>Fi(0,e,{tag:"required",process:{}}),zi=e=>Fi(0,e,{tag:"required",process:{}}),Li=(e,t)=>ts(e,x(t)),Hi=e=>ts(e,w),Pi=x(Di),Ui=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Wi=Ms([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ji=Wi.southeast,Gi=Wi.southwest,$i=Wi.northeast,qi=Wi.northwest,Yi=Wi.south,Xi=Wi.north,Ki=Wi.east,Ji=Wi.west,Zi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Qi=(e,t,o)=>Math.min(Math.max(e,t),o),el=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),tl="layout",ol=e=>e.x,nl=(e,t)=>e.x+e.width/2-t.width/2,sl=(e,t)=>e.x+e.width-t.width,rl=(e,t)=>e.y-t.height,al=e=>e.y+e.height,il=(e,t)=>e.y+e.height/2-t.height/2,ll=(e,t,o)=>Ui(ol(e),al(e),o.southeast(),ji(),"southeast",el(e,{left:1,top:3}),tl),cl=(e,t,o)=>Ui(sl(e,t),al(e),o.southwest(),Gi(),"southwest",el(e,{right:0,top:3}),tl),dl=(e,t,o)=>Ui(ol(e),rl(e,t),o.northeast(),$i(),"northeast",el(e,{left:1,bottom:2}),tl),ul=(e,t,o)=>Ui(sl(e,t),rl(e,t),o.northwest(),qi(),"northwest",el(e,{right:0,bottom:2}),tl),ml=(e,t,o)=>Ui(nl(e,t),rl(e,t),o.north(),Xi(),"north",el(e,{bottom:2}),tl),gl=(e,t,o)=>Ui(nl(e,t),al(e),o.south(),Yi(),"south",el(e,{top:3}),tl),pl=(e,t,o)=>Ui((e=>e.x+e.width)(e),il(e,t),o.east(),Ki(),"east",el(e,{left:0}),tl),hl=(e,t,o)=>Ui(((e,t)=>e.x-t.width)(e,t),il(e,t),o.west(),Ji(),"west",el(e,{right:1}),tl),fl=()=>[ll,cl,dl,ul,gl,ml,pl,hl],bl=()=>[cl,ll,ul,dl,gl,ml,pl,hl],vl=()=>[dl,ul,ll,cl,ml,gl],yl=()=>[ul,dl,cl,ll,ml,gl],xl=()=>[ll,cl,dl,ul,gl,ml],wl=()=>[cl,ll,ul,dl,gl,ml];var Sl=Object.freeze({__proto__:null,events:e=>Hr([Wr(ur(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);H(a,(e=>{const o=n[e],s=o.schema,a=Kn("channel["+e+"] data\nReceiver: "+sa(t.element),s,r.data);o.onReceive(t,a)}))}))])}),kl=[ss("channels",qn(rn.value,Dn([Vi("onReceive"),xs("schema",Vn())])))];const Cl=(e,t,o)=>Qr(((n,s)=>{o(n,e,t)})),Ol=e=>({key:e,value:void 0}),_l=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:xa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>wa(e,t))),...l,revoke:k(Ol,o),config:t=>{const n=Kn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:eo((()=>Kn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Aa({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},Tl=e=>Bs(e),El=Dn([ns("fields"),ns("name"),xs("active",{}),xs("apis",{}),xs("state",_a),xs("extra",{})]),Al=e=>{const t=Kn("Creating behaviour: "+e.name,El,e);return((e,t,o,n,s,r)=>{const a=Dn(e),i=ys(t,[("config",l=e,gs("config",Dn(l)))]);var l;return _l(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},Ml=Dn([ns("branchKey"),ns("branches"),ns("name"),xs("active",{}),xs("apis",{}),xs("state",_a),xs("extra",{})]),Dl=e=>{const t=Kn("Creating behaviour: "+e.name,Ml,e);return((e,t,o,n,s,r)=>{const a=e,i=ys(t,[gs("config",e)]);return _l(a,i,t,o,n,s,r)})(Zn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},Bl=x(void 0),Il=Al({fields:kl,name:"receiving",active:Sl});var Fl=Object.freeze({__proto__:null,exhibit:(e,t)=>Aa({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Rl=(e,t=!1)=>e.dom.focus({preventScroll:t}),Nl=e=>e.dom.blur(),Vl=e=>{const t=ft(e).dom;return e.dom===t.activeElement},zl=(e=qo())=>A.from(e.dom.activeElement).map(ze),Ll=e=>zl(ft(e)).filter((t=>e.dom.contains(t.dom))),Hl=(e,t)=>{const o=ft(t),n=zl(o).bind((e=>{const o=t=>Qe(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{zl(o).filter((t=>Qe(t,e))).fold((()=>{Rl(e)}),b)})),s},Pl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Ul=(e,t)=>{Ft(e,(e=>({...e,position:A.some(e.position)}))(t))},Wl=Ms([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),jl=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Pl(e,u,m,h,h)),(()=>Pl(e,h,m,g,h)),(()=>Pl(e,u,h,h,p)),(()=>Pl(e,h,h,g,p)),(()=>Pl(e,u,m,h,h)),(()=>Pl(e,u,h,h,p)),(()=>Pl(e,u,m,h,h)),(()=>Pl(e,h,m,g,h)))},Gl=(e,t)=>e.fold((()=>{const e=t.rect;return Pl("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>jl("absolute",t,e,o,n,s)),((e,o,n,s)=>jl("fixed",t,e,o,n,s))),$l=(e,t)=>{const o=k(Ko,t),n=e.fold(o,o,(()=>{const e=Wo();return Ko(t).translate(-e.left,-e.top)})),s=Qt(t),r=Gt(t);return Jo(n.left,n.top,s,r)},ql=(e,t)=>t.fold((()=>e.fold(tn,tn,Jo)),(t=>e.fold(x(t),x(t),(()=>{const o=Yl(e,t.x,t.y);return Jo(o.left,o.top,t.width,t.height)})))),Yl=(e,t,o)=>{const n=qt(t,o);return e.fold(x(n),x(n),(()=>{const e=Wo();return n.translate(-e.left,-e.top)}))};Wl.none;const Xl=Wl.relative,Kl=Wl.fixed,Jl="data-alloy-placement",Zl=e=>Tt(e,Jl),Ql=Ms([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),ec=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Qi(i,e.y,e.bottom):Qi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Jo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Jo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Qi(a,o,d),g=Qi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Jo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Ql.fit(y):Ql.nofit(y,m,g,f)},tc=e=>{const t=As(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},oc=()=>tc((e=>e.unbind())),nc=()=>{const e=tc(b);return{...e,on:t=>e.get().each(t)}},sc=E,rc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!1))(e,t,sc,o),ac=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!0))(e,t,sc,o),ic=Io,lc=["top","bottom","right","left"],cc="data-alloy-transition-timer",dc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>$a(e,t))))(e,t.classes))(e,n)){Bt(e,"position",o.position);const a=$l(t,e),l=Gl(t,{...s,rect:a}),c=Z(lc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ft(e,c),i&&((e,t)=>{qa(e,t.classes),Tt(e,cc).each((t=>{clearTimeout(parseInt(t,10)),At(e,cc)})),((e,t)=>{const o=oc(),n=oc();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Qe(t.target,e)&&Be(n)&&R(lc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===nr())&&(clearTimeout(s),At(e,cc),Ya(e,t.classes))}},l=rc(e,sr(),(t=>{a(t)&&(l.unbind(),o.set(rc(e,nr(),i)),n.set(rc(e,or(),i)))})),c=(e=>{const t=t=>{const o=Rt(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),Ct(e,cc,s)}))})(e,t)})(e,n),Pt(e))}else Ya(e,n.classes)},uc=(e,t)=>{((e,t)=>{const o=Wt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Bt(e,"max-height",o+"px")})(e,Math.floor(t))},mc=x(((e,t)=>{uc(e,t),It(e,{"overflow-x":"hidden","overflow-y":"auto"})})),gc=x(((e,t)=>{uc(e,t)})),pc=(e,t,o)=>void 0===e[t]?o:e[t],hc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Ht(t,"max-height"),Ht(t,"max-width");const s={width:Qt(r=t),height:Gt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=ec(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Ql.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),Ql.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:ji(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Gl(o.origin,t);o.transition.each((s=>{dc(e,o.origin,n,s,t,o.lastPlacement)})),Ul(e,n)})(t,s,n),((e,t)=>{((e,t)=>{Ct(e,Jl,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Ya(e,o.off),qa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},fc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],bc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(fc,o);return{offset:qt(e,t),classesOn:Y(o,a),classesOff:Y(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},vc=()=>bc(0,0,{}),yc=w,xc=(e,t)=>o=>"rtl"===wc(o)?t:e,wc=e=>"rtl"===Rt(e,"direction")?"rtl":"ltr";var Sc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(Sc||(Sc={}));const kc="data-alloy-vertical-dir",Cc=e=>hi(e,(e=>$e(e)&&_t(e,"data-alloy-vertical-dir")===Sc.BottomToTop)),Oc=()=>ys("layouts",[ns("onLtr"),ns("onRtl"),ms("onBottomLtr"),ms("onBottomRtl")]),_c=(e,t,o,n,s,r,a)=>{const i=a.map(Cc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return xc(d,u)(e)};var Tc=[ns("hotspot"),ms("bubble"),xs("overrides",{}),Oc(),Li("placement",((e,t,o)=>{const n=t.hotspot,s=$l(o,n.element),r=_c(e.element,t,xl(),wl(),vl(),yl(),A.some(t.hotspot.element));return A.some(yc({anchorBox:s,bubble:t.bubble.getOr(vc()),overrides:t.overrides,layouts:r}))}))],Ec=[ns("x"),ns("y"),xs("height",0),xs("width",0),xs("bubble",vc()),xs("overrides",{}),Oc(),Li("placement",((e,t,o)=>{const n=Yl(o,t.x,t.y),s=Jo(n.left,n.top,t.width,t.height),r=_c(e.element,t,fl(),bl(),fl(),bl(),A.none());return A.some(yc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Ac=Ms([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Mc=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Dc=e=>e.fold(w,w),Bc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),qt(0,0)),Ic=e=>{const t=L(e,Dc);return Bc(t)},Fc=Ac.screen,Rc=Ac.absolute,Nc=(e,t,o)=>{const n=tt(e.element),s=Wo(n),r=((e,t,o)=>{const n=st(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=tt(t),n=tt(e.element);return Qe(o,n)})).map(Xt)})(e,0,o).getOr(s);return Rc(r,s.left,s.top)},Vc=(e,t,o,n)=>{const s=Fc(qt(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},zc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Ic(r),l=()=>Ic(r),c=()=>(e=>{const t=L(e,Mc);return Bc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?vl():xl(),m=o.showAbove?yl():wl(),g=_c(s,o,u,m,u,m,A.none());var p,h,f,b;return yc({anchorBox:d,bubble:o.bubble.getOr(vc()),overrides:o.overrides,layouts:g})}));var Lc=[ns("node"),ns("root"),ms("bubble"),Oc(),xs("overrides",{}),xs("showAbove",!1),Li("placement",((e,t,o)=>{const n=Nc(e,0,t);return t.node.filter(xt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Vc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return zc(a,n,t,o,i)}))}))];const Hc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Pc=Ms([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Uc=(Pc.before,Pc.on,Pc.after,e=>e.fold(w,w,w)),Wc=Ms([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),jc={domRange:Wc.domRange,relative:Wc.relative,exact:Wc.exact,exactFromRange:e=>Wc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>Uc(e),exact:(e,t,o,n)=>e}))(e);return st(t)},range:Hc},Gc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},$c=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},qc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Yc=Ms([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Xc=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Kc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:eo((()=>Gc(e,t,o))),rtl:eo((()=>A.some(Gc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:eo((()=>$c(e,t,o,n,s))),rtl:eo((()=>A.some($c(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Yc.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Xc(0,Yc.ltr,o))):Xc(0,Yc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Yc.ltr,Yc.rtl;const Jc=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Zc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?[]:L(o.querySelectorAll(e),ze)})(t,e),Qc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Hc(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},ed=e=>{if(null===e.anchorNode||null===e.focusNode)return Qc(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=tt(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Qe(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Hc(t,e.anchorOffset,o,e.focusOffset)):Qc(e)}},td=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(qc):A.none()})(Kc(e,t)),od=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(qe),nd=(e,t)=>({element:e,offset:t}),sd=(e,t)=>qe(e)?nd(e,t):((e,t)=>{const o=lt(e);if(0===o.length)return nd(e,t);if(t<o.length)return nd(o[t],0);{const e=o[o.length-1],t=qe(e)?(e=>od.get(e))(e).length:lt(e).length;return nd(e,t)}})(e,t),rd=e=>void 0!==e.foffset,ad=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(ed))(e)))().map((e=>{if(rd(e)){const t=sd(e.start,e.soffset),o=sd(e.finish,e.foffset);return jc.range(t.element,t.offset,o.element,o.offset)}return e}));var id=[ms("getSelection"),ns("root"),ms("bubble"),Oc(),xs("overrides",{}),xs("showAbove",!1),Li("placement",((e,t,o)=>{const n=st(t.root).dom,s=Nc(e,0,t),r=ad(n,t).bind((e=>{if(rd(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(qc):A.none()})(Kc(e,t)))(n,jc.exactFromRange(e)).orThunk((()=>{const t=Ve("\ufeff");No(e.start,t);const o=td(n,jc.exact(t,0,t,1));return Uo(t),o}));return t.bind((e=>Vc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Vc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=ad(n,t).bind((e=>rd(e)?$e(e.start)?A.some(e.start):at(e.start):A.some(e.firstCell))).getOr(e.element);return zc(r,s,t,o,a)}))];const ld="link-layout",cd=e=>e.x+e.width,dd=(e,t)=>e.x-t.width,ud=(e,t)=>e.y-t.height+e.height,md=e=>e.y,gd=(e,t,o)=>Ui(cd(e),md(e),o.southeast(),ji(),"southeast",el(e,{left:0,top:2}),ld),pd=(e,t,o)=>Ui(dd(e,t),md(e),o.southwest(),Gi(),"southwest",el(e,{right:1,top:2}),ld),hd=(e,t,o)=>Ui(cd(e),ud(e,t),o.northeast(),$i(),"northeast",el(e,{left:0,bottom:3}),ld),fd=(e,t,o)=>Ui(dd(e,t),ud(e,t),o.northwest(),qi(),"northwest",el(e,{right:1,bottom:3}),ld),bd=()=>[gd,pd,hd,fd],vd=()=>[pd,gd,fd,hd];var yd=[ns("item"),Oc(),xs("overrides",{}),Li("placement",((e,t,o)=>{const n=$l(o,t.item.element),s=_c(e.element,t,bd(),vd(),bd(),vd(),A.none());return A.some(yc({anchorBox:n,bubble:vc(),overrides:t.overrides,layouts:s}))}))],xd=Zn("type",{selection:id,node:Lc,hotspot:Tc,submenu:yd,makeshift:Ec});const wd=[us("classes",Hn),Cs("mode","all",["all","layout","placement"])],Sd=[xs("useFixed",T),ms("getBounds")],kd=[ss("anchor",xd),ys("transition",wd)],Cd=(e,t,o,n,s,r)=>{const a=Kn("placement.info",Bn(kd),s),i=a.anchor,l=n.element,c=o.get(n.uid);Hl((()=>{Bt(l,"position","fixed");const s=Vt(l,"visibility");Bt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Kl(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Xt(e.element),o=e.element.dom.getBoundingClientRect();return Xl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=pc(a,"maxHeightFunction",mc()),c=pc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:ql(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return hc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Ht(l,"visibility")}),(e=>{Bt(l,"visibility",e)})),Vt(l,"left").isNone()&&Vt(l,"top").isNone()&&Vt(l,"right").isNone()&&Vt(l,"bottom").isNone()&&xe(Vt(l,"position"),"fixed")&&Ht(l,"position")}),l)};var Od=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Cd(e,t,o,n,s,r)},positionWithinBounds:Cd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;H(["position","left","right","top","bottom"],(e=>Ht(s,e))),(e=>{At(e,Jl)})(s),o.clear(n.uid)}});const _d=Al({fields:Sd,name:"positioning",active:Fl,apis:Od,state:Object.freeze({__proto__:null,init:()=>{let e={};return Ta({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),Td=e=>e.getSystem().isConnected(),Ed=e=>{Fr(e,Cr());const t=e.components();H(t,Ed)},Ad=e=>{const t=e.components();H(t,Ad),Fr(e,kr())},Md=(e,t)=>{e.getSystem().addToWorld(t),xt(e.element)&&Ad(t)},Dd=e=>{Ed(e),e.getSystem().removeFromWorld(e)},Bd=(e,t)=>{Lo(e.element,t.element)},Id=(e,t)=>{Fd(e,t,Lo)},Fd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),xt(e.element)&&Ad(t),e.syncComponents()},Rd=e=>{Ed(e),Uo(e.element),e.getSystem().removeFromWorld(e)},Nd=e=>{const t=rt(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Rd(e),t.each((e=>{e.syncComponents()}))},Vd=e=>{const t=e.components();H(t,Rd),Po(e.element),e.syncComponents()},zd=(e,t)=>{Hd(e,t,Lo)},Ld=(e,t)=>{Hd(e,t,Vo)},Hd=(e,t,o)=>{o(e,t.element);const n=lt(t.element);H(n,(e=>{t.getByDom(e).each(Ad)}))},Pd=e=>{const t=lt(e.element);H(t,(t=>{e.getByDom(t).each(Ed)})),Uo(e.element)},Ud=(e,t,o,n)=>{o.get().each((t=>{Vd(e)}));const s=t.getAttachPoint(e);Id(s,e);const r=e.getSystem().build(n);return Id(e,r),o.set(r),r},Wd=(e,t,o,n)=>{const s=Ud(e,t,o,n);return t.onOpen(e,s),s},jd=(e,t,o)=>{o.get().each((n=>{Vd(e),Nd(e),t.onClose(e,n),o.clear()}))},Gd=(e,t,o)=>o.isOpen(),$d=(e,t,o)=>{const n=t.getAttachPoint(e);Bt(e.element,"position",_d.getMode(n)),((e,t,o,n)=>{Vt(e.element,t).fold((()=>{At(e.element,o)}),(t=>{Ct(e.element,o,t)})),Bt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},qd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>Vt(e,t).isSome())))(e.element)||Ht(e.element,"position"),((e,t,o)=>{Tt(e.element,o).fold((()=>Ht(e.element,t)),(o=>Bt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Yd=Object.freeze({__proto__:null,cloak:$d,decloak:qd,open:Wd,openWhileCloaked:(e,t,o,n,s)=>{$d(e,t),Wd(e,t,o,n),s(),qd(e,t)},close:jd,isOpen:Gd,isPartOf:(e,t,o,n)=>Gd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>Ud(e,t,o,n)))}),Xd=Object.freeze({__proto__:null,events:(e,t)=>Hr([Wr(fr(),((o,n)=>{jd(o,e,t)}))])}),Kd=[Ri("onOpen"),Ri("onClose"),ns("isPartOf"),ns("getAttachPoint"),xs("cloakVisibilityAttr","data-precloak-visibility")],Jd=Object.freeze({__proto__:null,init:()=>{const e=nc(),t=x("not-implemented");return Ta({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Zd=Al({fields:Kd,name:"sandboxing",active:Xd,apis:Yd,state:Jd}),Qd=x("dismiss.popups"),eu=x("reposition.popups"),tu=x("mouse.released"),ou=Dn([xs("isExtraPart",T),ys("fireEventInstead",[xs("event",Or())])]),nu=e=>{const t=Kn("Dismissal",ou,e);return{[Qd()]:{schema:Dn([ns("target")]),onReceive:(e,o)=>{Zd.isOpen(e)&&(Zd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Zd.close(e)),(t=>Fr(e,t.event))))}}}},su=Dn([ys("fireEventInstead",[xs("event",_r())]),ls("doReposition")]),ru=e=>{const t=Kn("Reposition",su,e);return{[eu()]:{onReceive:e=>{Zd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Fr(e,t.event)))}}}},au=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},iu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var lu=Object.freeze({__proto__:null,onLoad:au,onUnload:iu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),cu=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Jr(((o,n)=>{au(o,e,t)})),Zr(((o,n)=>{iu(o,e,t)}))]:[Cl(e,t,au)];return Hr(o)}});const du=()=>{const e=As(null);return Ta({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},uu=()=>{const e=As({}),t=As({});return Ta({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};H(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var mu=Object.freeze({__proto__:null,memory:du,dataset:uu,manual:()=>Ta({readState:b}),init:e=>e.store.manager.state(e)});const gu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var pu=[ms("initialValue"),ns("getFallbackEntry"),ns("getDataKey"),ns("setValue"),Li("manager",{setValue:gu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{gu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:uu})],hu=[ns("getValue"),xs("setValue",b),ms("initialValue"),Li("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:_a.init})],fu=[ms("initialValue"),Li("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:du})],bu=[ws("store",{mode:"memory"},Zn("mode",{memory:fu,manual:hu,dataset:pu})),Ri("onSetValue"),xs("resetOnDom",!1)];const vu=Al({fields:bu,name:"representing",active:cu,apis:lu,extra:{setValueFrom:(e,t)=>{const o=vu.getValue(t);vu.setValue(e,o)}},state:mu}),yu=(e,t)=>Es(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,es(o,o,{tag:"option",process:{}},On((e=>mn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([ts("dump",w)])),xu=e=>e.dump,wu=(e,t)=>({...Tl(t),...e.dump}),Su=yu,ku=wu,Cu="placeholder",Ou=Ms([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),_u=e=>ve(e,"uiType"),Tu=(e,t,o,n)=>((e,t,o,n)=>_u(o)&&o.uiType===Cu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Ou.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Ou.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=_u(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=Y(i,(o=>Tu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(_u(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Eu=Ou.single,Au=Ou.multiple,Mu=x(Cu),Du=Ms([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Bu=xs("factory",{sketch:w}),Iu=xs("schema",[]),Fu=ns("name"),Ru=es("pname","pname",yn((e=>"<alloy."+ca(e.name)+">")),Vn()),Nu=ts("schema",(()=>[ms("preprocess")])),Vu=xs("defaults",x({})),zu=xs("overrides",x({})),Lu=Bn([Bu,Iu,Fu,Ru,Vu,zu]),Hu=Bn([Bu,Iu,Fu,Vu,zu]),Pu=Bn([Bu,Iu,Fu,Ru,Vu,zu]),Uu=Bn([Bu,Nu,Fu,ns("unit"),Ru,Vu,zu]),Wu=e=>e.fold(A.some,A.none,A.some,A.some),ju=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Gu=(e,t)=>o=>{const n=Kn("Converting part type",t,o);return e(n)},$u=Gu(Du.required,Lu),qu=Gu(Du.external,Hu),Yu=Gu(Du.optional,Pu),Xu=Gu(Du.group,Uu),Ku=x("entirety");var Ju=Object.freeze({__proto__:null,required:$u,external:qu,optional:Yu,group:Xu,asNamedPart:Wu,name:ju,asCommon:e=>e.fold(w,w,w,w),original:Ku});const Zu=(e,t,o,n)=>bn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Qu=(e,t)=>{const o={};return H(t,(t=>{Wu(t).each((t=>{const n=em(e,t.pname);o[t.name]=o=>{const s=Kn("Part: "+t.name+" in "+e,Bn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},em=(e,t)=>({uiType:Mu(),owner:e,name:t}),tm=(e,t,o)=>({uiType:Mu(),owner:e,name:t,config:o,validated:{}}),om=e=>Y(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>cs(e.name,e.schema.concat([Hi(Ku())])))).toArray())),nm=e=>L(e,ju),sm=(e,t,o)=>((e,t,o)=>{const n={},s={};return H(o,(e=>{e.fold((e=>{n[e.pname]=Eu(!0,((t,o,n)=>e.factory.sketch(Zu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Zu(t,e,o[Ku()]),o))}),(e=>{n[e.pname]=Eu(!1,((t,o,n)=>e.factory.sketch(Zu(t,e,o,n))))}),(e=>{n[e.pname]=Au(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(bn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),rm=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>Y(o,(o=>Tu(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),am=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},im=(e,t,o)=>am(e,t,o).getOrDie("Could not find part: "+o),lm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},cm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},dm=e=>ae(e.partUids),um=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},mm=(e,t)=>{const o=nm(t);return Bs(L(o,(t=>({key:t,value:e+"-"+t}))))},gm=e=>es("partUids","partUids",wn((t=>mm(t.uid,e))),Vn());var pm=Object.freeze({__proto__:null,generate:Qu,generateOne:tm,schemas:om,names:nm,substitutes:sm,components:rm,defaultUids:mm,defaultUidsSchema:gm,getAllParts:cm,getAllPartNames:dm,getPart:am,getPartOrDie:im,getParts:lm,getPartsOrDie:um});const hm=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[cs("parts",e)]:[]).concat([ns("uid"),xs("dom",{}),xs("components",[]),Hi("originalSpec"),xs("debug.sketcher",{})]).concat(t))(n,s);return Kn(e+" [SpecSchema]",Dn(r.concat(t)),o)},fm=(e,t,o,n,s)=>{const r=bm(s),a=om(o),i=gm(o),l=hm(e,t,r,a,[i]),c=sm(0,l,o);return n(l,rm(e,l,c.internals()),r,c.externals())},bm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:fa("uid")},vm=Dn([ns("name"),ns("factory"),ns("configFields"),xs("apis",{}),xs("extraApis",{})]),ym=Dn([ns("name"),ns("factory"),ns("configFields"),ns("partFields"),xs("apis",{}),xs("extraApis",{})]),xm=e=>{const t=Kn("Sketcher for "+e.name,vm,e),o=ce(t.apis,Oa),n=ce(t.extraApis,((e,t)=>wa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=bm(n);return o(hm(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},wm=e=>{const t=Kn("Sketcher for "+e.name,ym,e),o=Qu(t.name,t.partFields),n=ce(t.apis,Oa),s=ce(t.extraApis,((e,t)=>wa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>fm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},Sm=e=>Ke("input")(e)&&"radio"!==_t(e,"type")||Ke("textarea")(e);var km=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Cm=[ns("find")],Om=Al({fields:Cm,name:"composing",apis:km}),_m=["input","button","textarea","select"],Tm=(e,t,o)=>{(t.disabled()?Im:Fm)(e,t)},Em=(e,t)=>!0===t.useNative&&R(_m,We(e.element)),Am=e=>{Ct(e.element,"disabled","disabled")},Mm=e=>{At(e.element,"disabled")},Dm=e=>{Ct(e.element,"aria-disabled","true")},Bm=e=>{Ct(e.element,"aria-disabled","false")},Im=(e,t,o)=>{t.disableClass.each((t=>{Wa(e.element,t)})),(Em(e,t)?Am:Dm)(e),t.onDisabled(e)},Fm=(e,t,o)=>{t.disableClass.each((t=>{Ga(e.element,t)})),(Em(e,t)?Mm:Bm)(e),t.onEnabled(e)},Rm=(e,t)=>Em(e,t)?(e=>Et(e.element,"disabled"))(e):(e=>"true"===_t(e.element,"aria-disabled"))(e);var Nm=Object.freeze({__proto__:null,enable:Fm,disable:Im,isDisabled:Rm,onLoad:Tm,set:(e,t,o,n)=>{(n?Im:Fm)(e,t)}}),Vm=Object.freeze({__proto__:null,exhibit:(e,t)=>Aa({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hr([Pr(mr(),((t,o)=>Rm(t,e))),Cl(e,t,Tm)])}),zm=[_s("disabled",T),xs("useNative",!0),ms("disableClass"),Ri("onDisabled"),Ri("onEnabled")];const Lm=Al({fields:zm,name:"disabling",active:Vm,apis:Nm}),Hm=(e,t,o,n)=>{const s=Zc(e.element,"."+t.highlightClass);H(s,(o=>{N(n,(e=>Qe(e.element,o)))||(Ga(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Fr(o,Ir())})))}))},Pm=(e,t,o,n)=>{Hm(e,t,0,[n]),Um(e,t,o,n)||(Wa(n.element,t.highlightClass),t.onHighlight(e,n),Fr(n,Br()))},Um=(e,t,o,n)=>$a(n.element,t.highlightClass),Wm=(e,t,o)=>vi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),jm=(e,t,o)=>{const n=Zc(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Gm=(e,t,o,n)=>{const s=Zc(e.element,"."+t.itemClass);return $(s,(e=>$a(e,t.highlightClass))).bind((t=>{const o=Zi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},$m=(e,t,o)=>{const n=Zc(e.element,"."+t.itemClass);return we(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var qm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Hm(e,t,0,[]),dehighlight:(e,t,o,n)=>{Um(e,t,o,n)&&(Ga(n.element,t.highlightClass),t.onDehighlight(e,n),Fr(n,Ir()))},highlight:Pm,highlightFirst:(e,t,o)=>{Wm(e,t).each((n=>{Pm(e,t,o,n)}))},highlightLast:(e,t,o)=>{jm(e,t).each((n=>{Pm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Zc(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>rn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Pm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=$m(e,t);G(s,n).each((n=>{Pm(e,t,o,n)}))},isHighlighted:Um,getHighlighted:(e,t,o)=>vi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Wm,getLast:jm,getPrevious:(e,t,o)=>Gm(e,t,0,-1),getNext:(e,t,o)=>Gm(e,t,0,1),getCandidates:$m}),Ym=[ns("highlightClass"),ns("itemClass"),Ri("onHighlight"),Ri("onDehighlight")];const Xm=Al({fields:Ym,name:"highlighting",apis:qm}),Km=[8],Jm=[9],Zm=[13],Qm=[27],eg=[32],tg=[37],og=[38],ng=[39],sg=[40],rg=(e,t,o)=>{const n=K(e.slice(0,t)),s=K(e.slice(t+1));return G(n.concat(s),o)},ag=(e,t,o)=>{const n=K(e.slice(0,t));return G(n,o)},ig=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},lg=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},cg=e=>t=>{const o=t.raw;return R(e,o.which)},dg=e=>t=>X(e,(e=>e(t))),ug=e=>!0===e.raw.shiftKey,mg=e=>!0===e.raw.ctrlKey,gg=C(ug),pg=(e,t)=>({matches:e,classification:t}),hg=(e,t,o)=>{t.exists((e=>o.exists((t=>Qe(t,e)))))||Rr(e,Tr(),{prevFocus:t,newFocus:o})},fg=()=>{const e=e=>Ll(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);hg(t,n,s)}}},bg=()=>{const e=e=>Xm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Xm.highlight(t,e)}));const s=e(t);hg(t,n,s)}}};var vg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(vg||(vg={}));const yg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([xs("focusManager",fg()),ws("focusInside","onFocus",$n((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?rn.value(e):rn.error("Invalid value for focusInside")))),Li("handler",a),Li("state",t),Li("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==vg.OnFocusMode?A.none():s(e).map((o=>Wr(lr(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Wr(Js(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=cg(eg.concat(Zm))(n.event);e.focusInside===vg.OnEnterOrSpaceMode&&r&&Ns(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Wr(Zs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hr(a.toArray().concat(i))}};return a},xg=e=>{const t=[ms("onEscape"),ms("onEnter"),xs("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),xs("firstTabstop",0),xs("useTabstopAt",E),ms("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>yi(t,e))).getOr(t);return jt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>yi(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Zc(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Zc(e.element,s.selector);return n(e,s).bind((t=>$(a,k(Qe,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?rg:ag;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?ig:lg;return r(e,0,o,n)},l=x([pg(dg([ug,cg(Jm)]),a),pg(cg(Jm),i),pg(dg([gg,cg(Zm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([pg(cg(Qm),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),pg(cg(Jm),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>rt(e))(e).bind(dt).exists((t=>Qe(t,e))))(n)?a:i)(e,t,o)))))]);return yg(t,_a.init,l,c,(()=>A.some(s)))};var wg=xg(ts("cyclic",T)),Sg=xg(ts("cyclic",E));const kg=(e,t,o)=>Sm(o)&&cg(eg)(t.event)?A.none():((e,t,o)=>(Vr(e,o,mr()),A.some(!0)))(e,0,o),Cg=(e,t)=>A.some(!0),Og=[xs("execute",kg),xs("useSpace",!1),xs("useEnter",!0),xs("useControlEnter",!1),xs("useDown",!1)],_g=(e,t,o)=>o.execute(e,t,e.element);var Tg=yg(Og,_a.init,((e,t,o,n)=>{const s=o.useSpace&&!Sm(e.element)?eg:[],r=o.useEnter?Zm:[],a=o.useDown?sg:[],i=s.concat(r).concat(a);return[pg(cg(i),_g)].concat(o.useControlEnter?[pg(dg([mg,cg(Zm)]),_g)]:[])}),((e,t,o,n)=>o.useSpace&&!Sm(e.element)?[pg(cg(eg),Cg)]:[]),(()=>A.none()));const Eg=()=>{const e=nc();return Ta({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Ag=Object.freeze({__proto__:null,flatgrid:Eg,init:e=>e.state(e)});const Mg=e=>(t,o,n,s)=>{const r=e(t.element);return Fg(r,t,o,n,s)},Dg=(e,t)=>{const o=xc(e,t);return Mg(o)},Bg=(e,t)=>{const o=xc(t,e);return Mg(o)},Ig=e=>(t,o,n,s)=>Fg(e,t,o,n,s),Fg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),Rg=Ig,Ng=Ig,Vg=Ig,zg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Lg=(e,t,o)=>{const n=Zc(e,o);return((e,o)=>$(e,(e=>Qe(e,t))).map((t=>({index:t,candidates:e}))))(U(n,zg))},Hg=(e,t)=>$(e,(e=>Qe(t,e))),Pg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),Ug=(e,t,o,n,s)=>Pg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Zi(r,s,0,a-1);return A.some({row:t,column:i})})),Wg=(e,t,o,n,s)=>Pg(e,t,n,((t,r)=>{const a=Zi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Qi(r,0,i-1);return A.some({row:a,column:l})})),jg=[ns("selector"),xs("execute",kg),Ni("onEscape"),xs("captureTab",!1),Pi()],Gg=(e,t,o)=>{vi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},$g=e=>(t,o,n,s)=>Lg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),qg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),Yg=$g(((e,t,o,n)=>Ug(e,t,o,n,-1))),Xg=$g(((e,t,o,n)=>Ug(e,t,o,n,1))),Kg=$g(((e,t,o,n)=>Wg(e,t,o,n,-1))),Jg=$g(((e,t,o,n)=>Wg(e,t,o,n,1))),Zg=x([pg(cg(tg),Dg(Yg,Xg)),pg(cg(ng),Bg(Yg,Xg)),pg(cg(og),Rg(Kg)),pg(cg(sg),Ng(Jg)),pg(dg([ug,cg(Jm)]),qg),pg(dg([gg,cg(Jm)]),qg),pg(cg(eg.concat(Zm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>yi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Qg=x([pg(cg(Qm),((e,t,o)=>o.onEscape(e,t))),pg(cg(eg),Cg)]);var ep=yg(jg,Eg,Zg,Qg,(()=>A.some(Gg)));const tp=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===We(n)&&"disabled"===_t(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Lg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},op=(e,t,o,n)=>tp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Qi(t+o,n,s);return i===e?A.from(r):a(i)})),np=(e,t,o,n)=>tp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Zi(t,o,n,s);return i===e?A.none():a(i)})),sp=[ns("selector"),xs("getInitial",A.none),xs("execute",kg),Ni("onEscape"),xs("executeOnMove",!1),xs("allowVertical",!0),xs("allowHorizontal",!0),xs("cycles",!0)],rp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>yi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),ap=(e,t,o)=>{t.getInitial(e).orThunk((()=>vi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},ip=(e,t,o)=>(o.cycles?np:op)(e,o.selector,t,-1),lp=(e,t,o)=>(o.cycles?np:op)(e,o.selector,t,1),cp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?rp(t,o,n):A.some(!0))),dp=x([pg(cg(eg),Cg),pg(cg(Qm),((e,t,o)=>o.onEscape(e,t)))]);var up=yg(sp,_a.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?tg:[]].concat(o.allowVertical?og:[]),r=[...o.allowHorizontal?ng:[]].concat(o.allowVertical?sg:[]);return[pg(cg(s),cp(Dg(ip,lp))),pg(cg(r),cp(Bg(ip,lp))),pg(cg(Zm),rp),pg(cg(eg),rp)]}),dp,(()=>A.some(ap)));const mp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),gp=(e,t,o,n)=>{const s=e[t].length,r=Zi(o,n,0,s-1);return mp(e,t,r)},pp=(e,t,o,n)=>{const s=Zi(o,n,0,e.length-1),r=e[s].length,a=Qi(t,0,r-1);return mp(e,s,a)},hp=(e,t,o,n)=>{const s=e[t].length,r=Qi(o+n,0,s-1);return mp(e,t,r)},fp=(e,t,o,n)=>{const s=Qi(o+n,0,e.length-1),r=e[s].length,a=Qi(t,0,r-1);return mp(e,s,a)},bp=[cs("selectors",[ns("row"),ns("cell")]),xs("cycles",!0),xs("previousSelector",A.none),xs("execute",kg)],vp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return vi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},yp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return yi(n,s.selectors.row).bind((e=>{const t=Zc(e,s.selectors.cell);return Hg(t,n).bind((t=>{const n=Zc(o,s.selectors.row);return Hg(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>Zc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},xp=yp(((e,t,o)=>gp(e,t,o,-1)),((e,t,o)=>hp(e,t,o,-1))),wp=yp(((e,t,o)=>gp(e,t,o,1)),((e,t,o)=>hp(e,t,o,1))),Sp=yp(((e,t,o)=>pp(e,o,t,-1)),((e,t,o)=>fp(e,o,t,-1))),kp=yp(((e,t,o)=>pp(e,o,t,1)),((e,t,o)=>fp(e,o,t,1))),Cp=x([pg(cg(tg),Dg(xp,wp)),pg(cg(ng),Bg(xp,wp)),pg(cg(og),Rg(Sp)),pg(cg(sg),Ng(kp)),pg(cg(eg.concat(Zm)),((e,t,o)=>Ll(e.element).bind((n=>o.execute(e,t,n)))))]),Op=x([pg(cg(eg),Cg)]);var _p=yg(bp,_a.init,Cp,Op,(()=>A.some(vp)));const Tp=[ns("selector"),xs("execute",kg),xs("moveOnTab",!1)],Ep=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Ap=(e,t,o)=>{vi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Mp=(e,t,o)=>np(e,o.selector,t,-1),Dp=(e,t,o)=>np(e,o.selector,t,1),Bp=x([pg(cg(og),Vg(Mp)),pg(cg(sg),Vg(Dp)),pg(dg([ug,cg(Jm)]),((e,t,o,n)=>o.moveOnTab?Vg(Mp)(e,t,o,n):A.none())),pg(dg([gg,cg(Jm)]),((e,t,o,n)=>o.moveOnTab?Vg(Dp)(e,t,o,n):A.none())),pg(cg(Zm),Ep),pg(cg(eg),Ep)]),Ip=x([pg(cg(eg),Cg)]);var Fp=yg(Tp,_a.init,Bp,Ip,(()=>A.some(Ap)));const Rp=[Ni("onSpace"),Ni("onEnter"),Ni("onShiftEnter"),Ni("onLeft"),Ni("onRight"),Ni("onTab"),Ni("onShiftTab"),Ni("onUp"),Ni("onDown"),Ni("onEscape"),xs("stopSpaceKeyup",!1),ms("focusIn")];var Np=yg(Rp,_a.init,((e,t,o)=>[pg(cg(eg),o.onSpace),pg(dg([gg,cg(Zm)]),o.onEnter),pg(dg([ug,cg(Zm)]),o.onShiftEnter),pg(dg([ug,cg(Jm)]),o.onShiftTab),pg(dg([gg,cg(Jm)]),o.onTab),pg(cg(og),o.onUp),pg(cg(sg),o.onDown),pg(cg(tg),o.onLeft),pg(cg(ng),o.onRight),pg(cg(eg),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[pg(cg(eg),Cg)]:[],pg(cg(Qm),o.onEscape)]),(e=>e.focusIn));const Vp=wg.schema(),zp=Sg.schema(),Lp=up.schema(),Hp=ep.schema(),Pp=_p.schema(),Up=Tg.schema(),Wp=Fp.schema(),jp=Np.schema(),Gp=Dl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Vp,cyclic:zp,flow:Lp,flatgrid:Hp,matrix:Pp,execution:Up,menu:Wp,special:jp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Ag}),$p=(e,t)=>{Hl((()=>{((e,t,o)=>{const n=e.components();(e=>{H(e.components(),(e=>Uo(e.element))),Po(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);H(r,(t=>{Ed(t),e.getSystem().removeFromWorld(t)})),H(s,(t=>{Td(t)?Bd(e,t):(e.getSystem().addToWorld(t),Bd(e,t),xt(e.element)&&Ad(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},qp=(e,t)=>{Hl((()=>{((o,n,s)=>{const r=o.components(),a=Y(n,(e=>Ca(e).toArray()));H(r,(e=>{R(a,e)||Dd(e)}));const i=((e,t,o)=>Qa(e,t,((t,n)=>ei(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);H(l,(e=>{Td(e)&&Dd(e)})),H(i,(e=>{Td(e)||Md(o,e)})),o.syncComponents()})(e,t)}),e.element)},Yp=(e,t,o,n)=>{Dd(t);const s=ei(e.element,o,n,e.getSystem().buildOrPatch);Md(e,s),e.syncComponents()},Xp=(e,t,o)=>{const n=e.getSystem().build(o);Fd(e,n,t)},Kp=(e,t,o,n)=>{Nd(t),Xp(e,((e,t)=>((e,t,o)=>{ct(e,o).fold((()=>{Lo(e,t)}),(e=>{No(e,t)}))})(e,t,o)),n)},Jp=(e,t)=>e.components(),Zp=(e,t,o,n,s)=>{const r=Jp(e);return A.from(r[n]).map((o=>(s.fold((()=>Nd(o)),(s=>{(t.reuseDom?Yp:Kp)(e,o,n,s)})),o)))};var Qp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Xp(e,Lo,n)},prepend:(e,t,o,n)=>{Xp(e,zo,n)},remove:(e,t,o,n)=>{const s=Jp(e),r=G(s,(e=>Qe(n.element,e.element)));r.each(Nd)},replaceAt:Zp,replaceBy:(e,t,o,n,s)=>{const r=Jp(e);return $(r,n).bind((o=>Zp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?qp:$p)(e,n),contents:Jp});const eh=Al({fields:[Os("reuseDom",!0)],name:"replacing",apis:Qp}),th=(e,t)=>{const o=((e,t)=>{const o=Hr(t);return Al({fields:[ns("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:_a}}},oh=(e,t)=>{t.ignore||(Rl(e.element),t.onFocus(e))};var nh=Object.freeze({__proto__:null,focus:oh,blur:(e,t)=>{t.ignore||Nl(e.element)},isFocused:e=>Vl(e.element)}),sh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Aa(o)},events:e=>Hr([Wr(lr(),((t,o)=>{oh(t,e),o.stop()}))].concat(e.stopMousedown?[Wr(js(),((e,t)=>{t.event.prevent()}))]:[]))}),rh=[Ri("onFocus"),xs("stopMousedown",!1),xs("ignore",!1)];const ah=Al({fields:rh,name:"focusing",active:sh,apis:nh}),ih=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?Wa(e.element,t):Ga(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},lh=(e,t,o)=>{ih(e,t,o,!o.get())},ch=(e,t,o)=>{ih(e,t,o,t.selected)};var dh=Object.freeze({__proto__:null,onLoad:ch,toggle:lh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{ih(e,t,o,!0)},off:(e,t,o)=>{ih(e,t,o,!1)},set:ih}),uh=Object.freeze({__proto__:null,exhibit:()=>Aa({}),events:(e,t)=>{const o=(n=e,s=t,r=lh,ea((e=>{r(e,n,s)})));var n,s,r;const a=Cl(e,t,ch);return Hr(q([e.toggleOnExecute?[o]:[],[a]]))}});const mh=(e,t,o)=>{Ct(e.element,"aria-expanded",o)};var gh=[xs("selected",!1),ms("toggleClass"),xs("toggleOnExecute",!0),Ri("onToggled"),ws("aria",{mode:"none"},Zn("mode",{pressed:[xs("syncWithExpanded",!1),Li("update",((e,t,o)=>{Ct(e.element,"aria-pressed",o),t.syncWithExpanded&&mh(e,0,o)}))],checked:[Li("update",((e,t,o)=>{Ct(e.element,"aria-checked",o)}))],expanded:[Li("update",mh)],selected:[Li("update",((e,t,o)=>{Ct(e.element,"aria-selected",o)}))],none:[Li("update",b)]}))];const ph=Al({fields:gh,name:"toggling",active:uh,apis:dh,state:(!1,{init:()=>{const e=As(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const hh=()=>{const e=(e,t)=>{t.stop(),Nr(e)};return[Wr(tr(),e),Wr(pr(),e),Yr(Hs()),Yr(js())]},fh=e=>Hr(q([e.map((e=>ea(((t,o)=>{e(t),o.stop()})))).toArray(),hh()])),bh="alloy.item-hover",vh="alloy.item-focus",yh="alloy.item-toggled",xh=e=>{(Ll(e.element).isNone()||ah.isFocused(e))&&(ah.isFocused(e)||ah.focus(e),Rr(e,bh,{item:e}))},wh=e=>{Rr(e,vh,{item:e})},Sh=x(bh),kh=x(vh),Ch=x(yh),Oh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),_h=[ns("data"),ns("components"),ns("dom"),xs("hasSubmenu",!1),ms("toggling"),Su("itemBehaviours",[ph,ah,Gp,vu]),xs("ignoreFocus",!1),xs("domModification",{}),Li("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Oh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:ku(e.itemBehaviours,[e.toggling.fold(ph.revoke,(e=>ph.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Rr(e,yh,{item:e,state:t})})(t,o)}}))(e)))),ah.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{wh(e)}}),Gp.config({mode:"execution"}),vu.config({store:{mode:"memory",initialValue:e.data}}),th("item-type-events",[...hh(),Wr(Ys(),xh),Wr(gr(),ah.focus)])]),components:e.components,eventOrder:e.eventOrder}))),xs("eventOrder",{})],Th=[ns("dom"),ns("components"),Li("builder",(e=>({dom:e.dom,components:e.components,events:Hr([Xr(gr())])})))],Eh=x("item-widget"),Ah=x([$u({name:"widget",overrides:e=>({behaviours:Tl([vu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),Mh=[ns("uid"),ns("data"),ns("components"),ns("dom"),xs("autofocus",!1),xs("ignoreFocus",!1),Su("widgetBehaviours",[vu,ah,Gp]),xs("domModification",{}),gm(Ah()),Li("builder",(e=>{const t=sm(Eh(),e,Ah()),o=rm(Eh(),e,t.internals()),n=t=>am(t,e,"widget").map((e=>(Gp.focusIn(e),e))),s=(t,o)=>Sm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hr([ea(((e,t)=>{n(e).each((e=>{t.stop()}))})),Wr(Ys(),xh),Wr(gr(),((t,o)=>{e.autofocus?n(t):ah.focus(t)}))]),behaviours:ku(e.widgetBehaviours,[vu.config({store:{mode:"memory",initialValue:e.data}}),ah.config({ignore:e.ignoreFocus,onFocus:e=>{wh(e)}}),Gp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:Bl(),onLeft:s,onRight:s,onEscape:(t,o)=>ah.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(ah.focus(t),A.some(!0))})])}}))],Dh=Zn("type",{widget:Mh,item:_h,separator:Th}),Bh=x([Xu({factory:{sketch:e=>{const t=Kn("menu.spec item",Dh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:fa("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Ih=x([ns("value"),ns("items"),ns("dom"),ns("components"),xs("eventOrder",{}),yu("menuBehaviours",[Xm,vu,Om,Gp]),ws("movement",{mode:"menu",moveOnTab:!0},Zn("mode",{grid:[Pi(),Li("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Li("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),ns("rowSelector"),xs("previousSelector",A.none)],menu:[xs("moveOnTab",!0),Li("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),ss("markers",Mi()),xs("fakeFocus",!1),xs("focusManager",fg()),Ri("onHighlight"),Ri("onDehighlight")]),Fh=x("alloy.menu-focus"),Rh=wm({name:"Menu",configFields:Ih(),partFields:Bh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:wu(e.menuBehaviours,[Xm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),vu.config({store:{mode:"memory",initialValue:e.value}}),Om.config({find:A.some}),Gp.config(e.movement.config(e,e.movement))]),events:Hr([Wr(kh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Xm.highlight(e,o),t.stop(),Rr(e,Fh(),{menu:e,item:o})}))})),Wr(Sh(),((e,t)=>{const o=t.event.item;Xm.highlight(e,o)})),Wr(Ch(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===_t(o.element,"role")&&((e,t)=>{const o=Zc(e.element,'[role="menuitemradio"][aria-checked="true"]');H(o,(o=>{Qe(o,t.element)||e.getSystem().getByDom(o).each((e=>{ph.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Nh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Nh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),Vh=e=>"prepared"===e.type?A.some(e.menu):A.none(),zh=()=>{const e=As({}),t=As({}),o=As({}),n=nc(),s=As({}),r=e=>a(e).bind(Vh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{H(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Nh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=K(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(Y(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Lh=Vh,Hh=ca("tiered-menu-item-highlight"),Ph=ca("tiered-menu-item-dehighlight");var Uh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Uh||(Uh={}));const Wh=x("collapse-item"),jh=xm({name:"TieredMenu",configFields:[zi("onExecute"),zi("onEscape"),Vi("onOpenMenu"),Vi("onOpenSubmenu"),Ri("onRepositionMenu"),Ri("onCollapseMenu"),xs("highlightOnOpen",Uh.HighlightMenuAndItem),cs("data",[ns("primary"),ns("menus"),ns("expansions")]),xs("fakeFocus",!1),Ri("onHighlightItem"),Ri("onDehighlightItem"),Ri("onHover"),Bi(),ns("dom"),xs("navigateOnHover",!0),xs("stayInDom",!1),yu("tmenuBehaviours",[Gp,Xm,Om,eh]),xs("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=nc(),n=zh(),s=e=>vu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>Y(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Xm.highlight,i=(t,o)=>{a(t,o),Xm.getHighlighted(o).orThunk((()=>Xm.getFirst(o))).each((n=>{e.fakeFocus?Xm.highlight(o,n):Vr(t,n.element,gr())}))},l=(e,t)=>we(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));H(s,(o=>{Ya(o.element,[e.markers.backgroundMenu]),e.stayInDom||eh.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Zc(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===_t(e,"aria-haspopup")));return H(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);Ct(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return H(a,(t=>{Wa(t.element,e.markers.backgroundMenu)})),xt(r.element)||eh.append(t,di(r)),Ya(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Lm)&&Lm.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return xt(l.element)||eh.append(t,di(l)),e.onOpenSubmenu(t,o,l,K(s)),r===m.HighlightSubmenu?(Xm.highlightFirst(l),u(t,n,s)):(Xm.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>yi(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hr([Wr(Fh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Xm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ea(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Jr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>Rh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Rr(e,Hh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Rr(e,Ph,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?bg():fg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{eh.append(t,di(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Uh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Uh.HighlightJustMenu&&a(t,o)}))})),Wr(Hh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Wr(Ph,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Wr(Sh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Xm.getHighlighted(e).bind(Xm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(L(o,Lh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Xm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===_t(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:wu(e.tmenuBehaviours,[Gp.config({mode:"special",onRight:h(((e,t)=>Sm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Sm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Vr(e,t.element,gr())}))}}),Xm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),Om.config({find:e=>Xm.getHighlighted(e)}),eh.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ds(e,t),expansions:{}}),collapseItem:e=>({value:ca(Wh()),meta:{text:e}})}}),Gh=xm({name:"InlineView",configFields:[ns("lazySink"),Ri("onShow"),Ri("onHide"),bs("onEscape"),yu("inlineBehaviours",[Zd,vu,Il]),ys("fireDismissalEventInstead",[xs("event",Or())]),ys("fireRepositionEventInstead",[xs("event",_r())]),xs("getRelated",A.none),xs("isExtraPart",T),xs("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Zd.openWhileCloaked(t,o,(()=>_d.positionWithinBounds(r,t,n,s()))),vu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>xl(),onRtl:()=>wl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return jh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Zd.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{_d.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();_d.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();_d.positionWithinBounds(a,t,o,s()),H(n,(e=>{const t=i(e.triggeringPath);_d.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Zd.open(t,r),vu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Zd.isOpen(t)&&vu.getValue(t).each((o=>{switch(o.mode){case"menu":Zd.getState(t).each(jh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();_d.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Zd.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Zd.isOpen(e)&&(vu.setValue(e,A.none()),Zd.close(e))},getContent:e=>Zd.getState(e),reposition:s,isOpen:Zd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:wu(e.inlineBehaviours,[Zd.config({isPartOf:(t,o,n)=>Si(o,n)||((t,o)=>e.getRelated(t).exists((e=>Si(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),vu.config({store:{mode:"memory",initialValue:A.none()}}),Il.config({channels:{...nu({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...ru({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var $h=tinymce.util.Tools.resolve("tinymce.util.Delay");const qh=xm({name:"Button",factory:e=>{const t=fh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:ku(e.buttonBehaviours,[ah.config({}),Gp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[xs("uid",void 0),ns("dom"),xs("components",[]),Su("buttonBehaviours",[ah,Gp]),ms("action"),ms("role"),xs("eventOrder",{})]}),Yh=e=>{const t=Re(e),o=lt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:ta(t)};return{tag:We(t),classes:s,attributes:n,...r}},Xh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:fa("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}},{entries:Kh,setPrototypeOf:Jh,isFrozen:Zh,getPrototypeOf:Qh,getOwnPropertyDescriptor:ef}=Object;let{freeze:tf,seal:of,create:nf}=Object,{apply:sf,construct:rf}="undefined"!=typeof Reflect&&Reflect;sf||(sf=function(e,t,o){return e.apply(t,o)}),tf||(tf=function(e){return e}),of||(of=function(e){return e}),rf||(rf=function(e,t){return new e(...t)});const af=yf(Array.prototype.forEach),lf=yf(Array.prototype.pop),cf=yf(Array.prototype.push),df=yf(String.prototype.toLowerCase),uf=yf(String.prototype.toString),mf=yf(String.prototype.match),gf=yf(String.prototype.replace),pf=yf(String.prototype.indexOf),hf=yf(String.prototype.trim),ff=yf(RegExp.prototype.test),bf=(vf=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return rf(vf,t)});var vf;function yf(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return sf(e,t,n)}}function xf(e,t,o){var n;o=null!==(n=o)&&void 0!==n?n:df,Jh&&Jh(e,null);let s=t.length;for(;s--;){let n=t[s];if("string"==typeof n){const e=o(n);e!==n&&(Zh(t)||(t[s]=e),n=e)}e[n]=!0}return e}function wf(e){const t=nf(null);for(const[o,n]of Kh(e))t[o]=n;return t}function Sf(e,t){for(;null!==e;){const o=ef(e,t);if(o){if(o.get)return yf(o.get);if("function"==typeof o.value)return yf(o.value)}e=Qh(e)}return function(e){return console.warn("fallback value for",e),null}}const kf=tf(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Cf=tf(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Of=tf(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),_f=tf(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Tf=tf(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ef=tf(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Af=tf(["#text"]),Mf=tf(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Df=tf(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Bf=tf(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),If=tf(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Ff=of(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Rf=of(/<%[\w\W]*|[\w\W]*%>/gm),Nf=of(/\${[\w\W]*}/gm),Vf=of(/^data-[\-\w.\u00B7-\uFFFF]/),zf=of(/^aria-[\-\w]+$/),Lf=of(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Hf=of(/^(?:\w+script|data):/i),Pf=of(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Uf=of(/^html$/i);var Wf=Object.freeze({__proto__:null,MUSTACHE_EXPR:Ff,ERB_EXPR:Rf,TMPLIT_EXPR:Nf,DATA_ATTR:Vf,ARIA_ATTR:zf,IS_ALLOWED_URI:Lf,IS_SCRIPT_OR_DATA:Hf,ATTR_WHITESPACE:Pf,DOCTYPE_NAME:Uf});const jf=()=>"undefined"==typeof window?null:window;var Gf=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:jf();const o=t=>e(t);if(o.version="3.0.5",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;const n=t.document,s=n.currentScript;let{document:r}=t;const{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Sf(h,"cloneNode"),b=Sf(h,"nextSibling"),v=Sf(h,"childNodes"),y=Sf(h,"parentNode");if("function"==typeof i){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let x,w="";const{implementation:S,createNodeIterator:k,createDocumentFragment:C,getElementsByTagName:O}=r,{importNode:_}=n;let T={};o.isSupported="function"==typeof Kh&&"function"==typeof y&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:E,ERB_EXPR:A,TMPLIT_EXPR:M,DATA_ATTR:D,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:F}=Wf;let{IS_ALLOWED_URI:R}=Wf,N=null;const V=xf({},[...kf,...Cf,...Of,...Tf,...Af]);let z=null;const L=xf({},[...Mf,...Df,...Bf,...If]);let H=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),P=null,U=null,W=!0,j=!0,G=!1,$=!0,q=!1,Y=!1,X=!1,K=!1,J=!1,Z=!1,Q=!1,ee=!0,te=!1,oe=!0,ne=!1,se={},re=null;const ae=xf({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ie=null;const le=xf({},["audio","video","img","source","image","track"]);let ce=null;const de=xf({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ue="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let pe=ge,he=!1,fe=null;const be=xf({},[ue,me,ge],uf);let ve;const ye=["application/xhtml+xml","text/html"];let xe,we=null;const Se=r.createElement("form"),ke=function(e){return e instanceof RegExp||e instanceof Function},Ce=function(e){if(!we||we!==e){if(e&&"object"==typeof e||(e={}),e=wf(e),ve=ve=-1===ye.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,xe="application/xhtml+xml"===ve?uf:df,N="ALLOWED_TAGS"in e?xf({},e.ALLOWED_TAGS,xe):V,z="ALLOWED_ATTR"in e?xf({},e.ALLOWED_ATTR,xe):L,fe="ALLOWED_NAMESPACES"in e?xf({},e.ALLOWED_NAMESPACES,uf):be,ce="ADD_URI_SAFE_ATTR"in e?xf(wf(de),e.ADD_URI_SAFE_ATTR,xe):de,ie="ADD_DATA_URI_TAGS"in e?xf(wf(le),e.ADD_DATA_URI_TAGS,xe):le,re="FORBID_CONTENTS"in e?xf({},e.FORBID_CONTENTS,xe):ae,P="FORBID_TAGS"in e?xf({},e.FORBID_TAGS,xe):{},U="FORBID_ATTR"in e?xf({},e.FORBID_ATTR,xe):{},se="USE_PROFILES"in e&&e.USE_PROFILES,W=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,G=e.ALLOW_UNKNOWN_PROTOCOLS||!1,$=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,q=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,J=e.RETURN_DOM||!1,Z=e.RETURN_DOM_FRAGMENT||!1,Q=e.RETURN_TRUSTED_TYPE||!1,K=e.FORCE_BODY||!1,ee=!1!==e.SANITIZE_DOM,te=e.SANITIZE_NAMED_PROPS||!1,oe=!1!==e.KEEP_CONTENT,ne=e.IN_PLACE||!1,R=e.ALLOWED_URI_REGEXP||Lf,pe=e.NAMESPACE||ge,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),q&&(j=!1),Z&&(J=!0),se&&(N=xf({},[...Af]),z=[],!0===se.html&&(xf(N,kf),xf(z,Mf)),!0===se.svg&&(xf(N,Cf),xf(z,Df),xf(z,If)),!0===se.svgFilters&&(xf(N,Of),xf(z,Df),xf(z,If)),!0===se.mathMl&&(xf(N,Tf),xf(z,Bf),xf(z,If))),e.ADD_TAGS&&(N===V&&(N=wf(N)),xf(N,e.ADD_TAGS,xe)),e.ADD_ATTR&&(z===L&&(z=wf(z)),xf(z,e.ADD_ATTR,xe)),e.ADD_URI_SAFE_ATTR&&xf(ce,e.ADD_URI_SAFE_ATTR,xe),e.FORBID_CONTENTS&&(re===ae&&(re=wf(re)),xf(re,e.FORBID_CONTENTS,xe)),oe&&(N["#text"]=!0),Y&&xf(N,["html","head","body"]),N.table&&(xf(N,["tbody"]),delete P.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw bf('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw bf('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');x=e.TRUSTED_TYPES_POLICY,w=x.createHTML("")}else void 0===x&&(x=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,s)),null!==x&&"string"==typeof w&&(w=x.createHTML(""));tf&&tf(e),we=e}},Oe=xf({},["mi","mo","mn","ms","mtext"]),_e=xf({},["foreignobject","desc","title","annotation-xml"]),Te=xf({},["title","style","font","a","script"]),Ee=xf({},Cf);xf(Ee,Of),xf(Ee,_f);const Ae=xf({},Tf);xf(Ae,Ef);const Me=function(e){cf(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},De=function(e,t){try{cf(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){cf(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!z[e])if(J||Z)try{Me(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){let t,o;if(K)e="<remove></remove>"+e;else{const t=mf(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ve&&pe===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=x?x.createHTML(e):e;if(pe===ge)try{t=(new g).parseFromString(n,ve)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(pe,"template",null);try{t.documentElement.innerHTML=he?w:n}catch(e){}}const s=t.body||t.documentElement;return e&&o&&s.insertBefore(r.createTextNode(o),s.childNodes[0]||null),pe===ge?O.call(t,Y?"html":"body")[0]:Y?t.documentElement:s},Ie=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Fe=function(e){return"object"==typeof l?e instanceof l:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Re=function(e,t,n){T[e]&&af(T[e],(e=>{e.call(o,t,n,we)}))},Ne=function(e){let t;if(Re("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return Me(e),!0;var n;const s=xe(e.nodeName);if(Re("uponSanitizeElement",e,{tagName:s,allowedTags:N}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&(!Fe(e.content)||!Fe(e.content.firstElementChild))&&ff(/<[/\w]/g,e.innerHTML)&&ff(/<[/\w]/g,e.textContent))return Me(e),!0;if(!N[s]||P[s]){if(!P[s]&&ze(s)){if(H.tagNameCheck instanceof RegExp&&ff(H.tagNameCheck,s))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(s))return!1}if(oe&&!re[s]){const t=y(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n)t.insertBefore(f(o[n],!0),b(e))}return Me(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});const o=df(e.tagName),n=df(t.tagName);return!!fe[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===o:t.namespaceURI===ue?"svg"===o&&("annotation-xml"===n||Oe[n]):Boolean(Ee[o]):e.namespaceURI===ue?t.namespaceURI===ge?"math"===o:t.namespaceURI===me?"math"===o&&_e[n]:Boolean(Ae[o]):e.namespaceURI===ge?!(t.namespaceURI===me&&!_e[n])&&!(t.namespaceURI===ue&&!Oe[n])&&!Ae[o]&&(Te[o]||!Ee[o]):!("application/xhtml+xml"!==ve||!fe[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!ff(/<\/no(script|embed|frames)/i,e.innerHTML)?(q&&3===e.nodeType&&(t=e.textContent,t=gf(t,E," "),t=gf(t,A," "),t=gf(t,M," "),e.textContent!==t&&(cf(o.removed,{element:e.cloneNode()}),e.textContent=t)),Re("afterSanitizeElements",e,null),!1):(Me(e),!0)},Ve=function(e,t,o){if(ee&&("id"===t||"name"===t)&&(o in r||o in Se))return!1;if(j&&!U[t]&&ff(D,t));else if(W&&ff(B,t));else if(!z[t]||U[t]){if(!(ze(e)&&(H.tagNameCheck instanceof RegExp&&ff(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&ff(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&ff(H.tagNameCheck,o)||H.tagNameCheck instanceof Function&&H.tagNameCheck(o))))return!1}else if(ce[t]);else if(ff(R,gf(o,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==pf(o,"data:")||!ie[e])if(G&&!ff(I,gf(o,F,"")));else if(o)return!1;return!0},ze=function(e){return e.indexOf("-")>0},Le=function(e){let t,o,n,s;Re("beforeSanitizeAttributes",e,null);const{attributes:r}=e;if(!r)return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z};for(s=r.length;s--;){t=r[s];const{name:i,namespaceURI:l}=t;o="value"===i?t.value:hf(t.value);const c=o;if(n=xe(i),a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Re("uponSanitizeAttribute",e,a),o=a.attrValue,a.forceKeepAttr)continue;if(!a.keepAttr){De(i,e);continue}if(!$&&ff(/\/>/i,o)){De(i,e);continue}q&&(o=gf(o,E," "),o=gf(o,A," "),o=gf(o,M," "));const d=xe(e.nodeName);if(Ve(d,n,o)){if(!te||"id"!==n&&"name"!==n||(De(i,e),o="user-content-"+o),x&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(l);else switch(p.getAttributeType(d,n)){case"TrustedHTML":o=x.createHTML(o);break;case"TrustedScriptURL":o=x.createScriptURL(o)}if(o!==c)try{l?e.setAttributeNS(l,i,o):e.setAttribute(i,o)}catch(t){De(i,e)}}else De(i,e)}Re("afterSanitizeAttributes",e,null)},He=function e(t){let o;const n=Ie(t);for(Re("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Re("uponSanitizeShadowNode",o,null),Ne(o)||(o.content instanceof a&&e(o.content),Le(o));Re("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e){let t,s,r,i,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(he=!e,he&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw bf("toString is not a function");if("string"!=typeof(e=e.toString()))throw bf("dirty is not a string, aborting")}if(!o.isSupported)return e;if(X||Ce(c),o.removed=[],"string"==typeof e&&(ne=!1),ne){if(e.nodeName){const t=xe(e.nodeName);if(!N[t]||P[t])throw bf("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)t=Be("\x3c!----\x3e"),s=t.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?t=s:t.appendChild(s);else{if(!J&&!q&&!Y&&-1===e.indexOf("<"))return x&&Q?x.createHTML(e):e;if(t=Be(e),!t)return J?null:Q?w:""}t&&K&&Me(t.firstChild);const d=Ie(ne?e:t);for(;r=d.nextNode();)Ne(r)||(r.content instanceof a&&He(r.content),Le(r));if(ne)return e;if(J){if(Z)for(i=C.call(t.ownerDocument);t.firstChild;)i.appendChild(t.firstChild);else i=t;return(z.shadowroot||z.shadowrootmode)&&(i=_.call(n,i,!0)),i}let u=Y?t.outerHTML:t.innerHTML;return Y&&N["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&ff(Uf,t.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+u),q&&(u=gf(u,E," "),u=gf(u,A," "),u=gf(u,M," ")),x&&Q?x.createHTML(u):u},o.setConfig=function(e){Ce(e),X=!0},o.clearConfig=function(){we=null,X=!1},o.isValidAttribute=function(e,t,o){we||Ce({});const n=xe(e),s=xe(t);return Ve(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(T[e]=T[e]||[],cf(T[e],t))},o.removeHook=function(e){if(T[e])return lf(T[e])},o.removeHooks=function(e){T[e]&&(T[e]=[])},o.removeAllHooks=function(){T={}},o}();const $f=e=>Gf().sanitize(e);var qf=tinymce.util.Tools.resolve("tinymce.util.I18n");const Yf={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Xf="temporary-placeholder",Kf=e=>()=>be(e,Xf).getOr("!not found!"),Jf=(e,t)=>{const o=e.toLowerCase();if(qf.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},Zf=(e,t)=>be(t,Jf(e,t)),Qf=(e,t)=>{const o=t();return Zf(e,o).getOrThunk(Kf(o))},eb=()=>th("add-focusable",[Jr((e=>{bi(e.element,"svg").each((e=>Ct(e,"focusable","false")))}))]),tb=(e,t,o,n)=>{var s,r;const a=(e=>!!qf.isRtl()&&ve(Yf,e))(t)?["tox-icon--flip"]:[],i=be(o,Jf(t,o)).or(n).getOrThunk(Kf(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:Tl([...null!==(r=e.behaviours)&&void 0!==r?r:[],eb()])}},ob=(e,t,o,n=A.none())=>tb(t,e,o(),n),nb={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},sb=xm({name:"Notification",factory:e=>{const t=Xh({dom:Yh(`<p>${$f(e.translationProvider(e.text))}</p>`),behaviours:Tl([eh.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Xh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:Tl([eh.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{eh.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);eh.set(n,[ri(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(nb[e]))).toArray()]),i=Xh(qh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[ob("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Jf(e,n))));return tb({tag:"div",classes:["tox-notification__icon"]},s.getOr(Xf),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:Tl([eh.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:Tl([ah.config({}),th("notification-events",[Wr(Xs(),(e=>{i.getOpt(e).each(ah.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[ms("level"),ns("progress"),ms("icon"),ns("onAction"),ns("text"),ns("iconProvider"),ns("translationProvider"),Os("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var rb,ab,ib=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),lb=tinymce.util.Tools.resolve("tinymce.EditorManager"),cb=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(rb||(rb={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(ab||(ab={}));const db=e=>t=>t.options.get(e),ub=e=>t=>A.from(e(t)),mb=e=>{const t=cb.deviceType.isPhone(),o=cb.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:ib.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),V(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:ab.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!cb.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},gb=db("readonly"),pb=db("height"),hb=db("width"),fb=ub(db("min_width")),bb=ub(db("min_height")),vb=ub(db("max_width")),yb=ub(db("max_height")),xb=ub(db("style_formats")),wb=db("style_formats_merge"),Sb=db("style_formats_autohide"),kb=db("content_langs"),Cb=db("removed_menuitems"),Ob=db("toolbar_mode"),_b=db("toolbar_groups"),Tb=db("toolbar_location"),Eb=db("fixed_toolbar_container"),Ab=db("fixed_toolbar_container_target"),Mb=db("toolbar_persist"),Db=db("toolbar_sticky_offset"),Bb=db("menubar"),Ib=db("toolbar"),Fb=db("file_picker_callback"),Rb=db("file_picker_validator_handler"),Nb=db("font_size_input_default_unit"),Vb=db("file_picker_types"),zb=db("typeahead_urls"),Lb=db("anchor_top"),Hb=db("anchor_bottom"),Pb=db("draggable_modal"),Ub=db("statusbar"),Wb=db("elementpath"),jb=db("branding"),Gb=db("resize"),$b=db("paste_as_text"),qb=db("sidebar_show"),Yb=db("promotion"),Xb=db("help_accessibility"),Kb=db("default_font_stack"),Jb=e=>!1===e.options.get("skin"),Zb=e=>!1!==e.options.get("menubar"),Qb=e=>{const t=e.options.get("skin_url");if(Jb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return lb.baseURL+"/skins/ui/"+t}},ev=e=>A.from(e.options.get("skin_url")),tv=e=>e.options.get("line_height_formats").split(" "),ov=e=>{const t=Ib(e),o=r(t),n=l(t)&&t.length>0;return!sv(e)&&(n||o||!0===t)},nv=e=>{const t=V(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},sv=e=>nv(e).fold((()=>{const t=Ib(e);return f(t,r)&&t.length>0}),E),rv=e=>Tb(e)===ab.bottom,av=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Eb(e))&&void 0!==t?t:"";if(o.length>0)return vi(wt(),o);const n=Ab(e);return g(n)?A.some(ze(n)):A.none()},iv=e=>e.inline&&av(e).isSome(),lv=e=>av(e).getOrThunk((()=>bt(ft(ze(e.getElement()))))),cv=e=>e.inline&&!Zb(e)&&!ov(e)&&!sv(e),dv=e=>(e.options.get("toolbar_sticky")||e.inline)&&!iv(e)&&!cv(e),uv=e=>!iv(e)&&"split"===e.options.get("ui_mode"),mv=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var gv=Object.freeze({__proto__:null,get ToolbarMode(){return rb},get ToolbarLocation(){return ab},register:mb,getSkinUrl:Qb,getSkinUrlOption:ev,isReadOnly:gb,isSkinDisabled:Jb,getHeightOption:pb,getWidthOption:hb,getMinWidthOption:fb,getMinHeightOption:bb,getMaxWidthOption:vb,getMaxHeightOption:yb,getUserStyleFormats:xb,shouldMergeStyleFormats:wb,shouldAutoHideStyleFormats:Sb,getLineHeightFormats:tv,getContentLanguages:kb,getRemovedMenuItems:Cb,isMenubarEnabled:Zb,isMultipleToolbars:sv,isToolbarEnabled:ov,isToolbarPersist:Mb,getMultipleToolbarsOption:nv,getUiContainer:lv,useFixedContainer:iv,isSplitUiMode:uv,getToolbarMode:Ob,isDraggableModal:Pb,isDistractionFree:cv,isStickyToolbar:dv,getStickyToolbarOffset:Db,getToolbarLocation:Tb,isToolbarLocationBottom:rv,getToolbarGroups:_b,getMenus:mv,getMenubar:Bb,getToolbar:Ib,getFilePickerCallback:Fb,getFilePickerTypes:Vb,useTypeaheadUrls:zb,getAnchorTop:Lb,getAnchorBottom:Hb,getFilePickerValidatorHandler:Rb,getFontSizeInputDefaultUnit:Nb,useStatusBar:Ub,useElementPath:Wb,promotionEnabled:Yb,useBranding:jb,getResize:Gb,getPasteAsText:$b,getSidebarShow:qb,useHelpAccessibility:Xb,getDefaultFontStack:Kb});const pv="[data-mce-autocompleter]",hv=e=>yi(e,pv);var fv;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(fv||(fv={}));var bv=fv;const vv="tox-menu-nav__js",yv="tox-collection__item",xv="tox-swatch",wv={normal:vv,color:xv},Sv="tox-collection__item--enabled",kv="tox-collection__item-icon",Cv="tox-collection__item-label",Ov="tox-collection__item-caret",_v="tox-collection__item--active",Tv="tox-collection__item-container",Ev="tox-collection__item-container--row",Av=e=>be(wv,e).getOr(vv),Mv=e=>"color"===e?"tox-swatches":"tox-menu",Dv=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Mv(e),tieredMenu:"tox-tiered-menu"}),Bv=e=>{const t=Dv(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Av(e)}},Iv=(e,t,o)=>{const n=Dv(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Fv=[Rh.parts.items({})],Rv=(e,t,o)=>{const n=Dv(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Bv(o)}},Nv=x([ms("data"),xs("inputAttributes",{}),xs("inputStyles",{}),xs("tag","input"),xs("inputClasses",[]),Ri("onSetValue"),xs("styles",{}),xs("eventOrder",{}),yu("inputBehaviours",[vu,ah]),xs("selectOnFocus",!0)]),Vv=e=>Tl([ah.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Ka(t);t.dom.setSelectionRange(0,o.length)}:b})]),zv=e=>({...Vv(e),...wu(e.inputBehaviours,[vu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Ka(e.element),setValue:(e,t)=>{Ka(e.element)!==t&&Ja(e.element,t)}},onSetValue:e.onSetValue})])}),Lv=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Hv=xm({name:"Input",configFields:Nv(),factory:(e,t)=>({uid:e.uid,dom:Lv(e),components:[],behaviours:zv(e),eventOrder:e.eventOrder})}),Pv=ca("refetch-trigger-event"),Uv=ca("redirect-menu-item-interaction"),Wv="tox-menu__searcher",jv=e=>vi(e.element,`.${Wv}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Gv=jv,$v=e=>({fetchPattern:vu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),qv=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Rr(e,Uv,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[yv]},components:[Hv.sketch({inputClasses:[Wv,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:Tl([th(n,[Wr(Qs(),(e=>{Fr(e,Pv)})),Wr(Js(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Gp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Gp.name()]}})]}},Yv="tox-collection--results__js",Xv=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:ca("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Kv=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},Jv=(e,t)=>{const o=[];let n=[];return H(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Zv=(e,t,o)=>Rh.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?Kv({tag:"div",classes:["tox-collection__group"]},e)(s):Jv(s,((e,o)=>"separator"===t[o].type))}}),Qv=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Zv(e,t,w)]}),ey=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),ty=e=>(console.error(Jn(e)),console.log(e),A.none()),oy=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Rh.parts.items({preprocess:e=>Jv(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},ny=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Rh.parts.items({preprocess:"auto"!==e?Kv({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Qv(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Qv(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=ca("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[qv({i18n:qf.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Yv],attributes:{id:n}},components:[Zv(e,t,Xv)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=ca("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Yv].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Zv(e,t,Xv)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Rh.parts.items({preprocess:Kv({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Iv(t,n,s.menuType),components:Fv,items:o}},sy=as("type"),ry=as("name"),ay=as("label"),iy=as("text"),ly=as("title"),cy=as("icon"),dy=as("value"),uy=ls("fetch"),my=ls("getSubmenuItems"),gy=ls("onAction"),py=ls("onItemAction"),hy=_s("onSetup",(()=>b)),fy=hs("name"),by=hs("text"),vy=hs("icon"),yy=hs("tooltip"),xy=hs("label"),wy=hs("shortcut"),Sy=bs("select"),ky=Os("active",!1),Cy=Os("borderless",!1),Oy=Os("enabled",!0),_y=Os("primary",!1),Ty=e=>xs("columns",e),Ey=xs("meta",{}),Ay=_s("onAction",b),My=e=>ks("type",e),Dy=e=>es("name","name",yn((()=>ca(`${e}-name`))),Hn),By=Bn([sy,by]),Iy=Bn([My("autocompleteitem"),ky,Oy,Ey,dy,by,vy]),Fy=[Oy,yy,vy,by,hy],Ry=Bn([sy,gy].concat(Fy)),Ny=e=>Yn("toolbarbutton",Ry,e),Vy=[ky].concat(Fy),zy=Bn(Vy.concat([sy,gy])),Ly=e=>Yn("ToggleButton",zy,e),Hy=[_s("predicate",T),Cs("scope","node",["node","editor"]),Cs("position","selection",["node","selection","line"])],Py=Fy.concat([My("contextformbutton"),_y,gy,ts("original",w)]),Uy=Vy.concat([My("contextformbutton"),_y,gy,ts("original",w)]),Wy=Fy.concat([My("contextformbutton")]),jy=Vy.concat([My("contextformtogglebutton")]),Gy=Zn("type",{contextformbutton:Py,contextformtogglebutton:Uy}),$y=Bn([My("contextform"),_s("initValue",x("")),xy,us("commands",Gy),gs("launch",Zn("type",{contextformbutton:Wy,contextformtogglebutton:jy}))].concat(Hy)),qy=Bn([My("contexttoolbar"),as("items")].concat(Hy)),Yy=[sy,as("src"),hs("alt"),Ts("classes",[],Hn)],Xy=Bn(Yy),Ky=[sy,iy,fy,Ts("classes",["tox-collection__item-label"],Hn)],Jy=Bn(Ky),Zy=An((()=>Gn("type",{cardimage:Xy,cardtext:Jy,cardcontainer:Qy}))),Qy=Bn([sy,ks("direction","horizontal"),ks("align","left"),ks("valign","middle"),us("items",Zy)]),ex=[Oy,by,wy,("menuitem",es("value","value",yn((()=>ca("menuitem-value"))),Vn())),Ey];const tx=Bn([sy,xy,us("items",Zy),hy,Ay].concat(ex)),ox=Bn([sy,ky,vy].concat(ex)),nx=[sy,as("fancytype"),Ay],sx=[xs("initData",{})].concat(nx),rx=[bs("select"),Es("initData",{},[Os("allowCustomColors",!0),ks("storageKey","default"),vs("colors",Vn())])].concat(nx),ax=Zn("fancytype",{inserttable:sx,colorswatch:rx}),ix=Bn([sy,hy,Ay,vy].concat(ex)),lx=Bn([sy,my,hy,vy].concat(ex)),cx=Bn([sy,vy,ky,hy,gy].concat(ex)),dx=(e,t,o)=>{const n=Zc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},ux=e=>((e,t)=>Tl([th(e,t)]))(ca("unnamed-events"),e),mx=ca("tooltip.exclusive"),gx=ca("tooltip.show"),px=ca("tooltip.hide"),hx=(e,t,o)=>{e.getSystem().broadcastOn([mx],{})};var fx=Object.freeze({__proto__:null,hideAllExclusive:hx,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&eh.set(e,n)}))}}),bx=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{Nd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Hr(q([[Wr(gx,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){hx(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hr("normal"===e.mode?[Wr(Ys(),(e=>{Fr(o,gx)})),Wr($s(),(e=>{Fr(o,px)}))]:[]),behaviours:Tl([eh.config({})])});t.setTooltip(s),Id(n,s),e.onShow(o,s),_d.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Wr(px,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Wr(ur(),((e,t)=>{const n=t;n.universal||R(n.channels,mx)&&o(e)})),Zr((e=>{o(e)}))],"normal"===e.mode?[Wr(Xs(),(e=>{Fr(e,gx)})),Wr(cr(),(e=>{Fr(e,px)})),Wr(Ys(),(e=>{Fr(e,gx)})),Wr($s(),(e=>{Fr(e,px)}))]:[Wr(Br(),((e,t)=>{Fr(e,gx)})),Wr(Ir(),(e=>{Fr(e,px)}))]]))}}),vx=[ns("lazySink"),ns("tooltipDom"),xs("exclusive",!0),xs("tooltipComponents",[]),xs("delay",300),Cs("mode","normal",["normal","follow-highlight"]),xs("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([gl,ml,ll,dl,cl,ul]),onRtl:x([gl,ml,ll,dl,cl,ul])}}))),Ri("onHide"),Ri("onShow")],yx=Object.freeze({__proto__:null,init:()=>{const e=nc(),t=nc(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return Ta({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const xx=Al({fields:vx,name:"tooltipping",active:bx,state:yx,apis:fx}),wx="silver.readonly",Sx=Bn([("readonly",ss("readonly",Pn))]);const kx=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&H(n,(e=>{e.broadcastOn([Qd()],{target:o})})),H(n,(e=>{e.broadcastOn([wx],{readonly:t})}))},Cx=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&kx(t,!0)})),e.on("SwitchMode",(()=>kx(t,e.mode.isReadOnly()))),gb(e)&&e.mode.set("readonly")},Ox=()=>Il.config({channels:{[wx]:{schema:Sx,onReceive:(e,t)=>{Lm.set(e,t.readonly)}}}}),_x=e=>Lm.config({disabled:e}),Tx=e=>Lm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Ex=e=>Lm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Ax=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Mx=(e,t)=>Jr((o=>{Ax(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Dx=(e,t)=>Zr((o=>Ax(e,o)(t.get()))),Bx=(e,t)=>ea(((o,n)=>{Ax(e,o)(e.onAction),e.triggersSubmenu||t!==bv.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Fr(o,fr()),n.stop())})),Ix={[mr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Fx=we,Rx=(e,t,o,n)=>{const s=As(b);return{type:"item",dom:t.dom,components:Fx(t.optComponents),data:e.data,eventOrder:Ix,hasSubmenu:e.triggersSubmenu,itemBehaviours:Tl([th("item-events",[Bx(e,o),Mx(e,s),Dx(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Lm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Ox(),eh.config({})].concat(e.itemBehaviours))};var r},Nx=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Vx=e=>{const t=cb.os.isMacOS()||cb.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},zx=(e,t,o=[kv])=>ob(e,{tag:"div",classes:o},t),Lx=e=>({dom:{tag:"div",classes:[Cv]},components:[ri(qf.translate(e))]}),Hx=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Px=(e,t)=>({dom:{tag:"div",classes:[Cv]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ri(qf.translate(t))]}]}),Ux=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ri(Vx(e))]}),Wx=e=>zx("checkmark",e,["tox-collection__item-checkmark"]),jx=e=>{const t=e.map((e=>({attributes:{title:qf.translate(e),id:ca("menu-item")}}))).getOr({});return{tag:"div",classes:[vv,yv],...t}},Gx=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Zf(e,n).or(o).getOrThunk(Kf(n))})(e,t.icons,o)));return{dom:(()=>{const e=xv,o=r.getOr(""),a=n.map((e=>({title:t.translate(e)}))).getOr({}),i={tag:"div",attributes:a,classes:[e]};return"custom"===s?{...i,tag:"button",classes:[...i.classes,"tox-swatches__picker-btn"],innerHtml:o}:"remove"===s?{...i,classes:[...i.classes,"tox-swatch--remove"],innerHtml:o}:g(s)?{...i,attributes:{...i.attributes,"data-mce-color":s},styles:{"background-color":s},innerHtml:o}:i})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[kv]},r=o?e.iconContent.map((e=>ob(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Lx),(e=>ve(e,"style")?k(Px,e.style):Lx)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Hx(e,[Cv]))));return{dom:jx(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Ux),a,e.caret]}})(e,t,o,n),$x=(e,t)=>be(e,"tooltipWorker").map((e=>[xx.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:gc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{xx.setComponents(t,[ai({element:ze(e)})])}))}})])).getOr([]),qx=(e,t)=>{const o=(e=>ib.DOM.encode(e))(qf.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Yx=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Ev,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Tv,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Yx(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Hx(qx(e.text,n),e.classes)}})),Xx=Qu(Eh(),Ah()),Kx=e=>({value:ew(e)}),Jx=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Zx=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Qx=e=>Jx.test(e)||Zx.test(e),ew=e=>_e(e,"#").toUpperCase(),tw=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},ow=e=>{const t=tw(e.red)+tw(e.green)+tw(e.blue);return Kx(t)},nw=Math.min,sw=Math.max,rw=Math.round,aw=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,iw=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,lw=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),cw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},dw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=sw(0,nw(r,1)),a=sw(0,nw(a,1)),0===r)return t=o=n=rw(255*a),lw(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=rw(255*(t+d)),o=rw(255*(o+d)),n=rw(255*(n+d)),lw(t,o,n,1)},uw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Jx,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Zx.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return lw(o,n,s,1)},mw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return lw(s,r,a,i)},gw=e=>{if("transparent"===e)return A.some(lw(0,0,0,0));const t=aw.exec(e);if(null!==t)return A.some(mw(t[1],t[2],t[3],"1"));const o=iw.exec(e);return null!==o?A.some(mw(o[1],o[2],o[3],o[4])):A.none()},pw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,hw=lw(255,0,0,1),fw=(e,t)=>{e.dispatch("ResizeContent",t)},bw=(e,t)=>{e.dispatch("TextColorChange",t)},vw=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),yw=(e,t)=>()=>{e(),t()},xw=e=>Sw(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),ww=(e,t)=>o=>{const n=xw(e)(o),s=((e,t)=>o=>{const n=oc(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},Sw=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},kw=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},Cw=(e,t)=>()=>e.execCommand(t);var Ow=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const _w={},Tw=e=>be(_w,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=Ow.getItem(t);if(m(o)){const e=Ow.getItem("tinymce-custom-colors");Ow.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=Ow.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{F(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),Ow.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return _w[e]=n,n})),Ew=(e,t)=>{Tw(e).add(t)},Aw=(e,t,o)=>({hue:e,saturation:t,value:o}),Mw=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Aw(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Aw(Math.round(t),Math.round(100*o),Math.round(100*n)))},Dw=e=>ow(dw(e)),Bw=e=>{return(t=e,Qx(t)?A.some({value:ew(t)}):A.none()).orThunk((()=>gw(e).map(ow))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return ow(lw(s,r,a,i))}));var t},Iw="forecolor",Fw="hilitecolor",Rw=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Bw(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Nw=e=>t=>t.options.get(e),Vw="#000000",zw=(e,t)=>t===Iw&&e.options.isSet("color_map_foreground")?Nw("color_map_foreground")(e):t===Fw&&e.options.isSet("color_map_background")?Nw("color_map_background")(e):Nw("color_map")(e),Lw=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(zw(e,t).length))),Hw=(e,t)=>{const o=Nw("color_cols")(e),n=Lw(e,t);return o===Lw(e)?n:o},Pw=(e,t="default")=>Math.round(t===Iw?Nw("color_cols_foreground")(e):t===Fw?Nw("color_cols_background")(e):Nw("color_cols")(e)),Uw=Nw("custom_colors"),Ww=Nw("color_default_foreground"),jw=Nw("color_default_background"),Gw=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Rs(o,(e=>{if($e(e)){const t=Rt(e,"background-color");return Ce(gw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):Rt(o,"color");return gw(n).map((e=>"#"+ow(e).value))},$w=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},qw=(e,t,o,n)=>{"custom"===o?oS(e)((o=>{o.each((o=>{Ew(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Gw(e,t).getOr(Vw)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Yw=(e,t,o)=>e.concat((e=>L(Tw(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat($w(o))),Xw=(e,t,o)=>n=>{n(Yw(e,t,o))},Kw=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Jw=(e,t)=>{e.setTooltip(t)},Zw=(e,t)=>o=>{const n=Gw(e,t);return xe(n,o.toUpperCase())},Qw=(e,t,o)=>{if(Be(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=Yw(zw(e,t),t,!1),r=G(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},eS=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:Qw(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:Zw(e,o),columns:Pw(e,o),fetch:Xw(zw(e,o),o,Uw(e)),onAction:t=>{qw(e,o,n.get(),b)},onItemAction:(s,r)=>{qw(e,o,r,(o=>{n.set(o),bw(e,{name:t,color:o})}))},onSetup:s=>{Kw(s,t,n.get());const r=n=>{n.name===t&&(Kw(s,n.name,n.color),Jw(s,Qw(e,o,n.color)))};return e.on("TextColorChange",r),yw(xw(e)(s),(()=>{e.off("TextColorChange",r)}))}})},tS=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(Jw(n,Qw(e,o,s.get())),Kw(n,t,s.get()),xw(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:Zw(e,o),initData:{storageKey:o},onAction:n=>{qw(e,o,n.value,(o=>{s.set(o),bw(e,{name:t,color:o})}))}}]})},oS=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},nS=(e,t,o,n,s,r,a,i)=>{const l=ey(t),c=sS(t,o,n,"color"!==s?"normal":"color",r,a,i);return ny(e,l,c,n,{menuType:s})},sS=(e,t,o,n,s,r,a)=>we(L(e,(i=>{return"choiceitem"===i.type?(l=i,Yn("choicemenuitem",ox,l)).fold(ty,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Gx({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Wx(a.icons)):A.none(),caret:A.none(),value:e.value},a,i);return bn(Rx({data:Nx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{ph.set(e,t)},isActive:()=>ph.isOn(e),isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:Sv,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,ey(e))))):A.none();var l}))),rS=(e,t)=>{const o=Bv(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?vi(e.element,"[aria-checked=true]"):A.none()}},aS=ca("cell-over"),iS=ca("cell-execute"),lS=(e,t,o)=>{const n=o=>Rr(o,iS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return ci({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:Tl([th("insert-table-picker-cell",[Wr(Ys(),ah.focus),Wr(mr(),n),Wr(tr(),s),Wr(pr(),s)]),ph.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),ah.config({onFocus:o=>Rr(o,aS,{row:e,col:t})})])})},cS=e=>Y(e,(e=>L(e,di))),dS=(e,t)=>ri(`${t}x${e}`),uS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(lS(t,n,s))}n.push(o)}return n})(o),s=dS(0,0),r=Xh({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:Tl([eh.config({})])});return{type:"widget",data:{value:ca("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Xx.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:cS(n).concat(r.asSpec()),behaviours:Tl([th("insert-table-picker",[Jr((e=>{eh.set(r.get(e),[s])})),qr(aS,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)ph.set(e[n][s],n<=t&&s<=o)})(n,s,a),eh.set(r.get(e),[dS(s+1,a+1)])})),qr(iS,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Fr(t,fr())}))]),Gp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Yw(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat($w(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...nS(ca("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,bv.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Bv(s),movement:rS(n,s)};return{type:"widget",data:{value:ca("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Xx.widget(Rh.sketch(r))]}}},mS=e=>({type:"separator",dom:{tag:"div",classes:[yv,"tox-collection__group-heading"]},components:e.text.map(ri).toArray()});var gS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),pS=[ss("others",qn(rn.value,Vn()))],hS=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return Ta({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const fS=Al({fields:pS,name:"coupling",apis:gS,state:hS}),bS=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),H(o,r),o=[])})),{get:n,map:e=>bS((t=>{n((o=>{t(e(o))}))})),isReady:s}},vS={nu:bS,pure:e=>bS((t=>{t(e)}))},yS=e=>{setTimeout((()=>{throw e}),0)},xS=e=>{const t=t=>{e().then(t,yS)};return{map:t=>xS((()=>e().then(t))),bind:t=>xS((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>xS((()=>e().then((()=>t.toPromise())))),toLazy:()=>vS.nu(t),toCached:()=>{let t=null;return xS((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},wS=e=>xS((()=>new Promise(e))),SS=e=>xS((()=>Promise.resolve(e))),kS=x("sink"),CS=x(Yu({name:kS(),overrides:x({dom:{tag:"div"},behaviours:Tl([_d.config({useFixed:E})]),events:Hr([Yr(Js()),Yr(js()),Yr(tr())])})})),OS=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},_S=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=AS(n,e);return i.map((e=>e.bind((e=>A.from(jh.sketch({...r.menu(),uid:fa(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();_d.position(n,t,{anchor:o}),Zd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();_d.position(n,o,{anchor:{type:"submenu",item:t}}),Zd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();_d.position(s,t,{anchor:o}),H(n,(e=>{_d.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(ah.focus(n),Zd.close(s),A.some(!0))}))))))})(e,t,OS(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Zd.isOpen(n)&&Zd.close(n)}),(e=>{Zd.cloak(n),Zd.open(n,e),r(n)})),n)))},TS=(e,t,o,n,s,r,a)=>(Zd.close(n),SS(n)),ES=(e,t,o,n,s,r)=>{const a=fS.getCoupled(o,"sandbox");return(Zd.isOpen(a)?TS:_S)(e,t,o,a,n,s,r)},AS=(e,t)=>e.getSystem().getByUid(t.uid+"-"+kS()).map((e=>()=>rn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>rn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),MS=e=>{Zd.getState(e).each((e=>{jh.repositionMenus(e)}))},DS=(e,t,o)=>{const n=wi(),s=AS(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:ku(e.sandboxBehaviours,[vu.config({store:{mode:"memory",initialValue:t}}),Zd.config({onOpen:(s,r)=>{const a=OS(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=Om.getCurrent(t).getOr(t),s=Zt(e.element);o?Bt(n.element,"min-width",s+"px"):((e,t)=>{Jt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>Si(o,n)||Si(t,n),getAttachPoint:()=>s().getOrDie()}),Om.config({find:e=>Zd.getState(e).bind((e=>Om.getCurrent(e)))}),Il.config({channels:{...nu({isExtraPart:T}),...ru({doReposition:MS})}})])}},BS=e=>{const t=fS.getCoupled(e,"sandbox");MS(t)},IS=()=>[xs("sandboxClasses",[]),Su("sandboxBehaviours",[Om,Il,Zd,vu])],FS=x([ns("dom"),ns("fetch"),Ri("onOpen"),Ni("onExecute"),xs("getHotspot",A.some),xs("getAnchorOverrides",x({})),Oc(),yu("dropdownBehaviours",[ph,fS,Gp,ah]),ns("toggleClass"),xs("eventOrder",{}),ms("lazySink"),xs("matchWidth",!1),xs("useMinWidth",!1),ms("role")].concat(IS())),RS=x([qu({schema:[Bi(),xs("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),CS()]),NS=wm({name:"Dropdown",configFields:FS(),partFields:RS(),factory:(e,t,o,n)=>{const s=e=>{Zd.getState(e).each((e=>{jh.highlightPrimary(e)}))},r=(t,o,s)=>ES(e,w,t,n,o,s),a={expand:e=>{ph.isOn(e)||r(e,b,Uh.HighlightNone).get(b)},open:e=>{ph.isOn(e)||r(e,b,Uh.HighlightMenuAndItem).get(b)},refetch:t=>fS.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,Uh.HighlightMenuAndItem).map(b)),(o=>_S(e,w,t,o,n,b,Uh.HighlightMenuAndItem).map(b))),isOpen:ph.isOn,close:e=>{ph.isOn(e)&&r(e,b,Uh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{ph.isOn(e)&&BS(e)}},i=(e,t)=>(Nr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.dropdownBehaviours,[ph.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),fS.config({others:{sandbox:t=>DS(e,t,{onOpen:()=>ph.on(t),onClose:()=>ph.off(t)})}}),Gp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(NS.isOpen(e)){const t=fS.getCoupled(e,"sandbox");s(t)}else NS.open(e);return A.some(!0)},onEscape:(e,t)=>NS.isOpen(e)?(NS.close(e),A.some(!0)):A.none()}),ah.config({})]),events:fh(A.some((e=>{r(e,s,Uh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[mr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),VS=(e,t,o)=>{Gv(e).each((e=>{var n;((e,t)=>{Tt(t.element,"id").each((t=>Ct(e.element,"aria-activedescendant",t)))})(e,o),($a((n=t).element,Yv)?A.some(n.element):vi(n.element,"."+Yv)).each((t=>{Tt(t,"id").each((t=>Ct(e.element,"aria-controls",t)))}))})),Ct(o.element,"aria-selected","true")},zS=(e,t,o)=>{Ct(o.element,"aria-selected","false")},LS=e=>fS.getExistingCoupled(e,"sandbox").bind(jv).map($v).map((e=>e.fetchPattern)).getOr("");var HS;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(HS||(HS={}));const PS=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Yn("menuitem",ix,i)).fold(ty,(e=>A.some(((e,t,o,n=!0)=>{const s=Gx({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Rx({data:Nx(e),getApi:e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Yn("nestedmenuitem",lx,e))(e).fold(ty,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,zx("chevron-down",a,[Ov])):(e=>zx("chevron-right",e,[Ov]))(o.icons);var a;const i=Gx({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Rx({data:Nx(e),getApi:e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t),setIconFill:(t,o)=>{vi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ct(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);Ot(e.element,{"aria-label":n,title:n})}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Yn("togglemenuitem",cx,e))(e).fold(ty,(e=>A.some(((e,t,o,n=!0)=>{const s=Gx({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(Wx(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return bn(Rx({data:Nx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{ph.set(e,t)},isActive:()=>ph.isOn(e),isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:Sv,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Yn("separatormenuitem",By,e))(e).fold(ty,(e=>A.some(mS(e))));case"fancymenuitem":return(e=>Yn("fancymenuitem",ax,e))(e).fold(ty,(e=>((e,t)=>be(uS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},US=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||ey(e);return we(L(e,(e=>{switch(e.type){case"separator":return(n=e,Yn("Autocompleter.Separator",By,n)).fold(ty,(e=>A.some(mS(e))));case"cardmenuitem":return(e=>Yn("cardmenuitem",tx,e))(e).fold(ty,(e=>A.some(((e,t,o,n)=>{const s={dom:jx(e.label),optComponents:[A.some({dom:{tag:"div",classes:[Tv,Ev]},components:Yx(e.items,n)})]};return Rx({data:Nx({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>{Lm.set(e,!t),H(Zc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Lm)&&Lm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:$x(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Yn("Autocompleter.Item",Iy,e))(e).fold(ty,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Gx({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>qx(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon);return Rx({data:Nx(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:$x(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},WS=(e,t,o,n,s,r)=>{const a=ey(t),i=we(L(t,(e=>{const t=e=>PS(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?oy:ny)(e,a,i,1,l)},jS=e=>jh.singleData(e.value,e),GS=(e,t)=>{const o=ca("autocompleter"),n=As(!1),s=As(!1),r=ci(Gh.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:Tl([th("dismissAutocompleter",[Wr(Or(),(()=>d())),Wr(Br(),((t,o)=>{Tt(o.event.target,"id").each((t=>Ct(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),a=()=>Gh.isOpen(r),i=s.get,l=()=>{if(a()){Gh.hide(r),e.dom.remove(o,!1);const t=ze(e.getBody());Tt(t,"aria-owns").filter((e=>e===o)).each((()=>{At(t,"aria-owns"),At(t,"aria-activedescendant")}))}},c=()=>Gh.getContent(r).bind((e=>te(e.components(),0))),d=()=>e.execCommand("mceAutocompleterClose"),u=s=>{const a=(o=>{const s=re(o,(e=>A.from(e.columns))).getOr(1);return Y(o,(o=>{const r=o.items;return US(r,o.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>hv(ze(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>d(),reload:t=>{l(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};n.set(!0),o.onAction(a,r,t,s),n.set(!1)}))}),s,bv.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);a.length>0?(((t,o)=>{var n;(n=ze(e.getBody()),vi(n,pv)).each((n=>{const s=re(t,(e=>A.from(e.columns))).getOr(1);Gh.showMenuAt(r,{anchor:{type:"node",root:ze(e.getBody()),node:A.from(n)}},((e,t,o,n)=>{const s=rS(t,n),r=Bv(n);return{data:jS({...e,movement:s,menuBehaviours:ux("auto"!==t?[]:[Jr(((e,t)=>{dx(e,4,r.item).each((({numColumns:t,numRows:o})=>{Gp.setGridSize(e,o,t)}))}))])}),menu:{markers:Bv(n),fakeFocus:o===HS.ContentFocus}}})(ny("autocompleter-value",!0,o,s,{menuType:"normal"}),s,HS.ContentFocus,"normal"))})),c().each(Xm.highlightFirst)})(s,a),Ct(ze(e.getBody()),"aria-owns",o),e.inline||m()):l()},m=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>na(e,!0))(r.element);It(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),vi(s,'[role="menu"]').each((e=>{Ht(e,"position"),Ht(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),u(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>u(e))),e.on("AutocompleterEnd",(()=>{l(),s.set(!1),n.set(!1)}));((e,t)=>{const o=(e,t)=>{Rr(e,Js(),{raw:t})},n=()=>e.getMenu().bind(Xm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Nr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Xm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&hv(ze(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:d,isMenuOpen:a,isActive:i,isProcessingAction:n.get,getMenu:c},e)},$S=["visible","hidden","clip"],qS=e=>Me(e).length>0&&!R($S,e),YS=e=>{if(Ge(e)){const t=Rt(e,"overflow-x"),o=Rt(e,"overflow-y");return qS(t)||qS(o)}return!1},XS=(e,t)=>uv(e)?(e=>{const t=Jc(e,YS),o=0===t.length?vt(e).map(yt).map((e=>Jc(e,YS))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),KS=e=>{const t=[...L(e.others,Zo),tn()];return((e,t)=>j(t,((e,t)=>en(e,t)),e))(Zo(e.element),t)},JS=(e,t,o)=>yi(e,t,o).isSome(),ZS=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},QS=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},ek=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=nc(),o=As(!1),n=ZS((t=>{e.triggerEvent(hr(),t),o.set(!0)}),400),s=Bs([{key:Hs(),value:e=>(QS(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Ps(),value:e=>(n.cancel(),QS(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Us(),value:s=>(n.cancel(),t.get().filter((e=>Qe(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(pr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>rc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=nc(),a=rc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(dr(),e)}),0))})),i=rc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Km[0]&&!R(["input","textarea"],We(e.target))&&!JS(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=rc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=nc(),d=rc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(cr(),e)}),0))}));return{unbind:()=>{H(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},tk=(e,t)=>{const o=be(e,"target").getOr(t);return As(o)},ok=Ms([{stopped:[]},{resume:["element"]},{complete:[]}]),nk=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=As(!1),n=As(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),ok.complete())),(e=>{const o=e.descHandler;return Ma(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),ok.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),ok.complete()):rt(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),ok.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),ok.resume(n))))}))},sk=(e,t,o,n,s,r)=>nk(e,t,o,n,s,r).fold(E,(n=>sk(e,t,o,n,s,r)),T),rk=(e,t,o,n,s)=>{const r=tk(o,n);return sk(e,t,o,n,r,s)},ak=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Rs(n,(t=>((e,t)=>ha(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ha(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ha(t).getOrThunk((()=>((e,t)=>{const o=ca(ma+"uid-");return pa(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+sa(s.element)+"\nCannot use it for: "+sa(e.element)+"\nThe conflicting element is"+(xt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},ik=xm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:xu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[xs("components",[]),yu("containerBehaviours",[]),xs("events",{}),xs("domModification",{}),xs("eventOrder",{})]}),lk=e=>{const t=t=>rt(e.element).fold(E,(e=>Qe(t,e))),o=ak(),n=(e,n)=>o.find(t,e,n),s=ek(e.element,{triggerEvent:(e,t)=>_i(e,t.target,(o=>((e,t,o,n)=>rk(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{_i(e,t,(s=>rk(n,e,o,t,s)))},triggerFocus:(e,t)=>{ha(e).fold((()=>{Rl(e)}),(o=>{_i(lr(),e,(o=>(((e,t,o,n,s)=>{const r=tk(o,n);nk(e,t,o,n,r,s)})(n,lr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:ci,buildOrPatch:li,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),qe(e.element)||(o.register(e),H(e.components(),a),r.triggerEvent(vr(),e.element,{target:e.element}))},i=e=>{qe(e.element)||(H(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Id(e,t)},c=e=>{Nd(e)},d=e=>{const t=o.filter(ur());H(t,(t=>{const o=t.descHandler;Ma(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=As(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return H(e,(e=>{const t=e.descHandler;Ma(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>rn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),rn.value),h=e=>{const t=ha(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Uo(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},ck=x([xs("prefix","form-field"),yu("fieldBehaviours",[Om,vu])]),dk=x([Yu({schema:[ns("dom")],name:"label"}),Yu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ns("text")],name:"aria-descriptor"}),$u({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[ns("factory")],name:"field"})]),uk=wm({name:"FormField",configFields:ck(),partFields:dk(),factory:(e,t,o,n)=>{const s=wu(e.fieldBehaviours,[Om.config({find:t=>am(t,e,"field")}),vu.config({store:{mode:"manual",getValue:e=>Om.getCurrent(e).bind(vu.getValue),setValue:(e,t)=>{Om.getCurrent(e).each((e=>{vu.setValue(e,t)}))}}})]),r=Hr([Jr(((t,o)=>{const n=lm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=ca(e.prefix);n.label().each((e=>{Ct(e.element,"for",o),Ct(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=ca(e.prefix);Ct(o.element,"id",n),Ct(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>am(t,e,"field"),getLabel:t=>am(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var mk=Object.freeze({__proto__:null,exhibit:(e,t)=>Aa({attributes:Bs([{key:t.tabAttr,value:"true"}])})}),gk=[xs("tabAttr","data-alloy-tabstop")];const pk=Al({fields:gk,name:"tabstopping",active:mk});var hk=tinymce.util.Tools.resolve("tinymce.html.Entities");const fk=(e,t,o,n)=>{const s=bk(e,t,o,n);return uk.sketch(s)},bk=(e,t,o,n)=>({dom:vk(o),components:e.toArray().concat([t]),fieldBehaviours:Tl(n)}),vk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),yk=(e,t)=>uk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ri(t.translate(e))]}),xk=ca("form-component-change"),wk=ca("form-close"),Sk=ca("form-cancel"),kk=ca("form-action"),Ck=ca("form-submit"),Ok=ca("form-block"),_k=ca("form-unblock"),Tk=ca("form-tabchange"),Ek=ca("form-resize"),Ak=(e,t,o)=>{const n=e.label.map((e=>yk(e,t))),s=t.icons(),r=e=>(t,o)=>{yi(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,_t(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.isDisabled()||Rr(o,kk,{name:e.name,value:r})})),i=[Wr(Ys(),r(((e,t,o)=>{Rl(o)}))),Wr(tr(),a),Wr(pr(),a),Wr(Xs(),r(((e,t,o)=>{vi(e.element,"."+_v).each((e=>{Ga(e,_v)})),Wa(o,_v)}))),Wr(Ks(),r((e=>{vi(e.element,"."+_v).each((e=>{Ga(e,_v)}))}))),ea(r(((t,o,n,s)=>{Rr(t,kk,{name:e.name,value:s})})))],l=(e,t)=>L(Zc(e.element,".tox-collection__item"),t),c=uk.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:Tl([Lm.config({disabled:t.isDisabled,onDisabled:e=>{l(e,(e=>{Wa(e,"tox-collection__item--state-disabled"),Ct(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{Ga(e,"tox-collection__item--state-disabled"),At(e,"aria-disabled")}))}}),Ox(),eh.config({}),vu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=L(n,(o=>{const n=qf.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(o.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=n.replace(/\_| \- |\-/g,(e=>i[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${hk.encodeAllRaw(o.value)}" title="${l}" aria-label="${l}">${a}${r}</div>`})),a="auto"!==e.columns&&e.columns>1?z(r,e.columns):[r],i=L(a,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));oa(o.element,i.join(""))})(o,n),"auto"===e.columns&&dx(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Gp.setGridSize(o,e,t)})),Fr(o,Ek)}}),pk.config({}),Gp.config((d=e.columns,"normal",1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${yv}`}})),th("collection-events",i)]),eventOrder:{[mr()]:["disabling","alloy.base.behaviour","collection-events"]}});var d;return fk(n,c,["tox-form__group--collection"],[])},Mk=["input","textarea"],Dk=e=>{const t=We(e);return R(Mk,t)},Bk=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ga(o,t.invalidClass),t.notify.each((t=>{Dk(e.element)&&Ct(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{oa(e,t.validHtml)})),t.onValid(e)}))},Ik=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);Wa(s,t.invalidClass),t.notify.each((t=>{Dk(e.element)&&Ct(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{oa(e,n)})),t.onInvalid(e,n)}))},Fk=(e,t,o)=>t.validator.fold((()=>SS(rn.value(!0))),(t=>t.validate(e))),Rk=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Fk(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Ik(e,t,0,o),rn.error(o))),(o=>(Bk(e,t),rn.value(o)))):rn.error("No longer in system"))));var Nk=Object.freeze({__proto__:null,markValid:Bk,markInvalid:Ik,query:Fk,run:Rk,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return $a(o,t.invalidClass)}}),Vk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hr([Wr(t.onEvent,(t=>{Rk(t,e).get(w)}))].concat(t.validateOnLoad?[Jr((t=>{Rk(t,e).get(b)}))]:[])))).getOr({})}),zk=[ns("invalidClass"),xs("getRoot",A.none),ys("notify",[xs("aria","alert"),xs("getContainer",A.none),xs("validHtml",""),Ri("onValid"),Ri("onInvalid"),Ri("onValidate")]),ys("validator",[ns("validate"),xs("onEvent","input"),xs("validateOnLoad",!0)])];const Lk=Al({fields:zk,name:"invalidating",active:Vk,apis:Nk,extra:{validation:e=>t=>{const o=vu.getValue(t);return SS(e(o))}}}),Hk=Al({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hr([Pr(rr(),E)]),exhibit:()=>Aa({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Pk=ca("color-input-change"),Uk=ca("color-swatch-change"),Wk=ca("color-picker-cancel"),jk=Yu({schema:[ns("dom")],name:"label"}),Gk=e=>Yu({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hr([jr(Hs(),((t,o,n)=>e(t,n)),[t]),jr(js(),((t,o,n)=>e(t,n)),[t]),jr(Gs(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),$k=Gk("top-left"),qk=Gk("top"),Yk=Gk("top-right"),Xk=Gk("right"),Kk=Gk("bottom-right"),Jk=Gk("bottom"),Zk=Gk("bottom-left"),Qk=Gk("left"),eC=$u({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hr([$r(Hs(),e,"spectrum"),$r(Ps(),e,"spectrum"),$r(Us(),e,"spectrum"),$r(js(),e,"spectrum"),$r(Gs(),e,"spectrum"),$r(qs(),e,"spectrum")])})}),tC=e=>ug(e.event);var oC=[jk,Qk,Xk,qk,Jk,$k,Yk,Zk,Kk,eC,$u({schema:[ts("mouseIsDown",(()=>As(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:Tl([Gp.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,tC(n)),onRight:(o,n)=>t.onRight(o,e,tC(n)),onUp:(o,n)=>t.onUp(o,e,tC(n)),onDown:(o,n)=>t.onDown(o,e,tC(n))}),pk.config({}),ah.config({})]),events:Hr([Wr(Hs(),o),Wr(Ps(),o),Wr(js(),o),Wr(Gs(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const nC=x("slider.change.value"),sC=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>qt(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>qt(e.clientX,e.clientY))):A.none()}},rC=e=>e.model.minX,aC=e=>e.model.minY,iC=e=>e.model.minX-1,lC=e=>e.model.minY-1,cC=e=>e.model.maxX,dC=e=>e.model.maxY,uC=e=>e.model.maxX+1,mC=e=>e.model.maxY+1,gC=(e,t,o)=>t(e)-o(e),pC=e=>gC(e,cC,rC),hC=e=>gC(e,dC,aC),fC=e=>pC(e)/2,bC=e=>hC(e)/2,vC=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,yC=e=>e.snapToGrid,xC=e=>e.snapStart,wC=e=>e.rounded,SC=(e,t)=>void 0!==e[t+"-edge"],kC=e=>SC(e,"left"),CC=e=>SC(e,"right"),OC=e=>SC(e,"top"),_C=e=>SC(e,"bottom"),TC=e=>e.model.value.get(),EC=(e,t)=>({x:e,y:t}),AC=(e,t)=>{Rr(e,nC(),{value:t})},MC=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),DC=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),BC=(e,t,o)=>Math.max(t,Math.min(o,e)),IC=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=BC(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return BC(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},FC=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},RC="top",NC="right",VC="bottom",zC="left",LC=e=>e.element.dom.getBoundingClientRect(),HC=(e,t)=>e[t],PC=e=>{const t=LC(e);return HC(t,zC)},UC=e=>{const t=LC(e);return HC(t,NC)},WC=e=>{const t=LC(e);return HC(t,RC)},jC=e=>{const t=LC(e);return HC(t,VC)},GC=e=>{const t=LC(e);return HC(t,"width")},$C=e=>{const t=LC(e);return HC(t,"height")},qC=(e,t,o)=>(e+t)/2-o,YC=(e,t)=>{const o=LC(e),n=LC(t),s=HC(o,zC),r=HC(o,NC),a=HC(n,zC);return qC(s,r,a)},XC=(e,t)=>{const o=LC(e),n=LC(t),s=HC(o,RC),r=HC(o,VC),a=HC(n,RC);return qC(s,r,a)},KC=(e,t)=>{Rr(e,nC(),{value:t})},JC=(e,t,o)=>{const n={min:rC(t),max:cC(t),range:pC(t),value:o,step:vC(t),snap:yC(t),snapStart:xC(t),rounded:wC(t),hasMinEdge:kC(t),hasMaxEdge:CC(t),minBound:PC(e),maxBound:UC(e),screenRange:GC(e)};return IC(n)},ZC=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?DC:MC)(TC(o),rC(o),cC(o),vC(o,n));return KC(t,s),A.some(s)})(e,t,o,n).map(E),QC=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=GC(e),a=n.bind((t=>A.some(YC(t,e)))).getOr(0),i=s.bind((t=>A.some(YC(t,e)))).getOr(r),l={min:rC(t),max:cC(t),range:pC(t),value:o,hasMinEdge:kC(t),hasMaxEdge:CC(t),minBound:PC(e),minOffset:0,maxBound:UC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return FC(l)})(t,r,o,n,s);return PC(t)-PC(e)+a},eO=ZC(-1),tO=ZC(1),oO=A.none,nO=A.none,sO={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{AC(e,uC(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{AC(e,iC(t))}))};var rO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=JC(e,t,o);return KC(e,n),n},setToMin:(e,t)=>{const o=rC(t);KC(e,o)},setToMax:(e,t)=>{const o=cC(t);KC(e,o)},findValueOfOffset:JC,getValueFromEvent:e=>sC(e).map((e=>e.left)),findPositionOfValue:QC,setPositionFromValue:(e,t,o,n)=>{const s=TC(o),r=QC(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Zt(t.element)/2;Bt(t.element,"left",r-a+"px")},onLeft:eO,onRight:tO,onUp:oO,onDown:nO,edgeActions:sO});const aO=(e,t)=>{Rr(e,nC(),{value:t})},iO=(e,t,o)=>{const n={min:aC(t),max:dC(t),range:hC(t),value:o,step:vC(t),snap:yC(t),snapStart:xC(t),rounded:wC(t),hasMinEdge:OC(t),hasMaxEdge:_C(t),minBound:WC(e),maxBound:jC(e),screenRange:$C(e)};return IC(n)},lO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?DC:MC)(TC(o),aC(o),dC(o),vC(o,n));return aO(t,s),A.some(s)})(e,t,o,n).map(E),cO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=$C(e),a=n.bind((t=>A.some(XC(t,e)))).getOr(0),i=s.bind((t=>A.some(XC(t,e)))).getOr(r),l={min:aC(t),max:dC(t),range:hC(t),value:o,hasMinEdge:OC(t),hasMaxEdge:_C(t),minBound:WC(e),minOffset:0,maxBound:jC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return FC(l)})(t,r,o,n,s);return WC(t)-WC(e)+a},dO=A.none,uO=A.none,mO=lO(-1),gO=lO(1),pO={"top-left":A.none(),top:A.some(((e,t)=>{AC(e,lC(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{AC(e,mC(t))})),"bottom-left":A.none(),left:A.none()};var hO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=iO(e,t,o);return aO(e,n),n},setToMin:(e,t)=>{const o=aC(t);aO(e,o)},setToMax:(e,t)=>{const o=dC(t);aO(e,o)},findValueOfOffset:iO,getValueFromEvent:e=>sC(e).map((e=>e.top)),findPositionOfValue:cO,setPositionFromValue:(e,t,o,n)=>{const s=TC(o),r=cO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=jt(t.element)/2;Bt(t.element,"top",r-a+"px")},onLeft:dO,onRight:uO,onUp:mO,onDown:gO,edgeActions:pO});const fO=(e,t)=>{Rr(e,nC(),{value:t})},bO=(e,t)=>({x:e,y:t}),vO=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?DC:MC,a=t?TC(n).x:r(TC(n).x,rC(n),cC(n),vC(n,s)),i=t?r(TC(n).y,aC(n),dC(n),vC(n,s)):TC(n).y;return fO(o,bO(a,i)),A.some(a)})(e,t,o,n,s).map(E),yO=vO(-1,!1),xO=vO(1,!1),wO=vO(-1,!0),SO=vO(1,!0),kO={"top-left":A.some(((e,t)=>{AC(e,EC(iC(t),lC(t)))})),top:A.some(((e,t)=>{AC(e,EC(fC(t),lC(t)))})),"top-right":A.some(((e,t)=>{AC(e,EC(uC(t),lC(t)))})),right:A.some(((e,t)=>{AC(e,EC(uC(t),bC(t)))})),"bottom-right":A.some(((e,t)=>{AC(e,EC(uC(t),mC(t)))})),bottom:A.some(((e,t)=>{AC(e,EC(fC(t),mC(t)))})),"bottom-left":A.some(((e,t)=>{AC(e,EC(iC(t),mC(t)))})),left:A.some(((e,t)=>{AC(e,EC(iC(t),bC(t)))}))};var CO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=JC(e,t,o.left),s=iO(e,t,o.top),r=bO(n,s);return fO(e,r),r},setToMin:(e,t)=>{const o=rC(t),n=aC(t);fO(e,bO(o,n))},setToMax:(e,t)=>{const o=cC(t),n=dC(t);fO(e,bO(o,n))},getValueFromEvent:e=>sC(e),setPositionFromValue:(e,t,o,n)=>{const s=TC(o),r=QC(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=cO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Zt(t.element)/2,l=jt(t.element)/2;Bt(t.element,"left",r-i+"px"),Bt(t.element,"top",a-l+"px")},onLeft:yO,onRight:xO,onUp:wO,onDown:SO,edgeActions:kO});const OO=wm({name:"Slider",configFields:[xs("stepSize",1),xs("speedMultiplier",10),xs("onChange",b),xs("onChoose",b),xs("onInit",b),xs("onDragStart",b),xs("onDragEnd",b),xs("snapToGrid",!1),xs("rounded",!0),ms("snapStart"),ss("model",Zn("mode",{x:[xs("minX",0),xs("maxX",100),ts("value",(e=>As(e.mode.minX))),ns("getInitialValue"),Li("manager",rO)],y:[xs("minY",0),xs("maxY",100),ts("value",(e=>As(e.mode.minY))),ns("getInitialValue"),Li("manager",hO)],xy:[xs("minX",0),xs("maxX",100),xs("minY",0),xs("maxY",100),ts("value",(e=>As({x:e.mode.minX,y:e.mode.minY}))),ns("getInitialValue"),Li("manager",CO)]})),yu("sliderBehaviours",[Gp,vu]),ts("mouseIsDown",(()=>As(!1)))],partFields:oC,factory:(e,t,o,n)=>{const s=t=>im(t,e,"thumb"),r=t=>im(t,e,"spectrum"),a=t=>am(t,e,"left-edge"),i=t=>am(t,e,"right-edge"),l=t=>am(t,e,"top-edge"),c=t=>am(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&am(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{am(t,e,"spectrum").map(Gp.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.sliderBehaviours,[Gp.config({mode:"special",focusIn:b}),vu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Il.config({channels:{[tu()]:{onReceive:p}}})]),events:Hr([Wr(nC(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Jr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Wr(Hs(),h),Wr(Us(),f),Wr(js(),((e,t)=>{b(e),h(e,t)})),Wr(qs(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),_O=ca("rgb-hex-update"),TO=ca("slider-update"),EO=ca("palette-update"),AO="form",MO=[yu("formBehaviours",[vu])],DO=e=>"<alloy.field."+e+">",BO=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.formBehaviours,[vu.config({store:{mode:"manual",getValue:t=>{const o=cm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=Om.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+sa(e.element)),o.fold((()=>rn.error(n)),rn.value);var o,n})).map(vu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{am(t,e,n).each((e=>{Om.getCurrent(e).each((e=>{vu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>am(t,e,o).bind(Om.getCurrent)}}),IO={getField:Oa(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),tm(AO,DO(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>$u({name:e,pname:DO(e)})));return fm(AO,MO,s,BO,o)}},FO=ca("valid-input"),RO=ca("invalid-input"),NO=ca("validating-input"),VO="colorcustom.rgb.",zO=(e,t,o,n)=>{const s=(o,n)=>Lk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Rr(e,NO,{type:o})},onValid:e=>{Rr(e,FO,{type:o,value:vu.getValue(e)})},onInvalid:e=>{Rr(e,RO,{type:o,value:vu.getValue(e)})}},validator:{validate:t=>{const o=vu.getValue(t),s=n(o)?rn.value(!0):rn.error(e("aria.input.invalid"));return SS(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(VO+"range"),c=uk.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ri(r)]}),d=uk.parts.field({data:i,factory:Hv,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:Tl([s(n,o),pk.config({})]),onSetValue:e=>{Lk.isInvalid(e)&&Lk.run(e).get(b)}}),u=[c,d],m="hex"!==n?[uk.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;vu.setValue(e,{red:o,green:n,blue:s})},i=Xh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{Bt(e.element,"background-color","#"+t.value)}))},c=xm({factory:()=>{const s={red:As(A.some(255)),green:As(A.some(255)),blue:As(A.some(255)),hex:As(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Kx(t);d("hex",A.some(n.value));const s=uw(n);a(e,s),u(s),Rr(e,_O,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>lw(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=ow(t);return IO.getField(e,"hex").each((t=>{ah.isFocused(t)||vu.setValue(e,{hex:o.value})})),o})(e,t);Rr(e,_O,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(VO+t+".label"),description:e(VO+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return bn(IO.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",uk.sketch(r(cw,"red",h.label,h.description,255))),o.field("green",uk.sketch(r(cw,"green",f.label,f.description,255))),o.field("blue",uk.sketch(r(cw,"blue",b.label,b.description,255))),o.field("hex",uk.sketch(r(Qx,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:Tl([Lk.config({invalidClass:t("form-invalid")}),th("rgb-form-events",[Wr(FO,g),Wr(RO,m),Wr(NO,m)])])}))),{apis:{updateHex:(e,t)=>{vu.setValue(e,{hex:t.value}),((e,t)=>{const o=uw(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},LO=(e,t)=>{const o=xm({name:"ColourPicker",configFields:[ns("dom"),xs("onValidHex",b),xs("onInvalidHex",b)],factory:o=>{const n=zO(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=OO.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=OO.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return xm({factory:r=>{const a=x({x:0,y:0}),i=Tl([Om.config({find:A.some}),ah.config({})]);return OO.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||Ct(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Rr(t,EO,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,pw(hw))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Aw(t,100,100),r=dw(n);s(o,pw(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=Mw(uw(o));OO.setValue(t,{x:n.saturation,y:100-n.value}),Ct(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),r={paletteRgba:As(hw),paletteHue:As(0)},a=Xh(((e,t)=>{const o=OO.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=OO.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return OO.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:Tl([ah.config({})]),onChange:(e,t,o)=>{Ct(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Rr(e,TO,{value:o})}})})(0,t)),i=Xh(s.sketch({})),l=Xh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{OO.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=uw(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),H(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:Tl([th("colour-picker-events",[Wr(_O,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Mw(uw(e)))(n);g(t,n,s.hue,e)}})()),Wr(EO,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Aw(s,n.x,100-n.y),i=Dw(a);g(t,i,s,e)}})()),Wr(TO,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Mw(s),i=Aw(n,a.saturation,a.value),l=Dw(i);g(t,l,n,e)}})())]),Om.config({find:e=>l.getOpt(e)}),Gp.config({mode:"acyclic"})])}}});return o},HO=()=>Om.config({find:A.some}),PO=e=>Om.config({find:t=>ct(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),UO=Bn([xs("preprocess",w),xs("postprocess",w)]),WO=(e,t)=>{const o=Kn("RepresentingConfigs.memento processors",UO,t);return vu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=vu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);vu.setValue(r,s)}}})},jO=(e,t,o)=>vu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),GO=(e,t,o)=>jO(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),$O=e=>vu.config({store:{mode:"memory",initialValue:e}}),qO={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var YO=tinymce.util.Tools.resolve("tinymce.Resource"),XO=tinymce.util.Tools.resolve("tinymce.util.Tools");const KO=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},JO=ca("alloy-fake-before-tabstop"),ZO=ca("alloy-fake-after-tabstop"),QO=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:Tl([ah.config({ignore:!0}),pk.config({})])}),e_=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[QO([JO]),t,QO([ZO])],behaviours:Tl([PO(1)])}),t_=(e,t)=>{Rr(e,Js(),{raw:{which:9,shiftKey:t}})},o_=(e,t)=>{const o=t.element;$a(o,JO)?t_(e,!0):$a(o,ZO)&&t_(e,!1)},n_=e=>JS(e,["."+JO,"."+ZO].join(","),T),s_=ca("update-dialog"),r_=ca("update-title"),a_=ca("update-body"),i_=ca("update-footer"),l_=ca("body-send-message"),c_=ca("dialog-focus-shifted"),d_=Bo().browser,u_=d_.isSafari(),m_=d_.isFirefox(),g_=u_||m_,p_=d_.isChromium(),h_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,f_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),b_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!p_&&!u_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(h_),r=()=>{const e=n.contentWindow;g(e)&&(s?f_(e,"bottom"):!s&&g_&&0!==o&&f_(e,o))};u_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),u_||r()}))},v_=Ce(g_,u_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(b_,e))),y_=ca("toolbar.button.execute"),x_=ca("common-button-display-events"),w_={[mr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],[kr()]:["toolbar-button-events",x_],[js()]:["focusing","alloy.base.behaviour",x_]},S_=e=>Bt(e.element,"width",Rt(e.element,"width")),k_=(e,t,o)=>ob(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),C_=(e,t)=>k_(e,t,[]),O_=(e,t)=>k_(e,t,[eh.config({})]),__=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ri(o.translate(e))],behaviours:Tl([eh.config({})])}),T_=ca("update-menu-text"),E_=ca("update-menu-icon"),A_=(e,t,o)=>{const n=As(b),s=e.text.map((e=>Xh(__(e,t,o.providers)))),r=e.icon.map((e=>Xh(O_(e,o.providers.icons)))),a=(e,t)=>{const o=vu.getValue(e);return ah.focus(o),Rr(o,"keydown",{raw:t.event.raw}),NS.close(o),A.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=ob("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=ca("common-button-display-events"),u=Xh(NS.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Fx([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),A.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Gv(e).each((e=>ah.focus(e)))})(n)},dropdownBehaviours:Tl([...e.dropdownBehaviours,_x((()=>e.disabled||o.providers.isDisabled())),Ox(),Hk.config({}),eh.config({}),th("dropdown-events",[Mx(e,n),Dx(e,n)]),th(d,[Jr(((e,t)=>S_(e)))]),th("menubutton-update-display-text",[Wr(T_,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{eh.set(e,[ri(o.providers.translate(t.event.text))])}))})),Wr(E_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{eh.set(e,[O_(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:bn(w_,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[kr()]:["toolbar-button-events","dropdown-events",d]}),sandboxBehaviours:Tl([Gp.config({mode:"special",onLeft:a,onRight:a}),th("dropdown-sandbox-events",[Wr(Pv,((e,t)=>{(e=>{const t=vu.getValue(e),o=jv(e).map($v);NS.refetch(t).get((()=>{const e=fS.getCoupled(t,"sandbox");o.each((t=>jv(e).each((e=>((e,t)=>{vu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Wr(Uv,((e,t)=>{((e,t)=>{(e=>Zd.getState(e).bind(Xm.getHighlighted).bind(Xm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Rv(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:VS,onCollapseMenu:(e,t,o)=>{Xm.getHighlighted(o).each((t=>{VS(e,o,t)}))},onDehighlightItem:zS}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{mc()(e,t-10)}}),fetch:t=>wS(k(e.fetch,t))}));return u.asSpec()},M_=e=>"separator"===e.type,D_={type:"separator"},B_=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!M_(e[e.length-1])?e.concat([D_]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&M_(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>ca("generated-menu-item")));return bn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=B_(o,t);return{item:e,menus:bn(n.menus,{[e.value]:n.items}),expansions:bn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:bn(e.menus,s.menus),items:[s.item,...e.items],expansions:bn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},I_=(e,t,o,n)=>{const s=ca("primary-menu"),r=B_(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=WS(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>WS(n,e,t,o,!1,l))),d=bn(c,Ds(s,i));return A.from(jh.tieredData(s,d,r.expansions))},F_=e=>!ve(e,"items"),R_="data-value",N_=(e,t,o,n)=>L(o,(o=>F_(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{vu.setValue(e,o.value),Rr(e,xk,{name:t}),ah.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>N_(e,t,o.items,n)})),V_=(e,t)=>re(e,(e=>F_(e)?Ce(e.value===t,e):V_(e.items,t))),z_=xm({name:"HtmlSelect",configFields:[ns("options"),yu("selectBehaviours",[ah,vu]),xs("selectClasses",[]),xs("selectAttributes",{}),ms("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ds("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:wu(e.selectBehaviours,[ah.config({}),vu.config({store:{mode:"manual",getValue:e=>Ka(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?Ja(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>Ja(t.element,e.value)))},...n}})])}}}),L_=x([xs("field1Name","field1"),xs("field2Name","field2"),Vi("onLockedChange"),Ii(["lockClass"]),xs("locked",!1),Su("coupledFieldBehaviours",[Om,vu])]),H_=(e,t)=>$u({factory:uk,name:e,overrides:e=>({fieldBehaviours:Tl([th("coupled-input-behaviour",[Wr(Qs(),(o=>{((e,t,o)=>am(e,t,o).bind(Om.getCurrent))(o,e,t).each((t=>{am(o,e,"lock").each((n=>{ph.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),P_=x([H_("field1","field2"),H_("field2","field1"),$u({factory:qh,schema:[ns("dom")],name:"lock",overrides:e=>({buttonBehaviours:Tl([ph.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),U_=wm({name:"FormCoupledInputs",configFields:L_(),partFields:P_(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:ku(e.coupledFieldBehaviours,[Om.config({find:A.some}),vu.config({store:{mode:"manual",getValue:t=>{const o=um(t,e,["field1","field2"]);return{[e.field1Name]:vu.getValue(o.field1()),[e.field2Name]:vu.getValue(o.field2())}},setValue:(t,o)=>{const n=um(t,e,["field1","field2"]);ye(o,e.field1Name)&&vu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&vu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>am(t,e,"field1"),getField2:t=>am(t,e,"field2"),getLock:t=>am(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),W_=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return rn.value({value:e,unit:o})}return rn.error(e)},j_=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},G_=e=>A.none(),$_=(e,t)=>{const o=e.label.map((e=>yk(e,t))),n=[Lm.config({disabled:()=>e.disabled||t.isDisabled()}),Ox(),Gp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Fr(e,Ck),A.some(!0))}),th("textfield-change",[Wr(Qs(),((t,o)=>{Rr(t,xk,{name:e.name})})),Wr(dr(),((t,o)=>{Rr(t,xk,{name:e.name})}))]),pk.config({})],s=e.validation.map((e=>Lk.config({getRoot:e=>at(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=vu.getValue(t),n=e.validator(o);return SS(!0===n?rn.value(o):rn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=uk.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:Tl(q([n,s])),selectOnFocus:!1,factory:Hv}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Lm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{uk.getField(e).each(Lm.disable)},onEnabled:e=>{uk.getField(e).each(Lm.enable)}}),Ox()];return fk(o,i,l,c)},q_=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),Y_=e=>e.dimension.property,X_=(e,t)=>e.dimension.getDimension(t),K_=(e,t)=>{const o=q_(e,t);Ya(o,[t.shrinkingClass,t.growingClass])},J_=(e,t)=>{Ga(e.element,t.openClass),Wa(e.element,t.closedClass),Bt(e.element,Y_(t),"0px"),Pt(e.element)},Z_=(e,t)=>{Ga(e.element,t.closedClass),Wa(e.element,t.openClass),Ht(e.element,Y_(t))},Q_=(e,t,o,n)=>{o.setCollapsed(),Bt(e.element,Y_(t),X_(t,e.element)),K_(e,t),J_(e,t),t.onStartShrink(e),t.onShrunk(e)},eT=(e,t,o,n)=>{const s=n.getOrThunk((()=>X_(t,e.element)));o.setCollapsed(),Bt(e.element,Y_(t),s),Pt(e.element);const r=q_(e,t);Ga(r,t.growingClass),Wa(r,t.shrinkingClass),J_(e,t),t.onStartShrink(e)},tT=(e,t,o)=>{const n=X_(t,e.element);("0px"===n?Q_:eT)(e,t,o,A.some(n))},oT=(e,t,o)=>{const n=q_(e,t),s=$a(n,t.shrinkingClass),r=X_(t,e.element);Z_(e,t);const a=X_(t,e.element);(s?()=>{Bt(e.element,Y_(t),r),Pt(e.element)}:()=>{J_(e,t)})(),Ga(n,t.shrinkingClass),Wa(n,t.growingClass),Z_(e,t),Bt(e.element,Y_(t),a),o.setExpanded(),t.onStartGrow(e)},nT=(e,t,o)=>{const n=q_(e,t);return!0===$a(n,t.growingClass)},sT=(e,t,o)=>{const n=q_(e,t);return!0===$a(n,t.shrinkingClass)};var rT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Ht(e.element,Y_(t));const o=X_(t,e.element);Bt(e.element,Y_(t),o)}},grow:(e,t,o)=>{o.isExpanded()||oT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&tT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&Q_(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:nT,isShrinking:sT,isTransitioning:(e,t,o)=>nT(e,t)||sT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?tT:oT)(e,t,o)},disableTransitions:K_,immediateGrow:(e,t,o)=>{o.isExpanded()||(Z_(e,t),Bt(e.element,Y_(t),X_(t,e.element)),K_(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),aT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Aa(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ds(t.dimension.property,"0px")})},events:(e,t)=>Hr([Kr(nr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(K_(o,e),t.isExpanded()&&Ht(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),iT=[ns("closedClass"),ns("openClass"),ns("shrinkingClass"),ns("growingClass"),ms("getAnimationRoot"),Ri("onShrunk"),Ri("onStartShrink"),Ri("onGrown"),Ri("onStartGrow"),xs("expanded",!1),ss("dimension",Zn("property",{width:[Li("property","width"),Li("getDimension",(e=>Zt(e)+"px"))],height:[Li("property","height"),Li("getDimension",(e=>jt(e)+"px"))]}))];const lT=Al({fields:iT,name:"sliding",active:aT,apis:rT,state:Object.freeze({__proto__:null,init:e=>{const t=As(e.expanded);return Ta({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),cT=e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t),setActive:t=>{const o=e.element;t?(Wa(o,"tox-tbtn--enabled"),Ct(o,"aria-pressed",!0)):(Ga(o,"tox-tbtn--enabled"),At(o,"aria-pressed"))},isActive:()=>$a(e.element,"tox-tbtn--enabled"),setText:t=>{Rr(e,T_,{text:t})},setIcon:t=>Rr(e,E_,{icon:t})}),dT=(e,t,o,n,s=!0)=>A_({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?LS(t):""};e.fetch((t=>{n(I_(t,bv.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,cT(t))},onSetup:e.onSetup,getApi:cT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[pk.config({})]:[]]},t,o.shared),uT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Rl(t.element),Rr(t,kk,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},mT=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{title:e,"aria-label":e}},components:[ri(e)]}),gT=ca("leaf-label-event-id"),pT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>dT(e,"tox-mbtn",r,A.none(),o))),i=[mT(e.title)];return a.each((e=>i.push(e))),qh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Js()]:[gT,"keying"]},buttonBehaviours:Tl([...o?[pk.config({})]:[],ph.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Il.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?ph.on:ph.off)(t)}}}}),th(gT,[Jr(((t,o)=>{s.each((o=>{(o===e.id?ph.on:ph.off)(t)}))})),Wr(Js(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(fi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{bi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(ah.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},hT=ca("directory-label-event-id"),fT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>dT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>ob(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},mT(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{fi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!ph.isOn(o);ph.toggle(o),Rr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return qh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Js()]:[hT,"keying"]},buttonBehaviours:Tl([...t?[pk.config({})]:[],th(hT,[Wr(Js(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&fi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!ph.isOn(o)&&n||ph.isOn(o)&&s?(l(e),t.stop()):s&&!ph.isOn(o)&&(fi(o.element,".tox-tree--directory").each((e=>{bi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(ah.focus)}))})),t.stop())}))}))}))])])})},bT=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?pT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):yT({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:Tl([lT.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),eh.config({})])}),vT=ca("directory-event-id"),yT=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=As(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[fT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),bT({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:Tl([th(vT,[Jr(((e,t)=>{ph.set(e,c)})),Wr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),ph.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?pT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):yT({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?lT.grow(r):lT.shrink(r),eh.set(r,c)}})])}},xT=ca("tree-event-id");var wT=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hr([Wr(e.event,o),Zr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Wr(e,(()=>t.cancel()))])).getOr([])))}});const ST=e=>{const t=As(null);return Ta({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var kT=Object.freeze({__proto__:null,throttle:ST,init:e=>e.stream.streams.state(e)}),CT=[ss("stream",Zn("mode",{throttle:[ns("delay"),xs("stopEvent",!0),Li("streams",{setup:(e,t)=>{const o=e.stream,n=KO(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:ST})]})),xs("event","input"),ms("cancelEvent"),Vi("onStream")];const OT=Al({fields:CT,name:"streaming",active:wT,state:kT}),_T=(e,t,o)=>{const n=vu.getValue(o);vu.setValue(t,n),ET(t)},TT=(e,t)=>{const o=e.element,n=Ka(o),s=o.dom;"number"!==_t(o,"type")&&t(s,n)},ET=e=>{TT(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},AT=x("alloy.typeahead.itemexecute"),MT=x([ms("lazySink"),ns("fetch"),xs("minChars",5),xs("responseTime",1e3),Ri("onOpen"),xs("getHotspot",A.some),xs("getAnchorOverrides",x({})),xs("layouts",A.none()),xs("eventOrder",{}),Es("model",{},[xs("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),xs("selectsOver",!0),xs("populateFromBrowse",!0)]),Ri("onSetValue"),Ni("onExecute"),Ri("onItemExecute"),xs("inputClasses",[]),xs("inputAttributes",{}),xs("inputStyles",{}),xs("matchWidth",!0),xs("useMinWidth",!1),xs("dismissOnBlur",!0),Ii(["openClass"]),ms("initialData"),yu("typeaheadBehaviours",[ah,vu,OT,Gp,ph,fS]),ts("lazyTypeaheadComp",(()=>As(A.none))),ts("previewing",(()=>As(!0)))].concat(Nv()).concat(IS())),DT=x([qu({schema:[Bi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=vu.getValue(t),s=e.getDisplayText(n),r=vu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{_T(0,t,o),((e,t)=>{TT(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Xm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&_T(e.model,t,n),Tt(n.element,"id").each((e=>Ct(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Rr(e,AT(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&_T(e.model,t,o)}))}})})]),BT=wm({name:"Typeahead",configFields:MT(),partFields:DT(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=fS.getCoupled(t,"sandbox");if(Zd.isOpen(r))Om.getCurrent(r).each((e=>{Xm.getHighlighted(e).fold((()=>{s(e)}),(()=>{Lr(r,e.element,"keydown",o)}))}));else{const o=e=>{Om.getCurrent(e).each(s)};_S(e,a(t),t,r,n,o,Uh.HighlightMenuAndItem).get(b)}},r=Vv(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=Y(o,(e=>U(e.items,(e=>"item"===e.type))));return vu.getState(e).update(L(n,(e=>e.data))),t})),i=e=>Om.getCurrent(e),l="typeaheadevents",c=[ah.config({}),vu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Ka(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Ja(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ds("initialValue",e))).getOr({})}}),OT.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=fS.getCoupled(t,"sandbox");if(ah.isFocused(t)&&Ka(t.element).length>=e.minChars){const o=i(s).bind((e=>Xm.getHighlighted(e).map(vu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Xm.highlightFirst(t)}),(e=>{Xm.highlightBy(t,(t=>vu.getValue(t).value===e.value)),Xm.getHighlighted(t).orThunk((()=>(Xm.highlightFirst(t),A.none())))}))}))};_S(e,a(t),t,s,n,r,Uh.HighlightJustMenu).get(b)}},cancelEvent:br()}),Gp.config({mode:"special",onDown:(e,t)=>(s(e,t,Xm.highlightFirst),A.some(!0)),onEscape:e=>{const t=fS.getCoupled(e,"sandbox");return Zd.isOpen(t)?(Zd.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Xm.highlightLast),A.some(!0)),onEnter:t=>{const o=fS.getCoupled(t,"sandbox"),n=Zd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Xm.getHighlighted(e))).map((e=>(Rr(t,AT(),{item:e}),!0)));{const s=vu.getValue(t);return Fr(t,br()),e.onExecute(o,t,s),n&&Zd.close(o),A.some(!0)}}}),ph.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),fS.config({others:{sandbox:t=>DS(e,t,{onOpen:()=>ph.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>At(e.element,"aria-activedescendant"))),ph.off(t)}})}}),th(l,[Jr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Zr((t=>{e.lazyTypeaheadComp.set(A.none())})),ea((t=>{const o=b;ES(e,a(t),t,n,o,Uh.HighlightMenuAndItem).get(b)})),Wr(AT(),((t,o)=>{const n=fS.getCoupled(t,"sandbox");_T(e.model,t,o.event.item),Fr(t,br()),e.onItemExecute(t,n,o.event.item,vu.getValue(t)),Zd.close(n),ET(t)}))].concat(e.dismissOnBlur?[Wr(cr(),(e=>{const t=fS.getCoupled(e,"sandbox");Ll(t.element).isNone()&&Zd.close(t)}))]:[]))],d={[Cr()]:[vu.name(),OT.name(),l],...e.eventOrder};return{uid:e.uid,dom:Lv(bn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...wu(e.typeaheadBehaviours,c)},eventOrder:d}}}),IT=e=>({...e,toCached:()=>IT(e.toCached()),bindFuture:t=>IT(e.bind((e=>e.fold((e=>SS(rn.error(e))),(e=>t(e)))))),bindResult:t=>IT(e.map((e=>e.bind(t)))),mapResult:t=>IT(e.map((e=>e.map(t)))),mapError:t=>IT(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>IT(wS((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(rn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),FT=e=>IT(wS(e)),RT=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:Tl([_x((()=>!e.enabled||r.isDisabled())),Ox(),pk.config({}),th("button press",[Ur("click"),Ur("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=bn(i,{dom:n});return bn(l,{components:s})},NT=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>C_(e,o.icons))),a=Fx([r]);return RT(e,t,n,s,a,o)},VT=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},zT=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>C_(e,o.icons))),i=[a.getOrThunk((()=>ri(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c=[...VT(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return RT(e,t,n,{tag:"button",classes:c,attributes:{title:r}},i,o)},LT=(e,t,o,n=[],s=[])=>{const r=zT(e,A.some(t),o,n,s);return qh.sketch(r)},HT=(e,t)=>o=>{"custom"===t?Rr(o,kk,{name:e,value:{}}):"submit"===t?Fr(o,Ck):"cancel"===t?Fr(o,Sk):console.error("Unknown button type: ",t)},PT=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:uT(n.items,t,o)},r=Xh(dT(s,"tox-tbtn",o,A.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=HT(e.name,t),s={...e,borderless:!1};return LT(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t)=>{var o,n;const s=e.icon.map((e=>O_(e,t.icons))).map(Xh),r=e.buttonType.getOr(e.primary?"primary":"secondary"),a={...e,name:null!==(o=e.name)&&void 0!==o?o:"",primary:"primary"===r,tooltip:A.from(e.tooltip),enabled:null!==(n=e.enabled)&&void 0!==n&&n,borderless:!1},i=a.tooltip.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),l=VT(null!=r?r:"secondary"),c=e.icon.isSome()&&e.text.isSome(),d={tag:"button",classes:[...l.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...c?["tox-button--icon-and-text"]:[]],attributes:i},u=t.translate(e.text.getOr("")),m=ri(u),g=[...Fx([s.map((e=>e.asSpec()))]),...e.text.isSome()?[m]:[]],p=RT(a,A.some((o=>{Rr(o,kk,{name:e.name,value:{setIcon:e=>{s.map((n=>n.getOpt(o).each((o=>{eh.set(o,[O_(e,t.icons)])}))))}}})})),[],d,g,t);return qh.sketch(p)})(e,o.shared.providers);throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},UT={type:"separator"},WT=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),jT=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),GT=(e,t)=>(e=>L(e,WT))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),$T=e=>GT("header",e.targets),qT=e=>GT("anchor",e.targets),YT=e=>A.from(e.anchorTop).map((e=>jT("<top>",e))).toArray(),XT=e=>A.from(e.anchorBottom).map((e=>jT("<bottom>",e))).toArray(),KT=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},JT=ca("aria-invalid"),ZT=(e,t)=>{e.dom.checked=t},QT=e=>e.dom.checked,eE=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),tE={bar:eE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:eE(((e,t,o)=>Ak(e,t.shared.providers,o))),alertbanner:eE(((e,t)=>((e,t)=>{const o=Qf(e.icon,t.icons);return ik.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[qh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Rr(t,kk,{name:"alert-banner",value:e.url}),buttonBehaviours:Tl([eb()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:eE(((e,t,o)=>((e,t,o)=>$_({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:eE(((e,t,o)=>((e,t,o)=>$_({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:eE(((e,t)=>((e,t)=>{const o="tox-label";return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:[o,..."center"===e.align?[`${o}--center`]:[],..."end"===e.align?[`${o}--end`]:[]]},components:[ri(t.providers.translate(e.label))]},...L(e.items,t.interpreter)],behaviours:Tl([HO(),eh.config({}),(n=A.none(),GO(n,ta,oa)),Gp.config({mode:"acyclic"})])};var n})(e,t.shared))),iframe:(TA=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=As(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>Ct(o,"srcdoc",n);t?v_.fold(x(b_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>yk(e,t))),c=uk.parts.field({factory:{sketch:e=>e_(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:Tl([pk.config({}),ah.config({}),jO(o,i.getValue,i.setValue),Il.config({channels:{[c_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{at(e.element).each((o=>{(Qe(e.element,t)?Wa:Ga)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return fk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=bn(t,{source:"dynamic"});return eE(TA)(e,s,o,n)}),button:eE(((e,t)=>((e,t)=>{const o=HT(e.name,"custom");return n=A.none(),s=uk.parts.field({factory:qh,...zT(e,A.some(o),t,[$O(""),HO()])}),fk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:eE(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=uk.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:Tl([HO(),Lm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{at(e.element).each((e=>Wa(e,"tox-checkbox--disabled")))},onEnabled:e=>{at(e.element).each((e=>Ga(e,"tox-checkbox--disabled")))}}),pk.config({}),ah.config({}),GO(o,QT,ZT),Gp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),th("checkbox-events",[Wr(er(),((t,o)=>{Rr(t,xk,{name:e.name})}))])])}),r=uk.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ri(t.translate(e.label))],behaviours:Tl([Hk.config({})])}),a=e=>ob("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Xh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return uk.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled()}),Ox()])})})(e,t.shared.providers,o))),colorinput:eE(((e,t,o)=>((e,t,o,n)=>{const s=uk.parts.field({factory:Hv,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Lk.run(e).get(b),inputBehaviours:Tl([Lm.config({disabled:t.providers.isDisabled}),Ox(),pk.config({}),Lk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>at(e.element),notify:{onValid:e=>{const t=vu.getValue(e);Rr(e,Pk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=vu.getValue(e);if(0===t.length)return SS(rn.value(!0));{const e=Ne("span");Bt(e,"background-color",t);const o=Vt(e,"background-color").fold((()=>rn.error("blah")),(e=>rn.value(t)));return SS(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>yk(e,t.providers))),a=(e,t)=>{Rr(e,Uk,{value:t})},i=Xh(((e,t)=>NS.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:Tl([_x(t.providers.isDisabled),Ox(),Hk.config({}),pk.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>wS((t=>e.fetch(t))).map((n=>A.from(jS(bn(nS(ca("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,bv.CLOSE_ON_EXECUTE,T,t.providers),{movement:rS(e.columns,e.presets)}))))),parts:{menu:Rv(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[cl,ll,gl],onLtr:()=>[ll,cl,gl]},components:[],fetch:Xw(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Fr(t,Wk)),(o=>{a(t,o),Ew(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return uk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:Tl([th("form-field-events",[Wr(Pk,((t,o)=>{i.getOpt(t).each((e=>{Bt(e.element,"background-color",o.event.color)})),Rr(t,xk,{name:e.name})})),Wr(Uk,((e,t)=>{uk.getField(e).each((o=>{vu.setValue(o,t.event.value),Om.getCurrent(e).each(ah.focus)}))})),Wr(Wk,((e,t)=>{uk.getField(e).each((t=>{Om.getCurrent(e).each(ah.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:eE(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=LO((e=>t=>r(t)?e.translate(qO[t]):e.translate(t))(t),n),a=Xh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Rr(e,kk,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Rr(e,kk,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:Tl([jO(o,(e=>{const t=a.get(e);return Om.getCurrent(t).bind((e=>vu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=a.get(e);Om.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{vu.setValue(e,{hex:o.getOr("")}),IO.getField(e,"hex").each((e=>{Fr(e,Qs())}))}))})),HO()])}})(0,t.shared.providers,o))),dropzone:eE(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{H(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Lm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(vu.setValue(o,((e,t)=>{const o=XO.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Rr(o,xk,{name:e.name}))},l=Xh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:Tl([th("input-file-events",[Yr(tr()),Yr(pr())])])}),c=e.label.map((e=>yk(e,t))),d=uk.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:Tl([$O(o.getOr([])),HO(),Lm.config({}),ph.config({toggleClass:"dragenter",toggleOnExecute:!1}),th("dropzone-events",[Wr("dragenter",s([n,ph.toggle])),Wr("dragleave",s([n,ph.toggle])),Wr("dragover",n),Wr("drop",s([n,r])),Wr(er(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ri(t.translate("Drop an image here"))]},qh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ri(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:Tl([pk.config({}),_x(t.isDisabled),Ox()])})]}]})}});return fk(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:eE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:eE(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>V_(e.items,t))).orThunk((()=>oe(e.items).filter(F_))),r=e.label.map((e=>yk(e,n))),a=uk.parts.field({dom:{},factory:{sketch:o=>A_({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:e.label,role:A.none(),fetch:(o,n)=>{const s=N_(o,e.name,e.items,vu.getValue(o));n(I_(s,bv.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[pk.config({}),jO(s.map((e=>e.value)),(e=>_t(e.element,R_)),((t,o)=>{V_(e.items,o).each((e=>{Ct(t.element,R_,e.value),Rr(t,T_,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return uk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:Tl([Lm.config({disabled:x(!e.enabled),onDisabled:e=>{uk.getField(e).each(Lm.disable)},onEnabled:e=>{uk.getField(e).each(Lm.enable)}})])})})(e,t,o))),selectbox:eE(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>yk(e,t))),r=uk.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:z_,selectBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled()}),pk.config({}),th("selectbox-change",[Wr(er(),((t,o)=>{Rr(t,xk,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ob("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return uk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{uk.getField(e).each(Lm.disable)},onEnabled:e=>{uk.getField(e).each(Lm.enable)}}),Ox()])})})(e,t.shared.providers,o))),sizeinput:eE(((e,t)=>((e,t)=>{let o=G_;const n=ca("ratio-event"),s=e=>ob(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=U_.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled()}),Ox(),pk.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>uk.parts.field({factory:Hv,inputClasses:["tox-textfield"],inputBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled()}),Ox(),pk.config({}),th("size-input-events",[Wr(Xs(),((e,t)=>{Rr(e,n,{isField1:o})})),Wr(er(),((t,o)=>{Rr(t,xk,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ri(t.translate(e))]}),c=U_.parts.field1(a([uk.parts.label(l("Width")),i(!0)])),d=U_.parts.field2(a([uk.parts.label(l("Height")),i(!1)]));return U_.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{W_(vu.getValue(e)).each((e=>{o(e).each((e=>{vu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{U_.getField1(e).bind(uk.getField).each(Lm.disable),U_.getField2(e).bind(uk.getField).each(Lm.disable),U_.getLock(e).each(Lm.disable)},onEnabled:e=>{U_.getField1(e).bind(uk.getField).each(Lm.enable),U_.getField2(e).bind(uk.getField).each(Lm.enable),U_.getLock(e).each(Lm.enable)}}),Ox(),th("size-input-events2",[Wr(n,((e,t)=>{const n=t.event.isField1,s=n?U_.getField1(e):U_.getField2(e),r=n?U_.getField2(e):U_.getField1(e),a=s.map(vu.getValue).getOr(""),i=r.map(vu.getValue).getOr("");o=((e,t)=>{const o=W_(e).toOptional(),n=W_(t).toOptional();return Se(o,n,((e,t)=>j_(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>j_(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(G_))).getOr(G_)})(a,i)}))])])})})(e,t.shared.providers))),slider:eE(((e,t,o)=>((e,t,o)=>{const n=OO.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ri(t.translate(e.label))]}),s=OO.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=OO.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return OO.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:Tl([HO(),ah.config({})]),onChoose:(t,o,n)=>{Rr(t,xk,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:eE(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=vu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":JT,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=vu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=KT(a,(e=>L(e,(e=>jT(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,KT(a,$T(t)),KT(a,q([YT(t),qT(t),XT(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(UT,t)),[])):n;var s}))})(e.filetype,n,o),r=I_(s,bv.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return SS(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Lk)&&Lk.run(e).get(b)},typeaheadBehaviours:Tl([...o.getValidationHandler().map((t=>Lk.config({getRoot:e=>at(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{Ct(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=vu.getValue(o);return FT((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=rn.error(e.message);o(t)}else{const t=rn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Lm.config({disabled:()=>!e.enabled||s.isDisabled()}),pk.config({}),th("urlinput-events",[Wr(Qs(),(t=>{const o=Ka(t.element),n=o.trim();n!==o&&Ja(t.element,n),"file"===e.filetype&&Rr(t,xk,{name:e.name})})),Wr(er(),(t=>{Rr(t,xk,{name:e.name}),r(t)})),Wr(dr(),(t=>{Rr(t,xk,{name:e.name}),r(t)}))])]),eventOrder:{[Qs()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Rv(0,0,"normal")},onExecute:(e,t,o)=>{Rr(t,Ck,{})},onItemExecute:(t,o,n,s)=>{r(t),Rr(t,xk,{name:e.name})}},i=uk.parts.field({...a,factory:BT}),l=e.label.map((e=>yk(e,s))),c=Xh(((e,t,o=e,n=e)=>ob(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(JT),"warning")),d=Xh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=ca("browser.url.event"),g=Xh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:Tl([Lm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Xh(LT({name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Fr(e,m)),s,[],["tox-browse-url"]));return uk.sketch({dom:vk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:Tl([Lm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{uk.getField(e).each(Lm.disable),p.getOpt(e).each(Lm.disable)},onEnabled:e=>{uk.getField(e).each(Lm.enable),p.getOpt(e).each(Lm.enable)}}),Ox(),th("url-input-events",[Wr(m,(t=>{Om.getCurrent(t).each((o=>{const n=vu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{vu.setValue(o,n),Rr(t,xk,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:eE((e=>{const t=nc(),o=Xh({dom:{tag:e.tag}}),n=nc();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:Tl([th("custom-editor-events",[Jr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):YO.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),jO(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),HO()]),components:[o.asSpec()]}})),htmlpanel:eE((e=>"presentation"===e.presets?ik.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):ik.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:Tl([pk.config({}),ah.config({})])}))),imagepreview:eE(((e,t,o)=>((e,t)=>{const o=As(t.getOr({url:""})),n=Xh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Xh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:Tl([HO(),jO(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Zt(e),s=jt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Zt(e.element),jt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{It(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==_t(n,"src")&&(Ct(n,"src",t.url),Ga(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[rc(s,"load",o),rc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>H(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(Wa(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:eE(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:Tl([pk.config({}),ah.config({})])};var n,s})(e,t.shared.providers))),tree:eE(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=As(s),a=As(e.defaultSelectedId),i=ca("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?pT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):yT({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:Tl([Gp.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),th(xT,[Wr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Il.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),eh.set(e,l(A.some(t.value),r.get()))}}}}),eh.config({})])}})(e,t))),panel:eE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},oE={field:(e,t)=>t,record:x([])},nE=(e,t,o,n)=>{const s=bn(n,{shared:{interpreter:t=>sE(e,t,o,s)}});return sE(e,t,o,s)},sE=(e,t,o,n)=>be(tE,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),rE=(e,t,o)=>sE(oE,e,t,o),aE="layout-inset",iE=e=>e.x,lE=(e,t)=>e.x+e.width/2-t.width/2,cE=(e,t)=>e.x+e.width-t.width,dE=e=>e.y,uE=(e,t)=>e.y+e.height-t.height,mE=(e,t)=>e.y+e.height/2-t.height/2,gE=(e,t,o)=>Ui(cE(e,t),uE(e,t),o.insetSouthwest(),qi(),"southwest",el(e,{right:0,bottom:3}),aE),pE=(e,t,o)=>Ui(iE(e),uE(e,t),o.insetSoutheast(),$i(),"southeast",el(e,{left:1,bottom:3}),aE),hE=(e,t,o)=>Ui(cE(e,t),dE(e),o.insetNorthwest(),Gi(),"northwest",el(e,{right:0,top:2}),aE),fE=(e,t,o)=>Ui(iE(e),dE(e),o.insetNortheast(),ji(),"northeast",el(e,{left:1,top:2}),aE),bE=(e,t,o)=>Ui(lE(e,t),dE(e),o.insetNorth(),Yi(),"north",el(e,{top:2}),aE),vE=(e,t,o)=>Ui(lE(e,t),uE(e,t),o.insetSouth(),Xi(),"south",el(e,{bottom:3}),aE),yE=(e,t,o)=>Ui(cE(e,t),mE(e,t),o.insetEast(),Ji(),"east",el(e,{right:0}),aE),xE=(e,t,o)=>Ui(iE(e),mE(e,t),o.insetWest(),Ki(),"west",el(e,{left:1}),aE),wE=e=>{switch(e){case"north":return bE;case"northeast":return fE;case"northwest":return hE;case"south":return vE;case"southeast":return pE;case"southwest":return gE;case"east":return yE;case"west":return xE}},SE=(e,t,o,n,s)=>Zl(n).map(wE).getOr(bE)(e,t,o,n,s),kE=e=>{switch(e){case"north":return vE;case"northeast":return pE;case"northwest":return gE;case"south":return bE;case"southeast":return fE;case"southwest":return hE;case"east":return xE;case"west":return yE}},CE=(e,t,o,n,s)=>Zl(n).map(kE).getOr(bE)(e,t,o,n,s),OE={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},_E=(e,t,o)=>{const n={maxHeightFunction:gc()};return()=>o()?{type:"node",root:bt(ft(e())),node:A.from(e()),bubble:bc(12,12,OE),layouts:{onRtl:()=>[fE],onLtr:()=>[hE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:bc(-12,12,OE),layouts:{onRtl:()=>[ll,cl,gl],onLtr:()=>[cl,ll,gl]},overrides:n}},TE=(e,t,o,n)=>{const s={maxHeightFunction:gc()};return()=>n()?{type:"node",root:bt(ft(t())),node:A.from(t()),bubble:bc(12,12,OE),layouts:{onRtl:()=>[bE],onLtr:()=>[bE]},overrides:s}:e?{type:"node",root:bt(ft(t())),node:A.from(t()),bubble:bc(0,-Gt(t()),OE),layouts:{onRtl:()=>[ml],onLtr:()=>[ml]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:bc(0,0,OE),layouts:{onRtl:()=>[ml],onLtr:()=>[ml]},overrides:s}},EE=(e,t,o)=>()=>o()?{type:"node",root:bt(ft(e())),node:A.from(e()),layouts:{onRtl:()=>[bE],onLtr:()=>[bE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[gl],onLtr:()=>[gl]}},AE=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(jc.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),ME=e=>t=>({type:"node",root:e(),node:t}),DE=(e,t,o,n)=>{const s=iv(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:_E(a,t,i),inlineBottomDialog:TE(e.inline,a,o,i),banner:EE(a,t,i),cursor:AE(e,r),node:ME(r)}},BE=e=>(t,o)=>{oS(e)(t,o)},IE=e=>()=>Uw(e),FE=e=>t=>zw(e,t),RE=e=>t=>Pw(e,t),NE=e=>()=>Pb(e),VE=e=>ye(e,"items"),zE=e=>ye(e,"format"),LE=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],HE=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=HE(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),PE=e=>xb(e).map((t=>{const o=((e,t)=>{const o=HE(t),n=t=>{H(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return wb(e)?LE.concat(o):o})).getOr(LE),UE=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),WE=(e,t,o,n)=>{const s=t=>L(t,(t=>VE(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):zE(t)?(e=>UE(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:ca(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},jE=XO.trim,GE=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},$E=GE("true"),qE=GE("false"),YE=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),XE=e=>e.innerText||e.textContent,KE=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&ZE(e),JE=e=>e&&/^(H[1-6])$/.test(e.nodeName),ZE=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return $E(t)}return!1})(e)&&!qE(e),QE=e=>JE(e)&&ZE(e),eA=e=>{var t;const o=(e=>e.id?e.id:ca("h"))(e);return YE("header",null!==(t=XE(e))&&void 0!==t?t:"","#"+o,(e=>JE(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},tA=e=>{const t=e.id||e.name,o=XE(e);return YE("anchor",o||"#"+t,"#"+t,0,b)},oA=e=>jE(e.title).length>0,nA=e=>{const t=(e=>{const t=L(Zc(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>L(U(e,QE),eA))(t).concat((e=>L(U(e,KE),tA))(t)),oA)},sA="tinymce-url-history",rA=e=>r(e)&&/^https?/.test(e),aA=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,rA));var t})).isNone(),iA=()=>{const e=Ow.getItem(sA);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+sA+" was not valid JSON",e),{};throw e}return aA(t)?t:(console.log("Local storage "+sA+" was not valid format",t),{})},lA=e=>{const t=iA();return be(t,e).getOr([])},cA=(e,t)=>{if(!rA(e))return;const o=iA(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!aA(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Ow.setItem(sA,JSON.stringify(e))})(o)},dA=e=>!!e,uA=e=>ce(XO.makeMap(e,/[, ]/),dA),mA=e=>A.from(Fb(e)),gA=e=>A.from(e).filter(r).getOrUndefined(),pA=e=>({getHistory:lA,addToHistory:cA,getLinkInformation:()=>(e=>zb(e)?A.some({targets:nA(e.getBody()),anchorTop:gA(Lb(e)),anchorBottom:gA(Hb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Rb(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Vb(e)).filter(dA).map(uA);return mA(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?mA(e):A.none():o[t]?mA(e):A.none()})(e,t).map((o=>n=>wS((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),hA=pm,fA=Ju,bA=x([xs("shell",!1),ns("makeItem"),xs("setupItem",b),Su("listBehaviours",[eh])]),vA=Yu({name:"items",overrides:()=>({behaviours:Tl([eh.config({})])})}),yA=x([vA]),xA=wm({name:x("CustomList")(),configFields:bA(),partFields:yA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[eh.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:wu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):am(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=eh.contents(n),r=o.length,a=r-s.length,i=a>0?V(a,(()=>e.makeItem())):[],l=s.slice(r);H(l,(e=>eh.remove(n,e))),H(i,(e=>eh.append(n,e)));const c=eh.contents(n);H(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),wA=x([ns("dom"),xs("shell",!0),yu("toolbarBehaviours",[eh])]),SA=x([Yu({name:"groups",overrides:()=>({behaviours:Tl([eh.config({})])})})]),kA=wm({name:"Toolbar",configFields:wA(),partFields:SA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[eh.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:wu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):am(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{eh.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),CA=b,OA=T,_A=x([]);var TA,EA=Object.freeze({__proto__:null,setup:CA,isDocked:OA,getBehaviours:_A});const AA=e=>(xe(Vt(e,"position"),"fixed")?A.none():it(e)).orThunk((()=>{const t=Ne("span");return rt(e).bind((e=>{Lo(e,t);const o=it(t);return Uo(t),o}))})),MA=e=>AA(e).map(Xt).getOrThunk((()=>qt(0,0))),DA=(e,t)=>{const o=e.element;Wa(o,t.transitionClass),Ga(o,t.fadeOutClass),Wa(o,t.fadeInClass),t.onShow(e)},BA=(e,t)=>{const o=e.element;Wa(o,t.transitionClass),Ga(o,t.fadeInClass),Wa(o,t.fadeOutClass),t.onHide(e)},IA=(e,t)=>e.y>=t.y,FA=(e,t)=>e.bottom<=t.bottom,RA=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),NA=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),VA=e=>e.box.x-e.win.x,zA=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return qt(e.bounds.x,o)})(o,t);return{box:Jo(n.left,n.top,Zt(e),jt(e)),location:o.location}})),LA=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return qt(e.x,o)})(t,o),a=Jo(r.left,r.top,t.width,t.height);n.setInitialPos({style:zt(e),position:Rt(e,"position")||"static",bounds:a,location:s.location})},HA=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=AA(e).getOr(wt()),r=Zo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Pl("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),PA=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Pl("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Pl("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},UA=(e,t,o)=>{const n=e.element;return xe(Vt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>zA(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return FA(t,o.bounds);case"top":return IA(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>HA(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>zA(e,t,o))).bind((({box:e,location:o})=>{const n=tn(),s=VA({win:n,box:e}),r="top"===o?RA(n,s,t):NA(n,s,t);return PA(r)})))))(n,t,o):((e,t,o)=>{const n=Zo(e),s=tn(),r=((e,t,o)=>{const n=t.win,s=t.box,r=VA(t);return re(e,(e=>{switch(e){case"bottom":return FA(s,o.bounds)?A.none():A.some(NA(n,r,o));case"top":return IA(s,o.bounds)?A.none():A.some(RA(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(LA(e,n,t,o,r),PA(r)):A.none()})(n,t,o)},WA=(e,t,o)=>{o.setDocked(!1),H(["left","right","top","bottom","position"],(t=>Ht(e.element,t))),t.onUndocked(e)},jA=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Ul(e.element,n),(s?t.onDocked:t.onUndocked)(e)},GA=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(qa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?DA:BA)(e,t))}))}))},$A=(e,t,o,n,s)=>{GA(e,t,o,n,!0),jA(e,t,o,s.positionCss)},qA=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);GA(e,t,o,n),UA(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return WA(e,t,o);case"absolute":return jA(e,t,o,s.positionCss);case"fixed":$A(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},YA=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return zA(n,t,o).bind((({box:e})=>HA(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":WA(e,t,o);break;case"absolute":jA(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{Ya(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),qA(e,t,o)})(e,t,o)},XA=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Zo(e),r=tn(),a=n(r,VA({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>LA(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),PA(a)):A.none()})(t.element,s,n,e).each((e=>{$A(t,o,n,s,e)}))},KA=XA(RA),JA=XA(NA);var ZA=Object.freeze({__proto__:null,refresh:qA,reset:YA,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:KA,forceDockToBottom:JA}),QA=Object.freeze({__proto__:null,events:(e,t)=>Hr([Kr(nr(),((o,n)=>{e.contextual.each((e=>{$a(o.element,e.transitionClass)&&(Ya(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Wr(wr(),((o,n)=>{qA(o,e,t)})),Wr(Ar(),((o,n)=>{qA(o,e,t)})),Wr(Sr(),((o,n)=>{YA(o,e,t)}))])}),eM=[ys("contextual",[as("fadeInClass"),as("fadeOutClass"),as("transitionClass"),ls("lazyContext"),Ri("onShow"),Ri("onShown"),Ri("onHide"),Ri("onHidden")]),_s("lazyViewport",(()=>({bounds:tn(),optScrollEnv:A.none()}))),Ts("modes",["top","bottom"],Hn),Ri("onDocked"),Ri("onUndocked")];const tM=Al({fields:eM,name:"docking",active:QA,apis:ZA,state:Object.freeze({__proto__:null,init:e=>{const t=As(!1),o=As(!0),n=nc(),s=As(e.modes);return Ta({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),oM=x(ca("toolbar-height-change")),nM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},sM="tox-tinymce--toolbar-sticky-on",rM="tox-tinymce--toolbar-sticky-off",aM=(e,t)=>R(tM.getModes(e),t),iM=e=>{const t=e.element;at(t).each((o=>{const n="padding-"+tM.getModes(e)[0];if(tM.isDocked(e)){const e=Zt(o);Bt(t,"width",e+"px"),Bt(o,n,(e=>Gt(e)+(parseInt(Rt(e,"margin-top"),10)||0)+(parseInt(Rt(e,"margin-bottom"),10)||0))(t)+"px")}else Ht(t,"width"),Ht(o,n)}))},lM=(e,t)=>{t?(Ga(e,nM.fadeOutClass),qa(e,[nM.transitionClass,nM.fadeInClass])):(Ga(e,nM.fadeInClass),qa(e,[nM.fadeOutClass,nM.transitionClass]))},cM=(e,t)=>{const o=ze(e.getContainer());t?(Wa(o,sM),Ga(o,rM)):(Wa(o,rM),Ga(o,sM))},dM=(e,t)=>{const o=nc(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||iM(t),cM(e,tM.isDocked(t)),t.getSystem().broadcastOn([eu()],{}),n().each((e=>e.getSystem().broadcastOn([eu()],{})))},a=e.inline?[]:[Il.config({channels:{[oM()]:{onReceive:iM}}})];return[ah.config({}),tM.config({contextual:{lazyContext:t=>{const o=Gt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Zo(ze(n));return XS(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(aM(t,"top")?0:o);return Jo(s.x,n,s.width,e)}),(e=>{const n=en(s,KS(e)),r=aM(t,"top")?n.y:n.y+o;return Jo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>lM(e,!0)))},onShown:e=>{s((e=>Ya(e,[nM.transitionClass,nM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=tt(t);zl(o).filter((e=>!Qe(t,e))).filter((t=>Qe(t,ze(o.dom.body))||et(e,t))).each((()=>Rl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Ll(e).orThunk((()=>t().toOptional().bind((e=>Ll(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>lM(e,!1)))},onHidden:()=>{s((e=>Ya(e,[nM.transitionClass])))},...nM},lazyViewport:t=>XS(e,t.element).fold((()=>{const o=tn(),n=Db(e),s=o.y+(aM(t,"top")?n:0),r=o.height-(aM(t,"bottom")?n:0);return{bounds:Jo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:KS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var uM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(tM.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(iM)})),e.on("SkinLoaded",(()=>{o().each((e=>{tM.isDocked(e)?tM.reset(e):tM.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(tM.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{tM.refresh(t);const o=t.element;zg(o)&&((e,t)=>{const o=tt(t),n=st(t).dom.innerHeight,s=Wo(o),r=ze(e.elm),a=Qo(r),i=jt(r),l=a.y,c=l+i,d=Xt(t),u=jt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)jo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;jo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{cM(e,!1)}))},isDocked:e=>e().map(tM.isDocked).getOr(!1),getBehaviours:dM});const mM=Bn([sy,ss("items",Fn([Nn([ry,us("items",Hn)]),Hn]))].concat(Fy)),gM=[hs("text"),hs("tooltip"),hs("icon"),ws("search",!1,Fn([Pn,Bn([hs("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),ls("fetch"),_s("onSetup",(()=>b))],pM=Bn([sy,...gM]),hM=e=>Yn("menubutton",pM,e),fM=Bn([sy,yy,vy,by,Sy,uy,hy,Cs("presets","normal",["normal","color","listpreview"]),Ty(1),gy,py]);var bM=xm({factory:(e,t)=>{const o={focus:Gp.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=hM(o).mapError((e=>Jn(e))).getOrDie();return dT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));eh.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:Tl([eh.config({}),th("menubar-events",[Jr((t=>{e.onSetup(t)})),Wr(Ys(),((e,t)=>{vi(e.element,".tox-mbtn--active").each((o=>{yi(t.event.target,".tox-mbtn").each((t=>{Qe(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{NS.expand(e),NS.close(o),ah.focus(e)}))}))}))}))})),Wr(Tr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{NS.isOpen(o)&&(NS.expand(e),NS.close(o))}))}))}))]),Gp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),pk.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ns("dom"),ns("uid"),ns("onEscape"),ns("backstage"),xs("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const vM="container",yM=[yu("slotBehaviours",[])],xM=e=>"<alloy.field."+e+">",wM=(e,t)=>{const o=t=>dm(e),n=(t,o)=>(n,s)=>am(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==_t(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Bt(o,"display","none"),Ct(o,"aria-hidden","true"),Rr(e,Er(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{H(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Ht(o,"display"),At(o,"aria-hidden"),Rr(e,Er(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>am(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:xu(e.slotBehaviours),apis:c}},SM=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Oa(e))),kM={...SM,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),tm(vM,xM(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>$u({name:e,pname:xM(e)})));return fm(vM,yM,s,wM,o)}},CM=Bn([vy,yy,_s("onShow",b),_s("onHide",b),hy]),OM=e=>({element:()=>e.element.dom}),_M=(e,t)=>{const o=L(ae(t),(e=>{const o=t[e],n=Xn((e=>Yn("sidebar",CM,e))(o));return{name:e,getApi:OM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=As(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:ux([Mx(t,n),Dx(t,n),Wr(Er(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},TM=e=>kM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:_M(t,e),slotBehaviours:ux([Jr((e=>kM.hideAllSlots(e)))])}))),EM=(e,t)=>{Ct(e,"role",t)},AM=e=>Om.getCurrent(e).bind((e=>lT.isGrowing(e)||lT.hasGrown(e)?Om.getCurrent(e).bind((e=>G(kM.getSlotNames(e),(t=>kM.isShowing(e,t))))):A.none())),MM=ca("FixSizeEvent"),DM=ca("AutoSizeEvent");var BM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{Ct(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=Tl([Gp.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),ah.config({})]),a=n(s,r),i=s.getSystem().build(a);eh.append(s,di(i)),i.hasConfigured(Gp)&&t.focus&&Gp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>eh.remove(s,i)))},unblock:(e,t,o)=>{At(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),IM=[_s("getRoot",A.none),Os("focus",!0),Ri("onBlock"),Ri("onUnblock")];const FM=Al({fields:IM,name:"blocking",apis:BM,state:Object.freeze({__proto__:null,init:()=>{const e=tc((e=>e.destroy()));return Ta({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),RM=e=>Om.getCurrent(e).each((e=>Rl(e.element,!0))),NM=(e,t,o)=>{const n=As(!1),s=nc(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(ze).filter($e).exists((e=>$a(e,"mce-pastebin"))))&&(o.preventDefault(),RM(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Tt(e,o).each((t=>Ct(e,n,t))),Ct(e,o,-1)):(At(e,o),Tt(e,n).each((t=>{Ct(e,o,t),At(e,n)})))}))})(e,o),o)FM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Yh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Ht(s,"display"),At(s,"aria-hidden"),e.hasFocus()&&RM(t);else{const o=Om.getCurrent(t).exists((e=>Vl(e.element)));FM.unblock(t),Bt(s,"display","none"),Ct(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=$h.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},VM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),zM=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},LM=e=>L(e,(e=>e.element)),HM=(e,t)=>{const o=L(t,(e=>di(e)));kA.setGroups(e,o)},PM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=im(e,t,"primary"),r=fS.getCoupled(e,"overflowGroup");Bt(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Ll(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),HM(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=zM(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>zM(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=LM(e.concat(t));return VM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=LM(e).concat([o]);return VM(s,LM(t),n)})(r,a,n,i):((e,t,o)=>VM(LM(e),[],o))(r,0,i)})(Zt(s.element),t.builtGroups.get(),(e=>Zt(e.element)),r);0===l.extra.length?(eh.remove(s,r),o([])):(HM(s,l.within),o(l.extra)),Ht(s.element,"visibility"),Pt(s.element),i.each(ah.focus)},UM=x([yu("splitToolbarBehaviours",[fS]),ts("builtGroups",(()=>As([])))]),WM=x([Ii(["overflowToggledClass"]),bs("getOverflowBounds"),ns("lazySink"),ts("overflowGroups",(()=>As([]))),Ri("onOpened"),Ri("onClosed")].concat(UM())),jM=x([$u({factory:kA,schema:wA(),name:"primary"}),qu({schema:wA(),name:"overflow"}),qu({name:"overflow-button"}),qu({name:"overflow-group"})]),GM=x(((e,t)=>{((e,t)=>{const o=Jt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Bt(e,"max-width",o+"px")})(e,Math.floor(t))})),$M=x([Ii(["toggledClass"]),ns("lazySink"),ls("fetch"),bs("getBounds"),ys("fireDismissalEventInstead",[xs("event",Or())]),Oc(),Ri("onToggled")]),qM=x([qu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:Tl([ph.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),qu({factory:kA,schema:wA(),name:"toolbar",overrides:e=>({toolbarBehaviours:Tl([Gp.config({mode:"cyclic",onEscape:t=>(am(t,e,"button").each(ah.focus),A.none())})])})})]),YM=nc(),XM=(e,t)=>{const o=fS.getCoupled(e,"toolbarSandbox");Zd.isOpen(o)?Zd.close(o):Zd.open(o,t.toolbar())},KM=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();_d.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:GM()}}},s)},JM=(e,t,o,n,s)=>{kA.setGroups(t,s),KM(e,t,o,n),ph.on(e)},ZM=wm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...qh.sketch({...n.button(),action:e=>{XM(e,n)},buttonBehaviours:ku({dump:n.button().buttonBehaviours},[fS.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=wi();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:Tl([Gp.config({mode:"special",onEscape:e=>(Zd.close(e),A.some(!0))}),Zd.config({onOpen:(s,r)=>{const a=YM.get().getOr(!1);o.fetch().get((s=>{JM(e,r,o,t.layouts,s),n.link(e.element),a||Gp.focusIn(r)}))},onClose:()=>{ph.off(e),YM.get().getOr(!1)||ah.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Si(o,n)||Si(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Il.config({channels:{...nu({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...ru({doReposition:()=>{Zd.getState(fS.getCoupled(e,"toolbarSandbox")).each((n=>{KM(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Zd.getState(fS.getCoupled(t,"toolbarSandbox")).each((s=>{JM(t,s,e,o.layouts,n)}))},reposition:t=>{Zd.getState(fS.getCoupled(t,"toolbarSandbox")).each((n=>{KM(t,n,e,o.layouts)}))},toggle:e=>{XM(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{YM.set(!0),XM(e,t),YM.clear()})(e,n)},getToolbar:e=>Zd.getState(fS.getCoupled(e,"toolbarSandbox")),isOpen:e=>Zd.isOpen(fS.getCoupled(e,"toolbarSandbox"))}}),configFields:$M(),partFields:qM(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),QM=x([ns("items"),Ii(["itemSelector"]),yu("tgroupBehaviours",[Gp])]),eD=x([Xu({name:"items",unit:"item"})]),tD=wm({name:"ToolbarGroup",configFields:QM(),partFields:eD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.tgroupBehaviours,[Gp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),oD=e=>L(e,(e=>di(e))),nD=(e,t,o)=>{PM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{ZM.setGroups(e,oD(n))}))}))},sD=wm({name:"SplitFloatingToolbar",configFields:WM(),partFields:jM(),factory:(e,t,o,n)=>{const s=Xh(ZM.sketch({fetch:()=>wS((t=>{t(oD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[cl,ll],onRtl:()=>[ll,cl],onBottomLtr:()=>[ul,dl],onBottomRtl:()=>[dl,ul]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.splitToolbarBehaviours,[fS.config({others:{overflowGroup:()=>tD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),nD(t,s,e)},refresh:t=>nD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{ZM.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(ZM.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(ZM.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{ZM.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(ZM.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),rD=x([Ii(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ri("onOpened"),Ri("onClosed")].concat(UM())),aD=x([$u({factory:kA,schema:wA(),name:"primary"}),$u({factory:kA,schema:wA(),name:"overflow",overrides:e=>({toolbarBehaviours:Tl([lT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{am(t,e,"overflow-button").each((e=>{ph.off(e),ah.focus(e)})),e.onClosed(t)},onGrown:t=>{Gp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{am(t,e,"overflow-button").each(ph.on)}}),Gp.config({mode:"acyclic",onEscape:t=>(am(t,e,"overflow-button").each(ah.focus),A.some(!0))})])})}),qu({name:"overflow-button",overrides:e=>({buttonBehaviours:Tl([ph.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),qu({name:"overflow-group"})]),iD=(e,t)=>{am(e,t,"overflow-button").bind((()=>am(e,t,"overflow"))).each((o=>{lD(e,t),lT.toggleGrow(o)}))},lD=(e,t)=>{am(e,t,"overflow").each((o=>{PM(e,t,(e=>{const t=L(e,(e=>di(e)));kA.setGroups(o,t)})),am(e,t,"overflow-button").each((e=>{lT.hasGrown(o)&&ph.on(e)})),lT.refresh(o)}))},cD=wm({name:"SplitSlidingToolbar",configFields:rD(),partFields:aD(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.splitToolbarBehaviours,[fS.config({others:{overflowGroup:e=>tD.sketch({...n["overflow-group"](),items:[qh.sketch({...n["overflow-button"](),action:t=>{Fr(e,s)}})]})}}),th("toolbar-toggle-events",[Wr(s,(t=>{iD(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),lD(t,e)},refresh:t=>lD(t,e),toggle:t=>iD(t,e),isOpen:t=>((e,t)=>am(e,t,"overflow").map(lT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),dD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[tD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:Tl([pk.config({}),ah.config({})])}},uD=e=>tD.sketch(dD(e)),mD=(e,t)=>{const o=Jr((t=>{const o=L(e.initGroups,uD);kA.setGroups(t,o)}));return Tl([Ex(e.providers.isDisabled),Ox(),Gp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),th("toolbar-events",[o])])},gD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":dD({title:A.none(),items:[]}),"overflow-button":NT({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers)},splitToolbarBehaviours:mD(e,t)}},pD=e=>{const t=gD(e),o=sD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return sD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Qo(t),n=nt(t),s=Qo(n),r=Math.max(n.dom.scrollHeight,s.height);return Jo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},hD=e=>{const t=cD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=cD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=gD(e);return cD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([oM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([oM()],{type:"closed"}),e.onToggled(t,!1)}})},fD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return kA.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===rb.scrolling?["tox-toolbar--scrolling"]:[])},components:[kA.parts.groups({})],toolbarBehaviours:mD(e,t)})},bD=[by,vy,hs("tooltip"),Cs("buttonType","secondary",["primary","secondary"]),Os("borderless",!1),ls("onAction")],vD={button:[...bD,iy,is("type",["button"])],togglebutton:[...bD,Os("active",!1),is("type",["togglebutton"])]},yD=[is("type",["group"]),Ts("buttons",[],Zn("type",vD))],xD=Zn("type",{...vD,group:yD}),wD=Bn([Ts("buttons",[],xD),ls("onShow"),ls("onHide")]),SD=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>O_(e,t.icons))).map(Xh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=VT(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ri),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Fx([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=RT(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{eh.set(o,[O_(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(Wa(t,"tox-button--enabled"),Ct(t,"aria-pressed",!0)):(Ga(t,"tox-button--enabled"),At(t,"aria-pressed"))},isActive:()=>$a(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,t);return qh.sketch(h)})(e,t),kD=Bo().deviceType,CD=kD.isPhone(),OD=kD.isTablet();var _D=wm({name:"silver.View",configFields:[ns("viewConfig")],partFields:[Yu({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>SD(e,t)))}))(o,e.providers)):SD(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...CD||OD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:Tl([ah.config({}),Gp.config({mode:"flow",selector:"button, .tox-button",focusInside:vg.OnEnterOrSpaceMode})]),components:t?o:[ik.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),ik.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[ns("buttons"),ns("providers")],name:"header"}),Yu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>hA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const TD=(e,t,o)=>pe(t,((t,n)=>{const s=Xn(Yn("view",wD,t));return e.slot(n,_D.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[_D.parts.header({buttons:s.buttons,providers:o})]:[],_D.parts.pane({})]}))})),ED=(e,t)=>kM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:TD(o,e,t),slotBehaviours:ux([Jr((e=>kM.hideAllSlots(e)))])}))),AD=e=>G(kM.getSlotNames(e),(t=>kM.isShowing(e,t))),MD=(e,t,o)=>{kM.getSlot(e,t).each((e=>{_D.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var DD=xm({factory:(e,t)=>{const o={setViews:(e,o)=>{eh.set(e,[ED(o,t.backstage.shared.providers)])},whichView:e=>Om.getCurrent(e).bind(AD),toggleView:(e,t,o,n)=>Om.getCurrent(e).exists((s=>{const r=AD(s),a=r.exists((e=>n===e)),i=kM.getSlot(s,n).isSome();return i&&(kM.hideAllSlots(s),a?((e=>{const t=e.element;Bt(t,"display","none"),Ct(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Ht(t,"display"),At(t,"aria-hidden")})(e),kM.showSlot(s,n),((e,t)=>{MD(e,t,_D.getOnShow)})(s,n)),r.each((e=>((e,t)=>MD(e,t,_D.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:Tl([eh.config({}),Om.config({find:e=>{const t=eh.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[ns("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const BD=fA.optional({factory:bM,name:"menubar",schema:[ns("backstage")]}),ID=fA.optional({factory:{sketch:e=>xA.sketch({uid:e.uid,dom:e.dom,listBehaviours:Tl([Gp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>fD({type:e.type,uid:ca("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{kA.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[ns("dom"),ns("onEscape")]}),FD=fA.optional({factory:{sketch:e=>{const t=(e=>e.type===rb.sliding?hD:e.type===rb.floating?pD:fD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[ns("dom"),ns("onEscape"),ns("getSink")]}),RD=fA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?dM:_A;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:Tl(o(t,e.sharedBackstage))}}},name:"header",schema:[ns("dom")]}),ND=fA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[ns("dom")]}),VD=fA.optional({name:"socket",schema:[ns("dom")]}),zD=fA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:Tl([pk.config({}),ah.config({}),lT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{Om.getCurrent(e).each(kM.hideAllSlots),Fr(e,DM)},onGrown:e=>{Fr(e,DM)},onStartGrow:e=>{Rr(e,MM,{width:Vt(e.element,"width").getOr("")})},onStartShrink:e=>{Rr(e,MM,{width:Zt(e.element)+"px"})}}),eh.config({}),Om.config({find:e=>{const t=eh.contents(e);return oe(t)}})])}],behaviours:Tl([PO(0),th("sidebar-sliding-events",[Wr(MM,((e,t)=>{Bt(e.element,"width",t.event.width)})),Wr(DM,((e,t)=>{Ht(e.element,"width")}))])])})},name:"sidebar",schema:[ns("dom")]}),LD=fA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:Tl([eh.config({}),FM.config({focus:!1}),Om.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[ns("dom")]}),HD=fA.optional({factory:DD,name:"viewWrapper",schema:[ns("backstage")]}),PD=fA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var UD=wm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s={getSocket:t=>hA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{hA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{Om.getCurrent(e).each((n=>{eh.set(n,[TM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&Om.getCurrent(n).each((t=>{kM.showSlot(t,s),lT.immediateGrow(n),Ht(n.element,"width"),EM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{hA.getPart(t,e,"sidebar").each((e=>((e,t)=>{Om.getCurrent(e).each((o=>{Om.getCurrent(o).each((n=>{lT.hasGrown(o)?kM.isShowing(n,t)?(lT.shrink(o),EM(e.element,"presentation")):(kM.hideAllSlots(n),kM.showSlot(n,t),EM(e.element,"region")):(kM.hideAllSlots(n),kM.showSlot(n,t),lT.grow(o),EM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>hA.getPart(t,e,"sidebar").bind(AM).getOrNull(),getHeader:t=>hA.getPart(t,e,"header"),getToolbar:t=>hA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{hA.getPart(t,e,"toolbar").each((e=>{const t=L(o,uD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{hA.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,uD)));xA.setItems(e,t)}))},refreshToolbar:t=>{hA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{hA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{hA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>hA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>hA.getPart(t,e,"throbber"),focusToolbar:t=>{hA.getPart(t,e,"toolbar").orThunk((()=>hA.getPart(t,e,"multiple-toolbar"))).each((e=>{Gp.focusIn(e)}))},setMenubar:(t,o)=>{hA.getPart(t,e,"menubar").each((e=>{bM.setMenus(e,o)}))},focusMenubar:t=>{hA.getPart(t,e,"menubar").each((e=>{bM.focus(e)}))},setViews:(t,o)=>{hA.getPart(t,e,"viewWrapper").each((e=>{DD.setViews(e,o)}))},toggleView:(t,o)=>hA.getPart(t,e,"viewWrapper").exists((e=>DD.toggleView(e,(()=>s.showMainView(t)),(()=>s.hideMainView(t)),o))),whichView:t=>hA.getPart(t,e,"viewWrapper").bind(DD.whichView).getOrNull(),hideMainView:t=>{n=s.isToolbarDrawerToggled(t),n&&s.toggleToolbarDrawer(t),hA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Bt(t,"display","none"),Ct(t,"aria-hidden","true")}))},showMainView:t=>{n&&s.toggleToolbarDrawer(t),hA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Ht(t,"display"),At(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:s,behaviours:e.behaviours}},configFields:[ns("dom"),ns("behaviours")],partFields:[RD,BD,FD,ID,VD,zD,ND,LD,HD,PD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const WD={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},jD=e=>e.split(" "),GD=(e,t)=>{const o={...WD,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?jD("file edit view insert format tools table help"):jD(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(WD,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Cb(o).split(/[ ,]/);return{text:e.title,getItems:()=>Y(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:jD(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},$D=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),qD=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),YD=async(e,t)=>{const o="ui/"+ev(e).getOr("default")+"/skin.css",n=tinymce.Resource.get(o);return r(n)?Promise.resolve(qD(e,o,n,e.ui.styleSheetLoader)):$D(e,t+"/skin.min.css",e.ui.styleSheetLoader)},XD=async(e,t)=>{var o;if(o=ze(e.getElement()),vt(o).isSome()){const o="ui/"+ev(e).getOr("default")+"/skin.shadowdom.css",n=tinymce.Resource.get(o);return r(n)?(qD(e,o,n,ib.DOM.styleSheetLoader),Promise.resolve()):$D(e,t+"/skin.shadowdom.min.css",ib.DOM.styleSheetLoader)}},KD=(e,t)=>(async(e,t)=>{ev(t).fold((()=>{const o=Qb(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}),(o=>{const n="ui/"+o+(e?"/content.inline":"/content")+".css",s=tinymce.Resource.get(n);if(r(s))qD(t,n,s,t.ui.styleSheetLoader);else{const o=Qb(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}}));const o=Qb(t);if(!Jb(t)&&r(o))return Promise.all([YD(t,o),XD(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),JD=k(KD,!1),ZD=k(KD,!0),QD=(e,t,o)=>e.translate([t,e.translate(o)]),eB=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=Y(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>Y(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return Y(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(I_(a,bv.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},tB=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>L(n.data,(e=>UE(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:eB(t,o),getStyleItems:s}},oB=(e,t,o,n,s)=>{const{items:r,getStyleItems:a}=tB(0,t,o);return A_({text:o.icon.isSome()?A.none():o.text,icon:o.icon,tooltip:A.from(o.tooltip),role:A.none(),fetch:r.getFetch(t,a),onSetup:t=>{const r=o=>t.setTooltip(QD(e,n,o.value));return e.on(s,r),yw(Sw(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Lm.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:x(e),setTooltip:o=>{const n=t.shared.providers.translate(o);Ot(e.element,{"aria-label":n,title:n})}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var nB;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(nB||(nB={}));const sB=(e,t,o)=>{const n=(s=((e,t)=>t===nB.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},rB="Alignment {0}",aB="left",iB=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],lB=e=>{const t={type:"basic",data:iB};return{tooltip:QD(e,rB,aB),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(iB,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(iB,(t=>e.formatter.match(t.format))).fold(x(aB),(e=>e.title.toLowerCase()));Rr(t,E_,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},cB=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Ce(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},dB="Block {0}",uB="Paragraph",mB=e=>{const t=sB(e,"block_formats",nB.SemiColon);return{tooltip:QD(e,dB,uB),text:A.some(uB),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:kw(e),updateText:o=>{const n=cB(e,(()=>t.data)).fold(x(uB),(e=>e.title));Rr(o,T_,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},gB="Font {0}",pB="System Font",hB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],fB=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},bB=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),vB=e=>{const t=()=>{const t=e=>e?fB(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=Kb(e),i=G(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>Ce(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=fB(e.toLowerCase());return bB(o,hB)||bB(o,t)}return!1})(r,a),{title:pB,format:r})));return{matchOpt:i,font:n}},o=sB(e,"font_family_formats",nB.SemiColon);return{tooltip:QD(e,gB,pB),text:A.some(pB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(x(s),(e=>e.title));Rr(o,T_,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},yB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},xB=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),wB=(e,t)=>A.from(xB.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(yB[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),SB={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},kB="Font size {0}",CB="12pt",OB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},_B={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},TB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(_B,e).getOr(e),EB=e=>be(OB,e).getOr(""),AB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=TB(s,e),r=EB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=sB(e,"font_size_formats",nB.Space);return{tooltip:QD(e,kB,CB),text:A.some(CB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(x(s),(e=>e.title));Rr(o,T_,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},MB="Format {0}",DB=(e,t)=>{const o="Paragraph";return{tooltip:QD(e,MB,o),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:kw(e),updateText:t=>{const n=e=>VE(e)?Y(e.items,n):zE(e)?[{title:e.title,format:e.format}]:[],s=Y(PE(e),n),r=cB(e,x(s)).fold(x(o),(e=>e.title));Rr(t,T_,{text:r}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r})},shouldHide:Sb(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},BB=x([ns("toggleClass"),ns("fetch"),Vi("onExecute"),xs("getHotspot",A.some),xs("getAnchorOverrides",x({})),Oc(),Vi("onItemExecute"),ms("lazySink"),ns("dom"),Ri("onOpen"),yu("splitDropdownBehaviours",[fS,Gp,ah]),xs("matchWidth",!1),xs("useMinWidth",!1),xs("eventOrder",{}),ms("role")].concat(IS())),IB=$u({factory:qh,schema:[ns("dom")],name:"arrow",defaults:()=>({buttonBehaviours:Tl([ah.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Nr)},buttonBehaviours:Tl([ph.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),FB=$u({factory:qh,schema:[ns("dom")],name:"button",defaults:()=>({buttonBehaviours:Tl([ah.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),RB=x([IB,FB,Yu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ns("text")],name:"aria-descriptor"}),qu({schema:[Bi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),CS()]),NB=wm({name:"SplitDropdown",configFields:BB(),partFields:RB(),factory:(e,t,o,n)=>{const s=e=>{Om.getCurrent(e).each((e=>{Xm.highlightFirst(e),Gp.focusIn(e)}))},r=t=>{ES(e,w,t,n,s,Uh.HighlightMenuAndItem).get(b)},a=t=>{const o=im(t,e,"button");return Nr(o),A.some(!0)},i={...Hr([Jr(((t,o)=>{am(t,e,"aria-descriptor").each((e=>{const o=ca("aria");Ct(e.element,"id",o),Ct(t.element,"aria-describedby",o)}))}))]),...fh(A.some(r))},l={repositionMenus:e=>{ph.isOn(e)&&BS(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[mr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:wu(e.splitDropdownBehaviours,[fS.config({others:{sandbox:t=>{const o=im(t,e,"arrow");return DS(e,t,{onOpen:()=>{ph.on(o),ph.on(t)},onClose:()=>{ph.off(o),ph.off(t)}})}}}),Gp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),ah.config({}),ph.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),VB=e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t),setText:t=>Rr(e,T_,{text:t}),setIcon:t=>Rr(e,E_,{icon:t})}),zB=e=>({setActive:t=>{ph.set(e,t)},isActive:()=>ph.isOn(e),isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t),setText:t=>Rr(e,T_,{text:t}),setIcon:t=>Rr(e,E_,{icon:t})}),LB=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),HB=ca("focus-button"),PB=(e,t,o,n,s)=>{const r=t.map((e=>Xh(__(e,"tox-tbtn",s)))),a=e.map((e=>Xh(O_(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:LB(o,s)},components:Fx([a.map((e=>e.asSpec())),r.map((e=>e.asSpec()))]),eventOrder:{[js()]:["focusing","alloy.base.behaviour",x_],[kr()]:[x_,"toolbar-group-button-events"]},buttonBehaviours:Tl([Ex(s.isDisabled),Ox(),th(x_,[Jr(((e,t)=>S_(e))),Wr(T_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{eh.set(e,[ri(s.translate(t.event.text))])}))})),Wr(E_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{eh.set(e,[O_(t.event.icon,s.icons)])}))})),Wr(js(),((e,t)=>{t.event.prevent(),Fr(e,HB)}))])].concat(n.getOr([])))}},UB=(e,t,o)=>{var n;const s=As(b),r=PB(e.icon,e.text,e.tooltip,A.none(),o);return qh.sketch({dom:r.dom,components:r.components,eventOrder:w_,buttonBehaviours:{...Tl([th("toolbar-button-events",[(a={onAction:e.onAction,getApi:t.getApi},ea(((e,t)=>{Ax(a,e)((t=>{Rr(e,y_,{buttonApi:t}),a.onAction(t)}))}))),Mx(t,s),Dx(t,s)]),Ex((()=>!e.enabled||o.isDisabled())),Ox()].concat(t.toolbarButtonBehaviours)),[x_]:null===(n=r.buttonBehaviours)||void 0===n?void 0:n[x_]}});var a},WB=(e,t,o)=>UB(e,{toolbarButtonBehaviours:o.length>0?[th("toolbarButtonWith",o)]:[],getApi:VB,onSetup:e.onSetup},t),jB=(e,t,o)=>UB(e,{toolbarButtonBehaviours:[eh.config({}),ph.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[th("toolbarToggleButtonWith",o)]:[]),getApi:zB,onSetup:e.onSetup},t),GB=(e,t,o)=>n=>wS((e=>t.fetch(e))).map((s=>A.from(jS(bn(nS(ca("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,bv.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:rS(t.columns,t.presets),menuBehaviours:ux("auto"!==t.columns?[]:[Jr(((e,o)=>{dx(e,4,Av(t.presets)).each((({numRows:t,numColumns:o})=>{Gp.setGridSize(e,t,o)}))}))])}))))),$B=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],qB=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Jn(e))).getOrDie();return t(r,n,s)},YB={button:qB(Ny,((e,t)=>{return o=e,n=t.shared.providers,WB(o,n,[]);var o,n})),togglebutton:qB(Ly,((e,t)=>{return o=e,n=t.shared.providers,jB(o,n,[]);var o,n})),menubutton:qB(hM,((e,t)=>dT(e,"tox-tbtn",t,A.none(),!1))),splitbutton:qB((e=>Yn("SplitButton",fM,e)),((e,t)=>((e,t)=>{const o=e=>({isEnabled:()=>!Lm.isDisabled(e),setEnabled:t=>Lm.set(e,!t),setIconFill:(t,o)=>{vi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ct(e,"fill",o)}))},setActive:t=>{Ct(e.element,"aria-pressed",t),vi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>ph.set(e,t)))}))},isActive:()=>vi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(ph.isOn))),setText:t=>vi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Rr(e,T_,{text:t}))))),setIcon:t=>vi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Rr(e,E_,{icon:t}))))),setTooltip:o=>{const n=t.providers.translate(o);Ot(e.element,{"aria-label":n,title:n})}}),n=As(b),s={getApi:o,onSetup:e.onSetup};return NB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...LB(e.tooltip,t.providers)}},onExecute:t=>{const n=o(t);n.isEnabled()&&e.onAction(n)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:Tl([Tx(t.providers.isDisabled),Ox(),th("split-dropdown-events",[Jr(((e,t)=>S_(e))),Wr(HB,ah.focus),Mx(s,n),Dx(s,n)]),Hk.config({})]),eventOrder:{[kr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:GB(o,e,t.providers),parts:{menu:Rv(0,e.columns,e.presets)},components:[NB.parts.button(PB(e.icon,e.text,A.none(),A.some([ph.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),NB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Qf("chevron-down",t.providers.icons)},buttonBehaviours:Tl([Tx(t.providers.isDisabled),Ox(),eb()])}),NB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:qB((e=>Yn("GroupToolbarButton",mM,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[kc]:t.shared.header.isPositionedAtTop()?Sc.TopToBottom:Sc.BottomToTop};if(Ob(o)===rb.floating)return((e,t,o,n)=>{const s=t.shared,r=As(b),a={toolbarButtonBehaviours:[],getApi:VB,onSetup:e.onSetup},i=[th("toolbar-group-button-events",[Mx(a,r),Dx(a,r)])];return ZM.sketch({lazySink:s.getSink,fetch:()=>wS((t=>{t(L(o(e.items),uD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:PB(e.icon,e.text,e.tooltip,A.some(i),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>KB(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,A.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},XB={styles:(e,t)=>{const o={type:"advanced",...t.styles};return oB(e,t,DB(e,o),MB,"StylesTextUpdate")},fontsize:(e,t)=>oB(e,t,AB(e),kB,"FontSizeTextUpdate"),fontsizeinput:(e,t)=>((e,t,o)=>{let n=A.none();const s=Sw(e,"NodeChange SwitchMode",(t=>{const s=t.getComponent();n=A.some(s),o.updateInputValue(s),Lm.set(s,!e.selection.isEditable())})),r=e=>({getComponent:x(e)}),a=As(b),i=ca("custom-number-input-events"),l=(e,t,s)=>{const r=n.map((e=>vu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=n.map((e=>e.element.dom.selectionStart-i)),c=n.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,s),n.each((e=>{vu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},c=(e,t)=>l(((e,t)=>e-t),e,t),d=(e,t)=>l(((e,t)=>e+t),e,t),u=e=>at(e.element).fold(A.none,(e=>(Rl(e),A.some(!0)))),m=e=>Vl(e.element)?(dt(e.element).each((e=>Rl(e))),A.some(!0)):A.none(),g=(o,n,s,a)=>{const i=As(b),l=t.shared.providers.translate(s),c=ca("altExecuting"),d=Sw(e,"NodeChange SwitchMode",(t=>{Lm.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Lm.isDisabled(e)||o(!0)};return qh.sketch({dom:{tag:"button",attributes:{title:l,"aria-label":l},classes:a.concat(n)},components:[C_(n,t.shared.providers.icons)],buttonBehaviours:Tl([Lm.config({}),th(c,[Mx({onSetup:d,getApi:r},i),Dx({getApi:r},i),Wr(Js(),((e,t)=>{t.event.raw.keyCode!==SB.space()&&t.event.raw.keyCode!==SB.enter()||Lm.isDisabled(e)||o(!1)})),Wr(tr(),u),Wr(Us(),u)])]),eventOrder:{[Js()]:[c,"keying"],[tr()]:[c,"alloy.base.behaviour"],[Us()]:[c,"alloy.base.behaviour"]}})},p=Xh(g((e=>c(!1,e)),"minus","Decrease font size",[])),h=Xh(g((e=>d(!1,e)),"plus","Increase font size",[])),f=Xh({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Hv.sketch({inputBehaviours:Tl([Lm.config({}),th(i,[Mx({onSetup:s,getApi:r},a),Dx({getApi:r},a)]),th("input-update-display-text",[Wr(T_,((e,t)=>{vu.setValue(e,t.event.text)})),Wr(Ks(),(e=>{o.onAction(vu.getValue(e))})),Wr(er(),(e=>{o.onAction(vu.getValue(e))}))]),Gp.config({mode:"special",onEnter:e=>(l(w,!0,!0),A.some(!0)),onEscape:u,onUp:e=>(d(!0,!1),A.some(!0)),onDown:e=>(c(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:Tl([ah.config({}),Gp.config({mode:"special",onEnter:m,onSpace:m,onEscape:u}),th("input-wrapper-events",[Wr(Ys(),(e=>{H([p,h],(t=>{const o=ze(t.get(e).element.dom);Vl(o)&&Nl(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"]},components:[p.asSpec(),f.asSpec(),h.asSpec()],behaviours:Tl([ah.config({}),Gp.config({mode:"flow",focusInside:vg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Vl(e.element)?A.none():(Rl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Rr(e,T_,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{wB(o,["unsupportedLength","empty"]);const s=t(),r=wB(o,["unsupportedLength","empty"]).or(wB(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=Nb(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e)),fontfamily:(e,t)=>oB(e,t,vB(e),gB,"FontFamilyTextUpdate"),blocks:(e,t)=>oB(e,t,mB(e),dB,"BlocksTextUpdate"),align:(e,t)=>oB(e,t,lB(e),rB,"AlignTextUpdate")},KB=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L($B,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(XB,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=Y(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(XB,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(YB,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(n=>A.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},JB=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return KB(e,s,n,A.none())}));UD.setToolbars(s,t)}else UD.setToolbar(s,KB(e,o,n,A.none()))},ZB=Bo(),QB=ZB.os.isiOS()&&ZB.os.version.major<=12;var eI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=As(0),l=r.outerContainer;JD(e);const d=ze(s.targetNode),u=bt(ft(d));Ld(d,r.mothership),((e,t,o)=>{uv(e)&&Ld(o.mainUi.mothership.element,o.popupUi.mothership),zd(t,o.dialogUi.mothership)})(e,u,t),e.on("SkinLoaded",(()=>{UD.setSidebar(l,o.sidebar,qb(e)),JB(e,t,o,n),i.set(e.getWin().innerWidth),UD.setMenubar(l,GD(e,o)),UD.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=As(qt(s.innerWidth,s.innerHeight)),i=As(qt(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(qt(s.innerWidth,s.innerHeight)),fw(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(qt(t.offsetWidth,t.offsetHeight)),fw(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=ac(ze(e.getBody()),"load",c);e.on("hide",(()=>{H(o,(e=>{Bt(e.element,"display","none")}))})),e.on("show",(()=>{H(o,(e=>{Ht(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=UD.getSocket(l).getOrDie("Could not find expected socket element");if(QB){It(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=rc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}Cx(e,t),e.addCommand("ToggleSidebar",((t,o)=>{UD.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=UD.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(UD.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Qd()],{target:t}),H(a,(e=>{e.broadcastOn([Qd()],{target:t})})),c(UD.whichView(l))&&(e.focus(),e.nodeChanged(),UD.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=UD.whichView(l))&&void 0!==e?e:""}));const g=Ob(e);g!==rb.sliding&&g!==rb.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(UD.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{kx(t,!e)},isEnabled:()=>!Lm.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const tI=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),oI=e=>h(e)?e+"px":e,nI=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},sI=e=>{const t=hb(e),o=fb(e),n=vb(e);return tI(t).map((e=>nI(e,o,n)))},{ToolbarLocation:rI,ToolbarMode:aI}=gv,iI=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=ib.DOM,l=iv(e),c=dv(e),d=vb(e).or(sI(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Ob(e),p=g===aI.sliding||g===aI.floating,h=As(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?jt(e.components()[1].element):0)):0,v=()=>{H(a,(e=>{e.broadcastOn([eu()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>{const e=tI(Rt(wt(),"margin-left")).getOr(0);return Zt(wt())-Xt(t).left+e}));Bt(e.element,"max-width",o+"px")}));const n=l?A.none():(()=>{if(l)return A.none();if(Xt(r.outerContainer.element).left+Qt(r.outerContainer.element)>=window.innerWidth-40||Vt(r.outerContainer.element,"width").isSome()){Bt(r.outerContainer.element,"position","absolute"),Bt(r.outerContainer.element,"left","0px"),Ht(r.outerContainer.element,"width");const e=Qt(r.outerContainer.element);return A.some(e)}return A.none()})();p&&UD.refreshToolbar(r.outerContainer),l||(o=>{s.on((n=>{const s=UD.getToolbar(r.outerContainer),a=b(s),i=Zo(t),{top:l,left:c}=((e,t)=>uv(e)?AA(t):A.none())(e,r.outerContainer.element).fold((()=>({top:m()?Math.max(i.y-jt(n.element)+a,0):i.bottom,left:i.x})),(e=>{var t;const o=Zo(e),s=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Qe(e,wt()),l=r?Math.max(i.y-jt(n.element)+a,0):i.y-o.y+s-jt(n.element)+a;return{top:m()?l:i.bottom,left:r?i.x:i.x-o.x}})),d={position:"absolute",left:Math.round(c)+"px",top:Math.round(l)+"px"},u=o.map((e=>{const t=Wo(),o=window.innerWidth-(c-t.left);return{width:Math.max(Math.min(e,o),150)+"px"}})).getOr({});It(r.outerContainer.element,{...d,...u})}))})(n),c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(Tb(e)){case rI.auto:const e=UD.getToolbar(r.outerContainer),n=b(e),s=jt(o.element)-n,a=Zo(t);if(a.y>s)return"top";{const e=nt(t),o=Math.max(e.dom.scrollHeight,jt(e));return a.bottom<o-s||tn().bottom<a.bottom-s?"bottom":"top"}case rI.bottom:return"bottom";case rI.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{tM.setModes(e,[i]),u.setDockingMode(i);const t=m()?Sc.TopToBottom:Sc.BottomToTop;Ct(e.element,kc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Bt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{Ht(e.element,"display")})),w(),uv(e)?y((e=>tM.isDocked(e)?tM.reset(e):tM.refresh(e))):y(tM.refresh)},hide:()=>{h.set(!1),Bt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{Bt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(tM.reset)},repositionPopups:v}},lI=(e,t)=>{const o=Zo(e);return{pos:t?o.y:o.bottom,bounds:o}};var cI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=nc(),i=ze(s.targetNode),l=iI(e,i,t,n,a),c=Mb(e);ZD(e);const d=()=>{if(a.isSet())return void l.show();a.set(UD.getHeader(r.outerContainer).getOrDie());const s=lv(e);uv(e)?(Ld(i,r.mothership),Ld(i,t.popupUi.mothership)):zd(s,r.mothership),zd(s,t.dialogUi.mothership),JB(e,t,o,n),UD.setMenubar(r.outerContainer,GD(e,o)),l.show(),((e,t,o,n)=>{const s=As(lI(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=lI(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&fw(e,n),o.isVisible()&&(i!==r?o.update(tM.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(tM.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=KO((()=>o.update(tM.refresh)),33);e.on("ScrollWindow",(()=>{const e=Wo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),uv(e)&&e.on("ElementScroll",(e=>{o.update(tM.refresh)}));const l=oc();l.set(ac(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),Cx(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{kx(t,!e)},isEnabled:()=>!Lm.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const dI="contexttoolbar-hide",uI=(e,t)=>Wr(y_,((o,n)=>{const s=(e=>({hide:()=>Fr(e,fr()),getValue:()=>vu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),mI=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Xh(Hv.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:Tl([Gp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Nr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=L(t,(t=>Xh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Ly({...s,type:"togglebutton",onAction:b}));return jB(r,o,[uI(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Ny({...s,type:"button",onAction:b}));return WB(r,o,[uI(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>L(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Lm.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},gI=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,pI=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=mt(ze(e.startContainer),e.startOffset).element;return(qe(o)?rt(o):A.some(o)).filter($e).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Wo();return Jo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Qo(ze(e.getBody()));return Jo(o.x+t.left,o.y+t.top,t.width,t.height)}},hI=(e,t,o,n=0)=>{const s=$o(window),r=Zo(ze(e.getContentAreaContainer())),a=Zb(e)||ov(e)||sv(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Jo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=vi(a,".tox-editor-header").getOr(a),l=Zo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Zo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Jo(i,c,l,d-c)}},fI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},bI={maxHeightFunction:gc(),maxWidthFunction:GM()},vI=e=>"node"===e,yI=(e,t,o,n,s)=>{const r=pI(e),a=n.lastElement().exists((e=>Qe(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=mt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Qe(n.element,t)})(e,o)?a?SE:bE:a?((e,o,s)=>{const a=Vt(e,"position");Bt(e,"position",o);const i=gI(r,Zo(t),-20)&&!n.isReposition()?CE:SE;return a.each((t=>Bt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Wo().top:s.y)+(jt(t)+12)<=r.y?bE:vE},xI=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...yI(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>vI(n)?[s(e)]:[];return t?{onLtr:e=>[gl,ll,cl,dl,ul,ml].concat(r(e)),onRtl:e=>[gl,cl,ll,ul,dl,ml].concat(r(e))}:{onLtr:e=>[ml,gl,dl,ll,ul,cl].concat(r(e)),onRtl:e=>[ml,gl,ul,cl,dl,ll].concat(r(e))}},wI=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},SI=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return H(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Xn(Yn("ContextForm",$y,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Yn("ContextToolbar",qy,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},kI=ca("forward-slide"),CI=ca("backward-slide"),OI=ca("change-slide-event"),_I="tox-pop--resizing",TI="tox-pop--transition",EI=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Bo().deviceType.isTouch,i=nc(),l=nc(),c=nc(),d=ci((e=>{const t=As([]);return Gh.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Gh.getContent(e).each((e=>{Ht(e.element,"visibility")})),Ga(e.element,_I),Ht(e.element,"width")},inlineBehaviours:Tl([th("context-toolbar-events",[Kr(nr(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ga(e.element,_I),Ht(e.element,"width"))})),Wr(OI,((e,t)=>{const o=e.element;Ht(o,"width");const n=Zt(o);Gh.setContent(e,t.event.contents),Wa(o,_I);const s=Zt(o);Bt(o,"width",n+"px"),Gh.getContent(e).each((e=>{t.event.focus.bind((e=>(Rl(e),Ll(o)))).orThunk((()=>(Gp.focusIn(e),zl(ft(o)))))})),setTimeout((()=>{Bt(e.element,"width",s+"px")}),0)})),Wr(kI,((e,o)=>{Gh.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:zl(ft(e.element))}]))})),Rr(e,OI,{contents:o.event.forwardContents,focus:A.none()})})),Wr(CI,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Rr(e,OI,{contents:di(o.bar),focus:o.focus})}))}))]),Gp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Fr(o,CI),A.some(!0))))})]),lazySink:()=>rn.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=vI(t)?1:0;return hI(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>xt(e)&&Ge(e))).map(Qo).getOrThunk((()=>pI(e))))(e,i.get()):pI(e);return t.height<=0||!gI(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Gh.hide(d)},h=()=>{if(Gh.isOpen(d)){const e=d.element;Ht(e,"display"),g()?Bt(e,"display","none"):(l.set(0),Gh.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:Tl([Gp.config({mode:"acyclic"}),th("pop-dialog-wrap-events",[Jr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Gp.focusIn(t)))})),Zr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=eo((()=>SI(t,(e=>{const t=y([e]);Rr(d,kI,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Ob(e)===rb.scrolling?rb.scrolling:rb.default,i=q(L(t,(t=>"contexttoolbar"===t.type?((t,o)=>KB(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>mI(e,t))(t,r.providers))));return fD({type:a,uid:ca("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:bc(12,0,fI),layouts:{onLtr:()=>[pl],onRtl:()=>[hl]},overrides:bI}:{bubble:bc(0,12,fI,1/12),layouts:xI(e,o,n,t),overrides:bI})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>_d.getMode(o)});return bn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Ht(b,"display"),(e=>xe(Se(e,i.get(),Qe),!0))(n)||(Ga(b,TI),_d.reset(o,d)),Gh.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[TI],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Bt(b,"display","none")};let w=!1;const S=KO((()=>{!e.hasFocus()||e.removed||w||($a(d.element,TI)?S.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>Qe(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!et(o,e))(s)?A.none():((e,t,o)=>{const n=wI(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=wI(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Fs(t,(e=>{if($e(e)){const{contextToolbars:t,contextForms:n}=wI(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(dI,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Gh.getContent(d).each(Gp.focusIn)}))})),e.on("focusout",(t=>{$h.setEditorTimeout(e,(()=>{Ll(o.element).isNone()&&Ll(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Ll(d.element).fold(S.throttle,b)}))}))},AI=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=nc();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},MI=e=>{AI(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:tv,hash:e=>((e,t)=>wB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:xw(e),onMenuSetup:xw(e)}))(e)),(e=>A.from(kb(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Rs(t,(e=>A.some(e).filter($e).bind((e=>Tt(e,"lang").map((t=>({code:t,customCode:Tt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=oc();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),yw(o.clear,xw(e)(t))},onMenuSetup:xw(e)}))))(e).each((t=>AI(e,t)))},DI=e=>Sw(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),BI=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),yw((()=>e.off("PastePlainTextToggle",n)),xw(e)(o))},II=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},FI=e=>{(e=>{(e=>{XO.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:ww(e,t.name),onAction:II(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:ww(e,o),onAction:II(e,o)})}})(e),(e=>{XO.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Cw(e,t.action)})})),XO.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:xw(e),onAction:Cw(e,t.action)})}))})(e),(e=>{XO.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Cw(e,t.action),onSetup:ww(e,t.name)})}))})(e)})(e),(e=>{XO.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:Cw(e,t.action)})})),XO.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:xw(e),onAction:Cw(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:xw(e),onAction:II(e,"code")})})(e)},RI=(e,t)=>Sw(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),NI=e=>Sw(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),VI=(e,t)=>{(e=>{H([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Cw(e,t.cmd),onSetup:ww(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:xw(e),onAction:Cw(e,"JustifyNone")})})(e),FI(e),((e,t)=>{((e,t)=>{const o=tB(0,t,lB(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:xw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=tB(0,t,vB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:xw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=tB(0,t,DB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:xw(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=tB(0,t,mB(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:xw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=tB(0,t,AB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:xw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:RI(e,"hasUndo"),onAction:Cw(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:RI(e,"hasRedo"),onAction:Cw(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:RI(e,"hasUndo"),onAction:Cw(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:RI(e,"hasRedo"),onAction:Cw(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=Ww(e),o=jw(e),n=As(t),s=As(o);eS(e,"forecolor","forecolor",n),eS(e,"backcolor","hilitecolor",s),tS(e,"forecolor","forecolor","Text color",n),tS(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:Cw(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:NI(e),onAction:Cw(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:DI(e),onAction:Cw(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:xw(e),onAction:Cw(e,"indent")})})(e)})(e),MI(e),(e=>{const t=As($b(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:BI(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:BI(e,t)})})(e)},zI=e=>r(e)?e.split(/[ ,]/):e,LI=e=>t=>t.options.get(e),HI=LI("contextmenu_never_use_native"),PI=LI("contextmenu_avoid_overlap"),UI=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},WI=(e,t)=>({type:"makeshift",x:e,y:t}),jI=e=>"longpress"===e.type||0===e.type.indexOf("touch"),GI=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(jI(e)){const t=e.touches[0];return WI(t.pageX,t.pageY)}return WI(e.pageX,e.pageY)})(t):((e,t)=>{const o=ib.DOM.getPos(e);return((e,t,o)=>WI(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(jI(e)){const t=e.touches[0];return WI(t.clientX,t.clientY)}return WI(e.clientX,e.clientY)})(t)):$I(e),$I=e=>({type:"selection",root:ze(e.selection.getNode())}),qI=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return GI(e,t);case"selection":return $I(e)}},YI=(e,t,o,n,s,r)=>{const a=o(),i=qI(e,t,r);I_(a,bv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Gh.showMenuAt(s,{anchor:i},{menu:{markers:Bv("normal")},data:e})}))},XI={onLtr:()=>[gl,ll,cl,dl,ul,ml,bE,vE,fE,pE,hE,gE],onRtl:()=>[gl,cl,ll,ul,dl,ml,bE,vE,hE,gE,fE,pE]},KI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},JI=(e,t,o,n,s,r)=>{const a=Bo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=qI(e,t,o);return{bubble:bc(0,"point"===o?12:0,KI),layouts:XI,overrides:{maxWidthFunction:GM(),maxHeightFunction:gc()},...n}})(e,t,r);I_(o,bv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?Uh.HighlightMenuAndItem:Uh.HighlightNone;Gh.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Bv("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(hI(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(dI)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{$h.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return td(e.getWin(),jc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},ZI=e=>r(e)?"|"===e:"separator"===e.type,QI={type:"separator"},eF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return QI;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,eF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},tF=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!ZI(e))).fold((()=>[]),(e=>[QI]));return e.concat(o).concat(t).concat([QI])},oF=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),nF=(e,t)=>oF(e,t)?e.selection.getStart(!0):t.target,sF=(e,t,o)=>{const n=Bo().deviceType.isTouch,s=ci(Gh.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:Tl([th("dismissContextMenu",[Wr(Or(),((t,o)=>{Zd.close(t),e.focus()}))])])})),a=()=>Gh.hide(s),i=t=>{if(HI(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!HI(e))(e,t)||(e=>0===UI(e).length)(e))return;const a=((e,t)=>{const o=PI(e),n=oF(e,t)?"selection":"point";if(De(o)){const s=nF(e,t);return JS(ze(s),o)?"node":n}return n})(e,t);(n()?JI:YI)(e,t,(()=>{const o=nF(e,t),n=e.ui.registry.getAll(),s=UI(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&De(Me(n)))return tF(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,eF);return tF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&ZI(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},rF=Ms([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),aF=e=>t=>t.translate(-e.left,-e.top),iF=e=>t=>t.translate(e.left,e.top),lF=e=>(t,o)=>j(e,((e,t)=>t(e)),qt(t,o)),cF=(e,t,o)=>e.fold(lF([iF(o),aF(t)]),lF([aF(t)]),lF([])),dF=(e,t,o)=>e.fold(lF([iF(o)]),lF([]),lF([iF(t)])),uF=(e,t,o)=>e.fold(lF([]),lF([aF(o)]),lF([iF(t),aF(o)])),mF=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},gF=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(uF,pF),s(dF,hF),s(cF,fF))},pF=rF.offset,hF=rF.absolute,fF=rF.fixed,bF=(e,t)=>{const o=_t(e,t);return u(o)?NaN:parseInt(o,10)},vF=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=bF(o,t.leftAttr),s=bF(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(qt(n,s))})(e,t).fold((()=>o),(e=>fF(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?xF(e,t,a,s,r):wF(e,t,a,s,r),l=cF(a,s,r);return((e,t,o)=>{const n=e.element;Ct(n,t.leftAttr,o.left+"px"),Ct(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:fF(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},yF=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=dF(e,s,r),i=dF(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:gF(e.output,t,o,n),extra:e.extra}):A.none()})),xF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return yF(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=dF(e,s,r),i=dF(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return qt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:gF(e.output,o,n,s),extra:e.extra})))}))},wF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return yF(r,o,n,s)};var SF=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=tt(e.element),o=Wo(t),r=MA(s),a=((e,t,o)=>({coord:gF(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=mF(a.coord,0,r);Ft(s,i)}}});const kF="data-initial-z-index",CF=(e,t)=>{e.getSystem().addToGui(t),(e=>{rt(e.element).filter($e).each((t=>{Vt(t,"z-index").each((e=>{Ct(t,kF,e)})),Bt(t,"z-index",Rt(e.element,"z-index"))}))})(t)},OF=e=>{(e=>{rt(e.element).filter($e).each((e=>{Tt(e,kF).fold((()=>Ht(e,"z-index")),(t=>Bt(e,"z-index",t))),At(e,kF)}))})(e),e.getSystem().removeFromGui(e)},_F=(e,t,o)=>e.getSystem().build(ik.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var TF=ys("snaps",[ns("getSnapPoints"),Ri("onSensor"),ns("leftAttr"),ns("topAttr"),xs("lazyViewport",tn),xs("mustSnap",!1)]);const EF=[xs("useFixed",T),ns("blockerClass"),xs("getTarget",w),xs("onDrag",b),xs("repositionTarget",!0),xs("onDrop",b),_s("getBounds",tn),TF],AF=e=>{return(t=Vt(e,"left"),o=Vt(e,"top"),n=Vt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?fF:pF)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Xt(e);return hF(t.left,t.top)}));var t,o,n},MF=(e,t)=>({bounds:e.getBounds(),height:Gt(t.element),width:Qt(t.element)}),DF=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>MF(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=tt(e.element),a=Wo(r),i=MA(s),l=AF(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=dF(t,o,n),i=Qi(a.left,r.x,r.x+r.width-s.width),l=Qi(a.top,r.y,r.y+r.height-s.height),c=hF(i,l);return t.fold((()=>{const e=uF(c,o,n);return pF(e.left,e.top)}),x(c),(()=>{const e=cF(c,o,n);return fF(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>pF(e+a,t+i)),((e,t)=>hF(e+a,t+i)),((e,t)=>fF(e+a,t+i))));var t,a,i;const l=cF(e,n,s);return fF(l.left,l.top)}),(t=>{const a=vF(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=mF(c,0,i);Ft(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},BF=(e,t,o,n)=>{t.each(OF),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;At(o,t.leftAttr),At(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},IF=e=>(t,o)=>{const n=e=>{o.setStartData(MF(t,e))};return Hr([Wr(wr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var FF=Object.freeze({__proto__:null,getData:e=>A.from(qt(e.x,e.y)),getDelta:(e,t)=>qt(t.left-e.left,t.top-e.top)});const RF=(e,t,o)=>[Wr(js(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>BF(n,A.some(l),e,t),a=ZS(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),DF(n,e,t,FF,o)}},l=_F(n,e.blockerClass,(e=>Hr([Wr(js(),e.forceDrop),Wr(qs(),e.drop),Wr(Gs(),((t,o)=>{e.move(o.event)})),Wr($s(),e.delayDrop)]))(i));o(n),CF(n,l)}))],NF=[...EF,Li("dragger",{handlers:IF(RF)})];var VF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(qt(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>qt(t.left-e.left,t.top-e.top)});const zF=(e,t,o)=>{const n=nc(),s=o=>{BF(o,n.get(),e,t),n.clear()};return[Wr(Hs(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{DF(r,e,t,VF,o)}},c=_F(r,e.blockerClass,(e=>Hr([Wr(Hs(),e.forceDrop),Wr(Us(),e.drop),Wr(Ws(),e.drop),Wr(Ps(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),CF(r,c)})),Wr(Ps(),((o,n)=>{n.stop(),DF(o,e,t,VF,n.event)})),Wr(Us(),((e,t)=>{t.stop(),s(e)})),Wr(Ws(),s)]},LF=NF,HF=[...EF,Li("dragger",{handlers:IF(zF)})],PF=[...EF,Li("dragger",{handlers:IF(((e,t,o)=>[...RF(e,t,o),...zF(e,t,o)]))})];var UF=Object.freeze({__proto__:null,mouse:LF,touch:HF,mouseOrTouch:PF}),WF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return Ta({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const jF=Dl({branchKey:"mode",branches:UF,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:WF,apis:SF}),GF=(e,t,o,n,s,r)=>e.fold((()=>jF.snap({sensor:hF(o-20,n-20),range:qt(s,r),output:hF(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return jF.snap({sensor:hF(s,r),range:qt(40,40),output:hF(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),$F=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Qe(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),qF=e=>Xh(qh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:Tl([jF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Hk.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),YF=(e,t)=>{const o=As([]),n=As([]),s=As(!1),r=nc(),a=nc(),i=e=>{const o=Qo(e);return GF(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Qo(e);return GF(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=$F((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=$F((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=qF(c),m=qF(d),g=ci(u.asSpec()),p=ci(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);jF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Ht(t.element,"display");const i=st(ze(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Bt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");Bo().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(Id(t,g),Id(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Nd(g),Nd(p),s.set(!1)),r.clear(),a.clear()})))},XF=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:Tl([Gp.config({mode:"flow",selector:"div[role=button]"}),Lm.config({disabled:o.isDisabled}),Ox(),pk.config({}),eh.config({}),th("elementPathEvents",[Jr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Gp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=vw(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>qh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ri(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:Tl([_x(o.isDisabled),Ox()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ri(` ${s} `)]},a])}),[]):[];eh.set(t,a)}))}))])]),components:[]}};var KF;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(KF||(KF={}));const JF=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:nI(n+t.top,bb(e),yb(e))};return o===KF.Both&&(r.width=nI(s+t.left,fb(e),vb(e))),r})(e,t,o,jt(n),Zt(n));le(s,((e,t)=>{h(e)&&Bt(n,t,oI(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},ZF=(e,t,o,n)=>{const s=qt(20*o,20*n);return JF(e,s,t),A.some(!0)},QF=(e,t)=>{const o=()=>{const o=[],n=Xb(e),s=Wb(e),r=jb(e)||e.hasPlugin("wordcount");return s&&o.push(XF(e,{},t)),n&&o.push((()=>{const e=Vx("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ri(qf.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>eh.set(e,[ri(t.translate(["{0} "+n,o[n]]))]);return qh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:Tl([_x(t.isDisabled),Ox(),pk.config({}),eh.config({}),vu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),th("wordcount-events",[ea((e=>{const t=vu.getValue(e),n="words"===t.mode?"characters":"words";vu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Jr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=vu.getValue(t);vu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[mr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),jb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v6",rel:"noopener",target:"_blank","aria-label":qf.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:Tl([ah.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=Gb(e);return!1===t?KF.None:"both"===t?KF.Both:KF.Vertical})(e);if(o===KF.None)return A.none();const n=o===KF.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ob("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-label":t.translate(n)},behaviours:[jF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>JF(e,s,o),blockerClass:"tox-blocker"}),Gp.config({mode:"special",onLeft:()=>ZF(e,o,-1,0),onRight:()=>ZF(e,o,1,0),onUp:()=>ZF(e,o,0,-1),onDown:()=>ZF(e,o,0,1)}),pk.config({}),ah.config({})]},t.icons))})(e,t);return n.concat(s.toArray())})()}},eR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),tR=(e,t)=>{const o=e.inline,n=o?cI:eI,s=dv(e)?uM:EA,r=(()=>{const e=nc(),t=nc(),o=nc();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Qe(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=nc(),i=nc(),l=nc(),c=Bo().deviceType.isTouch()?["tox-platform-touch"]:[],d=rv(e),u=Ob(e),m=Xh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Xh({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(UD.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",UD.getToolbar),v=r.lazyGetInOuterOrDie("throbber",UD.getThrobber),y=((e,t,o,n)=>{const s=As(!1),r=(e=>{const t=As(rv(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:qf.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},i=pA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=As([]),s=As([]),r=As(!1);return e.on("PreInit",(s=>{const r=PE(e),a=WE(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=WE(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:BE(e),hasCustomColors:IE(e),getColors:FE(e),getColorCols:RE(e)}))(t),d=(e=>({isDraggableModal:NE(e)}))(t),u={shared:{providers:a,anchors:DE(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m={...u,shared:{...u.shared,interpreter:e=>rE(e,{},m),getSink:e.popup}},g={...u,shared:{...u.shared,interpreter:e=>rE(e,{},g),getSink:e.dialog}};return{popup:m,dialog:g}})({popup:()=>rn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>rn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[kc]:d?Sc.BottomToTop:Sc.TopToBottom}},o=UD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=UD.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=UD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=sv(e),a=ov(e),i=Zb(e),l=Yb(e),c=UD.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return UD.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:q([i?h:[],r?[s]:a?[n]:[],iv(e)?[]:[m.asSpec()]]),sticky:dv(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[UD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),UD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=UD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=UD.parts.viewWrapper({backstage:y.popup}),i=Ub(e)&&!o?A.some(QF(e,y.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),h=UD.parts.editorContainer({components:q([l,o?[]:[g.asSpec(),...i.toArray()]])}),f=cv(e),v={role:"application",...qf.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=ci(UD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r],s],behaviours:Tl([Ox(),Lm.config({disableClass:"tox-tinymce--disabled"}),Gp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=lk(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=oI((e=>{const t=(e=>{const t=pb(e),o=bb(e),n=yb(e);return tI(t).map((e=>nI(e,o,n)))})(e);return t.getOr(pb(e))})(e)),n=oI((e=>sI(e).getOr(hb(e)))(e));return e.inline||(Lt("div","width",n)&&Bt(t.element,"width",n),Lt("div","height",o)?Bt(t.element,"height",o):Bt(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>eR("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>eR("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=lv(e),o=Qe(wt(),t)&&"grid"===Rt(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...qf.isRtl()?{dir:"rtl"}:{}}},behaviours:Tl([_d.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hr([Wr(Sr(),(e=>{Bt(e.element,"width",document.body.clientWidth+"px")}))])},a=ci(bn(n,o?r:{})),l=lk(a);return i.set(l),{sink:a,mothership:l}})(),d=uv(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...qf.isRtl()?{dir:"rtl"}:{}}},behaviours:Tl([_d.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=ci(e),n=lk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(_b(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=nv(e),h={menuItems:l,menus:mv(e),menubar:Bb(e),toolbar:g.getOrThunk((()=>Ib(e))),allowToolbarGroups:u===rb.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{UD.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{UD.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?UD.toggleToolbarDrawerWithoutFocusing(f):UD.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>UD.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{H([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{H([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Qd(),{target:e.target}),a=qo(),i=rc(a,"touchstart",r),l=rc(a,"touchmove",(e=>n(yr(),e))),c=rc(a,"touchend",(e=>n(xr(),e))),d=rc(a,"mousedown",r),u=rc(a,"mouseup",(e=>{0===e.raw.button&&s(tu(),{target:e.target})})),m=e=>s(Qd(),{target:ze(e.target)}),g=e=>{0===e.button&&s(tu(),{target:ze(e.target)})},p=()=>{H(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(wr(),ic(e)),f=e=>{s(eu(),{}),n(Sr(),ic(e))},b=ft(ze(e.getElement())),v=ac(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=XS(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>Qe(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Ar(),o))}}))})),y=()=>s(eu(),{}),x=t=>{t.state&&s(Qd(),{target:ze(e.getContainer())})},w=e=>{s(Qd(),{target:ze(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{H([t,...o],Pd),H([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),VI(e,y.popup),sF(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();H(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),NM(e,v,y.popup.shared),EI(e,c,r.sink,{backstage:y.popup}),YF(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},oR=x([ns("lazySink"),ms("dragBlockClass"),_s("getBounds",tn),xs("useTabstopAt",E),xs("firstTabstop",0),xs("eventOrder",{}),yu("modalBehaviours",[Gp]),Ni("onExecute"),zi("onEscape")]),nR={sketch:w},sR=x([Yu({name:"draghandle",overrides:(e,t)=>({behaviours:Tl([jF.config({mode:"mouse",getTarget:e=>fi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),$u({schema:[ns("dom")],name:"title"}),$u({factory:nR,schema:[ns("dom")],name:"close"}),$u({factory:nR,schema:[ns("dom")],name:"body"}),Yu({factory:nR,schema:[ns("dom")],name:"footer"}),qu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[xs("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),xs("components",[])],name:"blocker"})]),rR=wm({name:"ModalDialog",configFields:oR(),partFields:sR(),factory:(e,t,o,n)=>{const s=nc(),r=ca("modal-events"),a={...e.eventOrder,[kr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([di(t)]),behaviours:Tl([ah.config({}),th("dialog-blocker-events",[Kr(Xs(),(()=>{FM.isBlocked(t)||Gp.focusIn(t)}))])])});Id(o,a),Gp.focusIn(t)},hide:e=>{s.clear(),rt(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Nd(e)}))}))},getBody:t=>im(t,e,"body"),getFooter:t=>am(t,e,"footer"),setIdle:e=>{FM.unblock(e)},setBusy:(e,t)=>{FM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:wu(e.modalBehaviours,[eh.config({}),Gp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),FM.config({getRoot:s.get}),th(r,[Jr((t=>{((e,t)=>{const o=Tt(e,"id").fold((()=>{const e=ca("dialog-label");return Ct(t,"id",e),e}),w);Ct(e,"aria-labelledby",o)})(t.element,im(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),aR=Bn([sy,ry].concat(ex)),iR=Pn,lR=[Dy("button"),vy,Cs("align","end",["start","end"]),_y,Oy,fs("buttonType",["primary","secondary"])],cR=[...lR,iy],dR=[is("type",["submit","cancel","custom"]),...cR],uR=[is("type",["menu"]),by,yy,vy,us("items",aR),...lR],mR=[...lR,is("type",["togglebutton"]),as("tooltip"),vy,by,Os("active",!1)],gR=Zn("type",{submit:dR,cancel:dR,custom:dR,menu:uR,togglebutton:mR}),pR=[sy,iy,is("level",["info","warn","error","success"]),cy,xs("url","")],hR=Bn(pR),fR=[sy,iy,Oy,Dy("button"),vy,Cy,fs("buttonType",["primary","secondary","toolbar"]),_y],bR=Bn(fR),vR=[sy,ry],yR=vR.concat([xy]),xR=vR.concat([ay,Oy]),wR=Bn(xR),SR=Pn,kR=yR.concat([Ty("auto")]),CR=Bn(kR),OR=Nn([dy,iy,cy]),_R=yR.concat([ks("storageKey","default")]),TR=Bn(_R),ER=Hn,AR=Bn(yR),MR=Hn,DR=vR.concat([ks("tag","textarea"),as("scriptId"),as("scriptUrl"),ws("settings",void 0,jn)]),BR=vR.concat([ks("tag","textarea"),ls("init")]),IR=$n((e=>Yn("customeditor.old",Dn(BR),e).orThunk((()=>Yn("customeditor.new",Dn(DR),e))))),FR=Hn,RR=Bn(yR),NR=In(_n),VR=e=>[sy,rs("columns"),e],zR=[sy,as("html"),Cs("presets","presentation",["presentation","document"])],LR=Bn(zR),HR=yR.concat([Os("border",!1),Os("sandboxed",!0),Os("streamContent",!1),Os("transparent",!0)]),PR=Bn(HR),UR=Hn,WR=Bn(vR.concat([hs("height")])),jR=Bn([as("url"),ps("zoom"),ps("cachedWidth"),ps("cachedHeight")]),GR=yR.concat([hs("inputMode"),hs("placeholder"),Os("maximized",!1),Oy]),$R=Bn(GR),qR=Hn,YR=e=>[sy,ay,e,Cs("align","start",["start","center","end"])],XR=[iy,dy],KR=[iy,us("items",Qn(0,(()=>JR)))],JR=Fn([Bn(XR),Bn(KR)]),ZR=yR.concat([us("items",JR),Oy]),QR=Bn(ZR),eN=Hn,tN=yR.concat([ds("items",[iy,dy]),Ss("size",1),Oy]),oN=Bn(tN),nN=Hn,sN=yR.concat([Os("constrain",!0),Oy]),rN=Bn(sN),aN=Bn([as("width"),as("height")]),iN=vR.concat([ay,Ss("min",0),Ss("max",0)]),lN=Bn(iN),cN=Ln,dN=[sy,us("header",Hn),us("cells",In(Hn))],uN=Bn(dN),mN=yR.concat([hs("placeholder"),Os("maximized",!1),Oy]),gN=Bn(mN),pN=Hn,hN=[is("type",["directory","leaf"]),ly,as("id"),gs("menu",pM)],fN=Bn(hN),bN=hN.concat([us("children",Qn(0,(()=>Gn("type",{directory:vN,leaf:fN}))))]),vN=Bn(bN),yN=Gn("type",{directory:vN,leaf:fN}),xN=[sy,us("items",yN),bs("onLeafAction"),bs("onToggleExpand"),Ts("defaultExpandedIds",[],Hn),hs("defaultSelectedId")],wN=Bn(xN),SN=yR.concat([Cs("filetype","file",["image","media","file"]),Oy,hs("picker_text")]),kN=Bn(SN),CN=Bn([dy,Ey]),ON=e=>es("items","items",{tag:"required",process:{}},In($n((t=>Yn(`Checking item of ${e}`,_N,t).fold((e=>rn.error(Jn(e))),(e=>rn.value(e))))))),_N=An((()=>{return Gn("type",{alertbanner:hR,bar:Bn((e=ON("bar"),[sy,e])),button:bR,checkbox:wR,colorinput:TR,colorpicker:AR,dropzone:RR,grid:Bn(VR(ON("grid"))),iframe:PR,input:$R,listbox:QR,selectbox:oN,sizeinput:rN,slider:lN,textarea:gN,urlinput:kN,customeditor:IR,htmlpanel:LR,imagepreview:WR,collection:CR,label:Bn(YR(ON("label"))),table:uN,tree:wN,panel:EN});var e})),TN=[sy,xs("classes",[]),us("items",_N)],EN=Bn(TN),AN=[Dy("tab"),ly,us("items",_N)],MN=[sy,ds("tabs",AN)],DN=Bn(MN),BN=cR,IN=gR,FN=Bn([as("title"),ss("body",Gn("type",{panel:EN,tabpanel:DN})),ks("size","normal"),Ts("buttons",[],IN),xs("initialData",{}),_s("onAction",b),_s("onChange",b),_s("onSubmit",b),_s("onClose",b),_s("onCancel",b),_s("onTabChange",b)]),RN=Bn([is("type",["cancel","custom"]),...BN]),NN=Bn([as("title"),as("url"),ps("height"),ps("width"),vs("buttons",RN),_s("onAction",b),_s("onCancel",b),_s("onClose",b),_s("onMessage",b)]),VN=e=>a(e)?[e].concat(Y(fe(e),VN)):l(e)?Y(e,VN):[],zN=e=>r(e.type)&&r(e.name),LN={checkbox:SR,colorinput:ER,colorpicker:MR,dropzone:NR,input:qR,iframe:UR,imagepreview:jR,selectbox:nN,sizeinput:aN,slider:cN,listbox:eN,size:aN,textarea:pN,urlinput:CN,customeditor:FR,collection:OR,togglemenuitem:iR},HN=e=>{const t=(e=>U(VN(e),zN))(e),o=Y(t,(e=>(e=>A.from(LN[e.type]))(e).fold((()=>[]),(t=>[ss(e.name,t)]))));return Bn(o)},PN=e=>{var t;return{internalDialog:Xn(Yn("dialog",FN,e)),dataValidator:HN(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},UN={open:(e,t)=>{const o=PN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Xn(Yn("dialog",NN,t))),redial:e=>PN(e)};var WN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?qp:$p)(o,r)}))};return Hr([Wr(ur(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Jr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),jN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),GN=[ns("channel"),ms("renderComponents"),ms("updateState"),ms("initialData"),Os("reuseDom",!0)];const $N=Al({fields:GN,name:"reflecting",active:WN,apis:jN,state:Object.freeze({__proto__:null,init:()=>{const e=As(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),qN=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?rn.error(t):rn.value(o)},YN=(e,t,o)=>{const n=Xh(IO.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>nE(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:Tl([Gp.config({mode:"acyclic",useTabstopAt:C(n_)}),(s=n,Om.config({find:s.getOpt})),WO(n,{postprocess:e=>qN(e).fold((e=>(console.error(e),{})),w)}),th("dialog-body-panel",[Wr(Xs(),((e,t)=>{e.getSystem().broadcastOn([c_],{newFocus:A.some(t.event.target)})}))])])};var s},XN=xm({name:"TabButton",configFields:[xs("uid",void 0),ns("value"),es("dom","dom",wn((()=>({attributes:{role:"tab",id:ca("aria"),"aria-selected":"false"}}))),Vn()),ms("action"),xs("domModification",{}),yu("tabButtonBehaviours",[ah,Gp,vu]),ns("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:fh(e.action),behaviours:wu(e.tabButtonBehaviours,[ah.config({}),Gp.config({mode:"execution",useSpace:!0,useEnter:!0}),vu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),KN=x([ns("tabs"),ns("dom"),xs("clickToDismiss",!1),yu("tabbarBehaviours",[Xm,Gp]),Ii(["tabClass","selectedClass"])]),JN=Xu({factory:XN,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Xm.dehighlight(e,t),Rr(e,Dr(),{tabbar:e,button:t})},o=(e,t)=>{Xm.highlight(e,t),Rr(e,Mr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Xm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),ZN=x([JN]),QN=wm({name:"Tabbar",configFields:KN(),partFields:ZN(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:wu(e.tabbarBehaviours,[Xm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{Ct(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{Ct(t.element,"aria-selected","false")}}),Gp.config({mode:"flow",getInitial:e=>Xm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),eV=xm({name:"Tabview",configFields:[yu("tabviewBehaviours",[eh])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:wu(e.tabviewBehaviours,[eh.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),tV=x([xs("selectFirst",!0),Ri("onChangeTab"),Ri("onDismissTab"),xs("tabs",[]),yu("tabSectionBehaviours",[])]),oV=$u({factory:QN,schema:[ns("dom"),cs("markers",[ns("tabClass"),ns("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),nV=$u({factory:eV,name:"tabview"}),sV=x([oV,nV]),rV=wm({name:"TabSection",configFields:tV(),partFields:sV(),factory:(e,t,o,n)=>{const s=(t,o)=>{am(t,e,"tabbar").each((e=>{o(e).each(Nr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:xu(e.tabSectionBehaviours),events:Hr(q([e.selectFirst?[Jr(((e,t)=>{s(e,Xm.getFirst)}))]:[],[Wr(Mr(),((t,o)=>{(t=>{const o=vu.getValue(t);am(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Tt(t.element,"id").each((e=>{Ct(n.element,"aria-labelledby",e)})),eh.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Wr(Dr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>am(t,e,"tabview").map((e=>eh.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Xm.getCandidates(e);return G(o,(e=>vu.getValue(e)===t)).filter((t=>!Xm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),aV=(e,t)=>{Bt(e,"height",t+"px"),Bt(e,"flex-basis",t+"px")},iV=(e,t,o)=>{fi(e,'[role="dialog"]').each((e=>{vi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Bt(t,"height","0"),Bt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=nt(e).dom,s=fi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Rt(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=jt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Zt(o)?Math.max(jt(o),a):a,l=parseInt(Rt(e,"margin-top"),10)||0,c=parseInt(Rt(e,"margin-bottom"),10)||0;return r-(jt(e)+l+c-i)})(e,t,n))))).each((e=>{aV(t,e)}))}))}))},lV=e=>vi(e,'[role="tabpanel"]'),cV="send-data-to-section",dV="send-data-to-view",uV=(e,t,o)=>{const n=As({}),s=e=>{const t=vu.getValue(e),o=qN(t).getOr({}),s=n.get(),r=bn(s,o);n.set(r)},r=e=>{const t=n.get();vu.setValue(e,t)},a=As(null),i=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ri(o.shared.providers.translate(e.title))],view:()=>[IO.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>nE(n,e,t,o))),formBehaviours:Tl([Gp.config({mode:"acyclic",useTabstopAt:C(n_)}),th("TabView.form.events",[Jr(r),Zr(s)]),Il.config({channels:Bs([{key:cV,value:{onReceive:s}},{key:dV,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=nc(),o=[Jr((o=>{const n=o.element;lV(n).each((s=>{Bt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{eh.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return eh.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),iV(n,s,t),Ht(s,"visibility"),((e,t)=>{oe(e).each((e=>rV.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{iV(n,s,t)}))}))})),Wr(Sr(),(e=>{const o=e.element;lV(o).each((e=>{iV(o,e,t)}))})),Wr(Ek,((e,o)=>{const n=e.element;lV(n).each((e=>{const o=zl(ft(e));Bt(e,"visibility","hidden");const s=Vt(e,"height").map((e=>parseInt(e,10)));Ht(e,"height"),Ht(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),iV(n,e,t)):s.each((t=>{aV(e,t)})),Ht(e,"visibility"),o.each(Rl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return rV.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=vu.getValue(t);Rr(e,Tk,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[rV.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[QN.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:Tl([pk.config({})])}),rV.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:Tl([th("tabpanel",l.extraEvents),Gp.config({mode:"acyclic"}),Om.config({find:e=>oe(rV.getViewItems(e))}),jO(A.none(),(e=>(e.getSystem().broadcastOn([cV],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([dV],{})}))])})},mV=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:Tl([PO(0),$N.config({channel:`${a_}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[uV(t,e.initialData,n)]:[YN(t,e.initialData,n)]},initialData:e})])}),gV=cb.deviceType.isTouch(),pV=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),hV=(e,t)=>rR.parts.close(qh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:Tl([pk.config({})])})),fV=()=>rR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),bV=(e,t)=>rR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Yh(`<p>${$f(t.translate(e))}</p>`)}]}]}),vV=e=>rR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),yV=(e,t)=>[ik.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),ik.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],xV=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return rR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!n_(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Yh(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:gV?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:Tl([ah.config({}),th("dialog-events",e.dialogEvents.concat([Kr(Xs(),((e,t)=>{FM.isBlocked(e)||Gp.focusIn(e)})),Wr(Tr(),((e,t)=>{e.getSystem().broadcastOn([c_],{newFocus:t.event.newFocus})}))])),th("scroll-lock",[Jr((()=>{Wa(wt(),s)})),Zr((()=>{Ga(wt(),s)}))]),...e.extraBehaviours]),eventOrder:{[mr()]:["dialog-events"],[kr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Cr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},wV=e=>qh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},buttonBehaviours:Tl([pk.config({})]),components:[ob("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Fr(e,Sk)}}),SV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:Tl([$N.config({channel:`${r_}-${t}`,initialData:e,renderComponents:e=>[ri(n.translate(e.title))]})])}),kV=()=>({dom:Yh('<div class="tox-dialog__draghandle"></div>')}),CV=(e,t,o)=>((e,t,o)=>{const n=rR.parts.title(SV(e,t,A.none(),o)),s=rR.parts.draghandle(kV()),r=rR.parts.close(wV(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return ik.sketch({dom:Yh('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),OV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Yh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),_V=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=vi(e().element,".tox-dialog__header").map((e=>jt(e)));rR.setBusy(e(),((e,s)=>OV(o.message,s,t,n)))},onUnblock:()=>{rR.setIdle(e())}}),TV="tox-dialog--fullscreen",EV="tox-dialog--width-lg",AV="tox-dialog--width-md",MV=e=>{switch(e){case"large":return A.some(EV);case"medium":return A.some(AV);default:return A.none()}},DV=(e,t)=>{const o=ze(t.element.dom);$a(o,TV)||(Ya(o,[EV,AV]),MV(e).each((e=>Wa(o,e))))},BV=(e,t)=>{const o=ze(e.element.dom),n=Xa(o),s=G(n,(e=>e===EV||e===AV)).or(MV(t));((e,t)=>{H(t,(t=>{((e,t)=>{const o=La(e)?e.dom.classList.toggle(t):((e,t)=>R(Ha(e),t)?Ua(e,t):Pa(e,t))(e,t);ja(e)})(e,t)}))})(o,[TV,...s.toArray()])},IV=(e,t,o)=>ci(xV({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[$O({}),...e.extraBehaviours],onEscape:e=>{Fr(e,Sk)},dialogEvents:t,eventOrder:{[ur()]:[$N.name(),Il.name()],[kr()]:["scroll-lock",$N.name(),"messages","dialog-events","alloy.base.behaviour"],[Cr()]:["alloy.base.behaviour","dialog-events","messages",$N.name(),"scroll-lock"]}})),FV=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=be(t,e.name).getOr(As(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),RV=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),NV=(e,t)=>[qr(Xs(),o_),e(wk,((e,o,n,s)=>{zl(ft(s.element)).fold(b,Nl),t.onClose(),o.onClose()})),e(Sk,((e,t,o,n)=>{t.onCancel(e),Fr(n,wk)})),Wr(_k,((e,o)=>t.onUnblock())),Wr(Ok,((e,o)=>t.onBlock(o.event)))],VV=(e,t,o)=>{const n=(t,o)=>Wr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{$N.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...NV(n,t),n(Ck,((e,t)=>t.onSubmit(e))),n(xk,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(kk,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?Gp.focusIn(s):void 0,a=e=>Et(e,"disabled")||Tt(e,"aria-disabled").exists((e=>"true"===e)),i=ft(s.element),l=zl(i);t.onAction(e,{name:n.name,value:n.value}),zl(i).fold(r,(e=>{a(e)||l.exists((t=>et(e,t)&&a(t)))?r():o().toOptional().filter((t=>!et(t.element,e))).each(r)}))})),n(Tk,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Zr((t=>{const o=e();vu.setValue(t,o.getData())}))]},zV=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>ik.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},LV=(e,t,o)=>({dom:Yh('<div class="tox-dialog__footer"></div>'),components:[],behaviours:Tl([$N.config({channel:`${i_}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=Xh(((e,t)=>PT(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:zV})])}),HV=(e,t,o)=>rR.parts.footer(LV(e,t,o)),PV=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=Om.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return IO.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>$N.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},UV=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...vu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=bn(r,t),i=((e,t)=>{const o=e.getRoot();return $N.getState(o).get().map((e=>Xn(Yn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();vu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{PV(e,t).each(o?Lm.enable:Lm.disable)},focus:t=>{PV(e,t).each(ah.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Rr(t,Ok,{message:e})}))},unblock:()=>{n((e=>{Fr(e,_k)}))},showTab:t=>{n((o=>{const n=e.getBody();$N.getState(n).get().exists((e=>e.isTabPanel()))&&Om.getCurrent(n).each((e=>{rV.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=FV(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${s_}-${a}`],i),n.getSystem().broadcastOn([`${r_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${a_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${i_}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Fr(e,wk)}))},toggleFullscreen:e.toggleFullscreen};return s},WV=(e,t,o,n=!1,s)=>{const r=ca("dialog"),a=ca("dialog-label"),i=ca("dialog-content"),l=e.internalDialog,c=As(l.size),d=MV(c.get()).toArray(),u=Xh(((e,t,o,n)=>ik.sketch({dom:Yh('<div class="tox-dialog__header"></div>'),components:[SV(e,t,A.some(o),n),kV(),wV(n)],containerBehaviours:Tl([jF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>yi(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=Xh(((e,t,o,n,s)=>mV(e,t,A.some(o),n,s))({body:l.body,initialData:l.initialData},r,i,o,n)),g=FV(l.buttons),p=RV(g),h=Ce(0!==g.length,Xh(((e,t,o)=>LV(e,t,o))({buttons:g},r,o))),f=VV((()=>v),{onBlock:e=>{FM.block(b,((t,n)=>{const s=u.getOpt(b).map((e=>jt(e.element)));return OV(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{FM.unblock(b)},onClose:()=>t.closeWindow()},o.shared.getSink),b=ci({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog","aria-labelledby":a}},eventOrder:{[ur()]:[$N.name(),Il.name()],[mr()]:["execute-on-form"],[kr()]:["reflecting","execute-on-form"]},behaviours:Tl([Gp.config({mode:"cyclic",onEscape:e=>(Fr(e,wk),A.some(!0)),useTabstopAt:e=>!n_(e)&&("button"!==We(e)||"disabled"!==_t(e,"disabled")),firstTabstop:1}),$N.config({channel:`${s_}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),DV(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),ah.config({}),th("execute-on-form",f.concat([Kr(Xs(),((e,t)=>{Gp.focusIn(e)})),Wr(Tr(),((e,t)=>{e.getSystem().broadcastOn([c_],{newFocus:t.event.newFocus})}))])),FM.config({getRoot:()=>A.some(b)}),eh.config({}),$O({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),v=UV({getId:x(r),getRoot:x(b),getFooter:()=>h.map((e=>e.get(b))),getBody:()=>m.get(b),getFormWrapper:()=>{const e=m.get(b);return Om.getCurrent(e).getOr(e)},toggleFullscreen:()=>{BV(b,c.get())}},t.redial,p);return{dialog:b,instanceApi:v}};var jV=tinymce.util.Tools.resolve("tinymce.util.URI");const GV=["insertContent","setContent","execCommand","close","block","unblock"],$V=e=>a(e)&&-1!==GV.indexOf(e.mceAction),qV=(e,t,o,n)=>{const s=ca("dialog"),i=CV(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[e_(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:Tl([pk.config({}),ah.config({})])})]}],behaviours:Tl([Gp.config({mode:"acyclic",useTabstopAt:C(n_)})])};return rR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(HV({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Wr(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{$N.getState(e).get().each((o=>{t(o,e)}))};return[...NV(o,t),o(kk,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,_V((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new jV(e.url,{base_uri:new jV(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=oc(),v=[$N.config({channel:`${s_}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),th("messages",[Jr((()=>{const t=rc(ze(window),"message",(t=>{if(h.isSameOrigin(new jV(t.raw.origin))){const n=t.raw.data;$V(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!$V(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Zr(b.clear)]),Il.config({channels:{[l_]:{onReceive:(e,t)=>{vi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=IV({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Rr(t,Ok,{message:e})}))},unblock:()=>{t((e=>{Fr(e,_k)}))},close:()=>{t((e=>{Fr(e,wk)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([l_],e)}))}}})(y);return{dialog:y,instanceApi:x}},YV=(e,t)=>Xn(Yn("data",t,e)),XV=e=>JS(e,".tox-alert-dialog")||JS(e,".tox-confirm-dialog"),KV=(e,t,o)=>t&&o?[]:[tM.config({contextual:{lazyContext:()=>A.some(Zo(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>XS(e,t.element).map((e=>({bounds:KS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:tn(),optScrollEnv:A.none()})))})],JV=e=>{const t=e.editor,o=dv(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{rR.hide(l),n()},r=Xh(PT({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=fV(),i=hV(s,t.providers),l=ci(xV({lazySink:()=>t.getSink(),header:pV(a,i),body:bV(o,t.providers),footer:A.some(vV(yV([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wr(Sk,s)],eventOrder:{}}));rR.show(l);const c=r.get(l);ah.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{rR.hide(c),n(e)},r=Xh(PT({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=PT({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=fV(),l=hV((()=>s(!1)),t.providers),c=ci(xV({lazySink:()=>t.getSink(),header:pV(i,l),body:bV(o,t.providers),footer:A.some(vV(yV([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wr(Sk,(()=>s(!1))),Wr(Ck,(()=>s(!0)))],eventOrder:{}}));rR.show(c);const d=r.get(c);ah.focus(d)}}})(e.backstages.dialog),r=(t,o)=>UN.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=ca("dialog"),s=e.internalDialog,r=CV(s.title,n,o),a=As(s.size),i=MV(a.get()).toArray(),l=((e,t,o)=>{const n=mV(e,t,A.none(),o,!1);return rR.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),c=FV(s.buttons),d=RV(c),u=Ce(0!==c.length,HV({buttons:c},n,o)),m=VV((()=>f),_V((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[$N.config({channel:`${s_}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),DV(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=IV(g,m,o),h={getId:x(n),getRoot:x(p),getBody:()=>rR.getBody(p),getFooter:()=>rR.getFooter(p),getFormWrapper:()=>{const e=rR.getBody(p);return Om.getCurrent(e).getOr(e)},toggleFullscreen:()=>{BV(p,a.get())}},f=UV(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:UN.redial,closeWindow:()=>{rR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return rR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>UN.open(((n,i,l)=>{const c=YV(i,l),d=nc(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Gh.reposition(e),o&&u||tM.refresh(e)})),g=WV({dataValidator:l,initialData:c,internalDialog:n},{redial:UN.redial,closeWindow:()=>{d.on(Gh.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=ci(Gh.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:Tl([th("window-manager-inline-events",[Wr(Or(),((e,t)=>{Fr(g.dialog,Sk)}))]),...KV(t,o,u)]),isExtraPart:(e,t)=>XV(t)}));return d.set(p),Gh.showWithinBounds(p,di(g.dialog),{anchor:s},(()=>{const e=t.inline?wt():ze(t.getContainer()),o=Zo(e);return A.some(o)})),o&&u||(tM.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Gp.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>UN.open(((o,a,i)=>{const l=YV(a,i),c=nc(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Gh.reposition(e),tM.refresh(e)})),m=WV({dataValidator:i,initialData:l,internalDialog:o},{redial:UN.redial,closeWindow:()=>{c.on(Gh.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=ci(Gh.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:Tl([th("window-manager-inline-events",[Wr(Or(),((e,t)=>{Fr(m.dialog,Sk)}))]),tM.config({contextual:{lazyContext:()=>A.some(Zo(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>XS(t,e.element).map((e=>({bounds:KS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:tn(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>XV(t)}));return c.set(g),Gh.showWithinBounds(g,di(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=XS(t,e.element).map((e=>KS(e))).getOr(tn()),n=Zo(ze(t.getContentAreaContainer())),s=en(n,o);return A.some(Jo(s.x,s.y,s.width,s.height-15))})))),tM.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),Gp.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>UN.openUrl((o=>{const s=qV(o,{closeWindow:()=>{rR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return rR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};on.add("silver",(e=>{(e=>{mb(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:Rw(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:Lw(e)}),t("color_cols_foreground",{processor:n,default:Hw(e,Iw)}),t("color_cols_background",{processor:n,default:Hw(e,Fw)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:Vw}),t("color_default_background",{processor:"string",default:Vw})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:zI(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>tn();const{dialogs:o,popups:n,renderUI:s}=tR(e,{getPopupSinkBounds:()=>t()});GS(e,n.backstage.shared);const a=JV({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:()=>{const o=s();return XS(e,n.getMothership().element).each((e=>{t=()=>KS(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Zo(ze(e.getContentAreaContainer())),o=tn(),n=Qi(o.x,t.x,t.right),s=Qi(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Jo(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Gh.hide(l)},i=ci(sb.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=ci(Gh.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&$h.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=di(i),o={maxHeightFunction:gc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Gh.showWithinBounds(l,t,{anchor:e},s)}else F(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:wt(),node:A.some(ze(n)),overrides:o,layouts:{onRtl:()=>[gl],onLtr:()=>[gl]}};Gh.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{sb.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{sb.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();