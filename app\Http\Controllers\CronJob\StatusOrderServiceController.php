<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Api\Service\BaostarController;
use App\Http\Controllers\Api\Service\BoosterviewsController;
use App\Http\Controllers\Api\Service\CheoTuongTacController;
use App\Http\Controllers\Api\Service\Hacklike17Controller;
use App\Http\Controllers\Api\Service\SmmKingController;
use App\Http\Controllers\Api\Service\SubgiareController;
use App\Http\Controllers\Api\Service\TuongTacSaleController;
use App\Http\Controllers\Api\Service\SharegiareController;
use App\Http\Controllers\Api\Service\TanglikeautoController;
use App\Http\Controllers\Api\Service\TraodoisubController;
use App\Http\Controllers\Api\Service\TuongtaccheoController;
use App\Http\Controllers\Api\Service\TwoMxhController;
use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Cache;
use App\Library\DiscordSdk;
use App\Models\Smm;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderRefunded;
use App\Models\Card;
use App\Models\Recharge;
use App\Models\ServiceServer;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class StatusOrderServiceController extends Controller {

    public function refundAllOrders(Request $request)
    {
        $domain = $request->getHost();
        $orders = Order::whereIn("status", ["PendingRefundCancel", "PendingRefundPartial",])->get();
    
        if ($orders->isEmpty()) {
            return response()->json([
                "code" => "200",
                "status" => "success",
                "message" => "Không có đơn hàng cần hoàn tiền!",
            ], 200);
        }
    
        $cacheKey = "refunded_orders_{$domain}";
        $refundedOrderIds = Cache::get($cacheKey, []);
    
        foreach ($orders as $order) {
            if (in_array($order->id, $refundedOrderIds)) {
                continue; 
            }
    
            if (!$order->user || in_array($order->status, ["Refunded", "Cancelled", "Failed"])) {
                continue;
            }
    
            $alreadyRefunded = OrderRefunded::where('order_id', $order->id)
                ->where('user_id', $order->user_id)
                ->exists();
            if ($alreadyRefunded) {
                continue;
            }
    
            $orderData = json_decode($order->order_data);
            $quantity = $orderData->quantity;
            $price = $orderData->price;
    
            if ($order->status === "PendingRefundCancel") {
                $refundPercent = 100;
            } elseif ($order->status === "PendingRefundPartial") {
                $refundPercent = ($quantity > 0) ? (($quantity - $order->buff) / $quantity) * 100 : 0;
            } else {
                $refundPercent = 0;
            }
    
            $returnedQuantity = ($refundPercent / 100) * $quantity;
            $refundAmount = ceil($returnedQuantity * $price);
    
            OrderRefunded::create([
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'order_code' => $order->order_code,
                'refund_amount' => $refundAmount,
                'status' => 'Refunded',
            ]);
    
            $order->status = "Refunded";
    
            $order->total_profit -= ($order->total_profit * $refundPercent / 100);
    
            $tranCode = site("madon") . "_" . time() . rand(1000, 9999);
            Transaction::create([
                "user_id" => $order->user_id,
                "tran_code" => $tranCode,
                "type" => "refund",
                "action" => "add",
                "first_balance" => $refundAmount,
                "before_balance" => $order->user->balance,
                "after_balance" => $order->user->balance + $refundAmount,
                "note" => "Hoàn tiền đơn hàng #" . $order->order_code,
                "ip" => request()->ip(),
                "domain" => $order->domain,
            ]);
    
            $order->user->balance += $refundAmount;
            $order->user->save();

            if (site("discord_webhook_url")) {
                $discord_notify = new DiscordSdk();
                $discord_notify->botNotify()->sendMessage([
                    "text" => "Đơn hàng **#{$order->order_code}** đã được hoàn tiền với số lượng **{$returnedQuantity}** tương ứng **" . number_format($refundAmount) . "đ cho người dùng [{$order->user->id}] {$order->user->username}**",
                ]);
            }

            $order->save();

            $refundedOrderIds[] = $order->id;
            Cache::put($cacheKey, $refundedOrderIds, 120); 
        }
    
        return response()->json([
            "code" => "200",
            "status" => "success",
            "message" => "Hoàn tiền cho tất cả các đơn hàng thành công!",
        ], 200);
    }    

    public function CronJobUpdateOrderStatusSMMPartner(Request $request)
    { 
    // Lấy trước nhiều đơn, sau đó xử lý giới hạn sau
    $orders = Order::query()
        ->where('domain', getDomain())
        ->whereNotIn('orderProviderName', ['dontay', 'tuongtaccheo', 'traodoisub'])
        
        ->orderBy('id', 'desc')
        ->limit(100)
        ->get();
    if ($orders->isEmpty()) {
        return response()->json([
            "code" => 200,
            "message" => "Không còn đơn hàng nào cần cập nhật.",
            "status" => "NO_ORDERS",
        ]);
    }
    // Nhóm đơn theo orderProviderName
    $ordersGrouped = $orders->groupBy('orderProviderName');
    $smmList = Smm::where("domain", env("APP_MAIN_SITE"))
        ->where('status', 'active')
        ->get()
        ->keyBy('name');
    $i = 0;
    $limit = 100;
    foreach ($ordersGrouped as $providerName => $groupedOrders) {
        if ($i >= $limit) break;
        if (!$smmList->has($providerName)) {
            continue;
        }
        $smmConfig = $smmList[$providerName];
        $path = $smmConfig["name"];
        $token = $smmConfig["token"];
        // Giới hạn số đơn xử lý trong nhóm nếu vượt quá tổng limit
        $remaining = $limit - $i;
        $groupedOrders = $groupedOrders->take(99);
        $orderIds = $groupedOrders->pluck('order_id')->implode(',');
        $post = [
            "key" => $token,
            "action" => "status",
            "orders" => $orderIds.",",
        ];
        $result = curl_smm($path, $post);

        foreach ($groupedOrders as $order) {
            $key = $order->order_id;
            if (!isset($result[$key]) || isset($result[$key]['error'])) {
                continue;
            }
            $item = $result[$key];
            $start_count = $item['start_count'];
            $remains = $item['remains'];
            $status = $item['status'];
            $orderData = json_decode($order->order_data);
            $sv = ServiceServer::where("id", $order->server_id)
                ->where("service_id", $order->service_id)
                ->first();
            $quantity = isset($sv->percents) ? ($orderData->quantity * $sv->percents) / 100 : $orderData->quantity;
            $remains = max(0, $quantity - $remains);
            switch ($status) {
                case "In progress":
                    $order->status = "Running";
                    break;
                case "Completed":
                    $order->status = "Completed";
                    $remains = $quantity;
                    break;
                case "Preparing":
                    $order->status = "Pending";
                    break;
                case "Canceled":
                    $order->status = "PendingRefundCancel";
                    $order->error = $item["note"] ?? '';
                    break;
                case "Partial":
                    $order->status = "PendingRefundPartial";
                    $order->error = $item["note"] ?? '';
                    break;
                default:
                    $order->status = 'Running';
                    break;
            }
            $order->start = $start_count;
            $order->buff = $remains;
            $order->save();
             
            $i++;
             
        }
    }

}


    public function CronJobUpdateOrderStatusTraodoisub(Request $request)
    {
        $orders = Order::where('status', '!=', 'Completed')
            ->where('status', '!=', 'Cancelled')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Failed')
            ->where('status', '!=', 'Partial')
            ->where('status', '!=', 'Success')
            ->where('status', '!=', 'PendingRefundPartial')
             ->where('status', '!=', 'PendingRefundCancel')
            ->where('status', '!=', 'Partially Refunded')
            ->where('status', '!=', 'Partially Completed')
            ->orderBy('id', 'desc')->limit(100)->get();
        foreach ($orders as $order) {
            $orderId = $order['order_id'];
            
            if ($order['orderProviderName'] == 'traodoisub') {
            $tds = new TraodoisubController();
          
                $tds->path = $order['orderProviderPath'];
                $result = $tds->order($order['order_id']);
                
                if (isset($result['status']) && $result['status'] == true) {
                    if (isset($result['data']['data'][0])) {
                        $data = $result["data"]["data"][0];
                        
                            if(isset($data['code']))
                            {
                                $code_id = $data['code'];
                            }
                            else if (isset($data['order_code'])){
                                $code_id=$data['order_code'];
                            }
                            else{
                                $code_id=-1;
                            }
                            
                            
                         
                        if ($code_id == $order['order_id']) {
                            // print_r($code_id);

                               
                            $status =  $data['status'];
           
                            if (isset($data['start'])) {
                               
                                $start = $data['start'];
                            }
                            else{
                                $start = 0;
                            }
                            
                            $buff = $data['datang'] ?? '0';
                            $order = Order::where('order_id',$code_id)->first();
                            if ($order) {
                                // print_r($code_id."__");
                                switch ($status) {
                                    case 'running':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Running';
                                        break;
                                    case 'Đang chạy':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Running';
                                        break;
                                    case 'completed':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Completed';
                                        break;
                                    case 'Report':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Failed';
                                        break;
                                    case '<span class="badge badge badge-soft-warning">Tạm dừng</span>':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Holding';
                                        break;
                                    case 'Error':
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Failed';
                                        break;
                                    case 'Refund': 
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Refunded';
    
                                        break;
                                    default:
                                        $order->start = $start;
                                        $order->buff = $buff;
                                        $order->status = 'Running';
                                        break;
                                }
                                $order->last_update = now();
                                $order->save();
                                $this->syncOrderStatusToSubsites($order);
                            }
                        }
                        
                    }
                }
            }
        }

        return response()->json([
            'code' => 200,
            'message' => 'Cập nhật trạng thái đơn hàng thành công.',
            'status' => 'SUCCESS',
        ]);
    }
    
    public function CronJobUpdateOrderStatusTuongtaccheo(Request $request)
    {
        $orders = Order::where('status', '!=', 'Completed')
            ->where('status', '!=', 'Cancelled')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Failed')
            ->where('status', '!=', 'Partial')
            ->where('status', '!=', 'Success')
            ->where('status', '!=', 'PendingRefundPartial')
             ->where('status', '!=', 'PendingRefundCancel')
            ->where('status', '!=', 'Partially Refunded')
            ->where('status', '!=', 'Partially Completed')
            ->orderBy('id', 'desc')->limit(100)->get();
        foreach ($orders as $order) {
            $orderId = $order['order_id'];
            
            if ($order['orderProviderName'] == 'tuongtaccheo') {
                
                $tds = new TuongtaccheoController();
              
                $tds->path = $order['orderProviderPath'];
                $result = $tds->order($order['order_code']);
    
                if (isset($result['status']) && $result['status'] == true) {
                    if (isset($result['data'])) {
                        foreach ($result['data'] as $data) {
                            if (isset($data['maghinho'])  && $data['maghinho'] == $order['order_code']) {
                                $code_order = $data['maghinho'];
                               
                                if (isset($data['goc'])) {
                                   
                                    $start = $data['goc'];
                                }
                                else{
                                    $start = 0;
                                }
                                if($data['dalen']- $data['sldat'] >= 0){
                                    $status = 'Success';
                                }
                                else{
                                    $status = 'Running';
                                }
                                $item = json_decode($order->order_data);
                                $buff =$item->quantity- ($data['sldat']- $data['dalen']);
                                $order = Order::where('order_code', $code_order)->first();
                                if ($order) {
                                    $order->last_update = now();
                                    $order->start = $start;
                                    $order->buff = $buff;
                                    $order->status = $status;
                                    $order->save();
                                    $this->syncOrderStatusToSubsites($order);
                                }
                            }
                        }
                    }
                }
            }
        }

        return response()->json([
            'code' => 200,
            'message' => 'Cập nhật trạng thái đơn hàng thành công.',
            'status' => 'SUCCESS',
        ]);
    }

    public function CronJobUpdateOrderStatusTanglikecheo(Request $request) {
        $orders = Order::where('status', '!=', 'Completed')
            ->where('status', '!=', 'Cancelled')
            ->where('status', '!=', 'Refunded')
            ->where('status', '!=', 'Failed')
            ->where('status', '!=', 'Partial')
            ->where('status', '!=', 'Success')
            ->where('status', '!=', 'PendingRefundPartial')
            ->where('status', '!=', 'PendingRefundCancel')
            ->where('status', '!=', 'Partially Refunded')
            ->where('status', '!=', 'Partially Completed')
            ->where('orderProviderName', 'tanglikecheo')
            ->orderBy('id', 'desc')
            ->limit(100)->get();
        
        foreach ($orders as $order) {
            
            if ($order) {
                $orderId =
                //var_dump($orderId);
                $tanglikecheo = new TanglikecheoController();
                $server = ServiceServer::where('id', $order->server_id)->first();
                $response = $tanglikecheo->OrderHistory($orderId, $server->providerKey, $order->orderProviderPath);
                $result = json_decode($response->getContent(), true);
                //var_dump($response);

                if (isset($result['status']) && $result['status'] == 200) {
                    if (isset($result['data'])) {
                        foreach ($result['data'] as $data) {
                            if (isset($data['_id']) && $data['_id'] == $orderId) {
                                $code_order = $data['_id'];
                                
                                $start = $data['start'] ?? 0;
                                $buff = $data['count_is_run'] ?? 0;
                                
                                $status = 'Running';
                                if (isset($data['count_is_run'], $data['quantity']) && $data['count_is_run'] >= $data['quantity']) {
                                    $status = 'Completed';
                                }
                                
                                $order = Order::where('order_id', $code_order)->first();
                                //Log::info($order);
                                if ($order) {
                                    $order->last_update = now();
                                    $order->start = $start;
                                    $order->buff = $buff;
                                    $order->status = $status;
                                    $order->save();
                                    $this->syncOrderStatusToSubsites($order);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return response()->json([
            'code' => 200,
            'message' => 'Cập nhật trạng thái đơn hàng thành công.',
            'status' => 'SUCCESS',
        ]);
    }   
    
    private function syncOrderStatusToSubsites($parentOrder)
    {
        try {
            // Tạo cache key để tránh duplicate processing
            $cacheKey = "sync_order_status_{$parentOrder->id}";

            // Kiểm tra xem có đang xử lý không (lock trong 30 giây)
            if (Cache::has($cacheKey)) {
                Log::info("Đồng bộ đơn hàng #{$parentOrder->id} đang được xử lý bởi process khác");
                return false;
            }

            // Đặt lock
            Cache::put($cacheKey, true, 30);

            // Bắt đầu database transaction
            DB::beginTransaction();

            $subOrders = Order::where('order_id', $parentOrder->id)
                ->where('orderProviderName', env('APP_MAIN_SITE'))
                ->lockForUpdate() // Thêm row-level lock
                ->get();

            $syncedCount = 0;
            foreach ($subOrders as $subOrder) {
                // Kiểm tra xem có thay đổi thực sự không
                $hasChanges = false;

                if ($subOrder->start != $parentOrder->start) {
                    $subOrder->start = $parentOrder->start;
                    $hasChanges = true;
                }

                if ($subOrder->buff != $parentOrder->buff) {
                    $subOrder->buff = $parentOrder->buff;
                    $hasChanges = true;
                }

                if ($subOrder->status != $parentOrder->status) {
                    $subOrder->status = $parentOrder->status;
                    $hasChanges = true;
                }

                if (($parentOrder->status == 'Completed' || $parentOrder->status == 'Success') &&
                    ($subOrder->status != 'Completed' && $subOrder->status != 'Success') &&
                    !$subOrder->completed_at) {
                    $subOrder->completed_at = now();
                    $hasChanges = true;
                }

                if ($hasChanges) {
                    $subOrder->last_update = now();
                    $subOrder->save();
                    $syncedCount++;

                    Log::info("Đồng bộ đơn hàng con thành công", [
                        'parent_order_id' => $parentOrder->id,
                        'sub_order_id' => $subOrder->id,
                        'sub_order_code' => $subOrder->order_code,
                        'new_status' => $parentOrder->status,
                        'new_start' => $parentOrder->start,
                        'new_buff' => $parentOrder->buff
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            // Xóa cache lock
            Cache::forget($cacheKey);

            Log::info("Hoàn thành đồng bộ đơn hàng #{$parentOrder->id}", [
                'synced_count' => $syncedCount,
                'total_sub_orders' => $subOrders->count()
            ]);

            return true;
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollback();

            // Xóa cache lock
            Cache::forget($cacheKey ?? "sync_order_status_{$parentOrder->id}");

            Log::error("Lỗi đồng bộ đơn hàng #{$parentOrder->id} đến các trang con", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    public function cronService(Request $request)
    {
        return $this->CronJobUpdateOrderStatusSMMPartner($request);
    }

    public function cronJobStatusService2mxh(Request $request)
    {
        // Implement 2mxh status check logic here
        return response()->json([
            'code' => 200,
            'message' => 'Cập nhật trạng thái đơn hàng 2mxh thành công.',
            'status' => 'SUCCESS',
        ]);
    }

    public function updateOrdersStatusSmmPartners(Request $request)
    {
        // API endpoint để cập nhật trạng thái đơn hàng từ SMM Partners
        return $this->CronJobUpdateOrderStatusSMMPartner($request);
    }

    public function updateOrdersStatusTds(Request $request)
    {
        // API endpoint để cập nhật trạng thái đơn hàng từ Traodoisub
        return $this->CronJobUpdateOrderStatusTraodoisub($request);
    }
}



