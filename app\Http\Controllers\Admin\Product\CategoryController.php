<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    public function categories()
    {
        return view('admin.product.categories');
    }

    public function categoryCreate()
    {
        return view('admin.product.category-create');
    }

    public function categoryStore(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'name' => 'required',
            'description' => 'required',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            'slug' => 'required',
            'order' => 'required|integer',
            'status' => 'required|in:active,inactive',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $slug = Str::slug($request->name, '-');

        $check = ProductCategory::where('slug', $slug)->where('domain', $request->getHost())->first();

        if ($check) {
            return redirect()->back()->with('error', 'Đường dẫn đã tồn tại')->withInput();
        }

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '-.' . $image->extension();
            $image->move(public_path('uploads/category'), $imageName);
        }

        $category = ProductCategory::create([
            'name' => $request->name,
            'description' => $request->description,
            'image' => "/uploads/category/$imageName",
            'order' => $request->order ?? 0,
            'slug' => $slug,
            'status' => $request->status,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'domain' => $request->getHost(),
        ]);

        return redirect()->route('admin.products.categories')->with('success', 'Thêm danh mục thành công');
    }

    public function categoryEdit($id)
    {
        $category = ProductCategory::find($id);

        if (!$category) {
            return redirect()->route('admin.products.categories')->with('error', 'Danh mục không tồn tại');
        }

        return view('admin.product.category-edit', compact('category'));
    }

    public function categoryUpdate(Request $request, $id)
    {
        $category = ProductCategory::find($id);

        if (!$category) {
            return redirect()->route('admin.products.categories')->with('error', 'Danh mục không tồn tại');
        }

        $valid = Validator::make($request->all(), [
            'name' => 'required',
            'description' => 'required',
            'image' => 'image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            'slug' => 'required',
            'order' => 'required|integer',
            'status' => 'required|in:active,inactive',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $slug = Str::slug($request->name, '-');

        $check = ProductCategory::where('slug', $slug)->where('domain', $request->getHost())->where('id', '!=', $id)->first();

        if ($check) {
            return redirect()->back()->with('error', 'Đường dẫn đã tồn tại')->withInput();
        }

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '-.' . $image->extension();
            $image->move(public_path('uploads/category'), $imageName);

            if (file_exists(public_path($category->image))) {
                unlink(public_path($category->image));
            }
        }

        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'image' => $request->hasFile('image') ? "/uploads/category/$imageName" : $category->image,
            'order' => $request->order ?? 0,
            'slug' => $slug,
            'status' => $request->status,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'domain' => $request->getHost(),
        ]);

        return redirect()->route('admin.products.categories')->with('success', 'Cập nhật danh mục thành công');
    }
}
