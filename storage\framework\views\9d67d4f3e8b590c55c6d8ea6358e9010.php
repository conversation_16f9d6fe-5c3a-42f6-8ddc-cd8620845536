<?php $__env->startSection('title', 'Quản lí smm'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-xxl-4 col-xl-4 col-lg-6 col-md-6 col-sm-6">
        <div class="card custom-card">
            <div class="card-body">
                <div class="d-flex align-items-top">
                    <div class="me-3">
                        <span class="avatar avatar-md p-2 bg-secondary">
                            <svg class="fs-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                                    <path d="M3 10V8a2 2 0 0 1 2-2h2m-4 4c1.333 0 4-.8 4-4m-4 4v4m18-4V8a2 2 0 0 0-2-2h-2m4 4c-1.333 0-4-.8-4-4m4 4v4M7 6h10m4 8v2a2 2 0 0 1-2 2h-2m4-4c-1.333 0-4 .8-4 4m0 0H7m-4-4v2a2 2 0 0 0 2 2h2m-4-4c1.333 0 4 .8 4 4"></path>
                                    <circle cx="12" cy="12" r="2"></circle>
                                </g>
                            </svg>
                        </span>
                    </div>
                    <div class="flex-fill">
                        <div class="d-flex mb-1 align-items-top justify-content-between">
                            <h5 class="fw-semibold mb-0 lh-1">
                                <?php echo e(number_format($totalBalance)); ?> VNĐ                 
                            </h5>
                        </div>
                        <p class="mb-0 fs-10 op-7 text-muted fw-semibold">TỔNG SỐ DƯ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-4 col-xl-4 col-lg-6 col-md-6 col-sm-6">
        <div class="card custom-card">
            <div class="card-body">
                <div class="d-flex align-items-top">
                    <div class="me-3">
                        <span class="avatar avatar-md p-2 bg-success">
                        <i class="ti ti-access-point fs-2"></i>
                        </span>
                    </div>
                    <div class="flex-fill">
                        <div class="d-flex mb-1 align-items-top justify-content-between">
                            <h5 class="fw-semibold mb-0 lh-1">
                             <?php echo e($totalActive); ?>                            
                            </h5>
                        </div>
                        <p class="mb-0 fs-10 op-7 text-muted fw-semibold">ĐANG HOẠT ĐỘNG</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-4 col-xl-4 col-lg-6 col-md-6 col-sm-6">
        <div class="card custom-card">
            <div class="card-body">
                <div class="d-flex align-items-top">
                    <div class="me-3">
                        <span class="avatar avatar-md p-2 bg-info">
                        <i class="ti ti-lock fs-2"></i>
                        </span>
                    </div>
                    <div class="flex-fill">
                        <div class="d-flex mb-1 align-items-top justify-content-between">
                            <h5 class="fw-semibold mb-0 lh-1">
                                <?php echo e($totalInactive); ?>

                            </h5>
                        </div>
                        <p class="mb-0 fs-10 op-7 text-muted fw-semibold">KHÔNG HOẠT ĐỘNG</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12 mb-3">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Danh sách đối tác API</h5>
            </div>
            <div class="card-body">
                <div class="form-group d-flex justify-content-between align-items-center gap-2 mb-3">
                    <a href="<?php echo e(route('admin.service.smm.balance')); ?>" class="btn btn-primary-gradient">
                        <svg class="fs-2" xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24"><path fill="currentColor" d="M4 20v-2h2.75l-.4-.35q-1.225-1.225-1.787-2.662T4 12.05q0-2.775 1.663-4.937T10 4.25v2.1Q8.2 7 7.1 8.563T6 12.05q0 1.125.425 2.188T7.75 16.2l.25.25V14h2v6zm10-.25v-2.1q1.8-.65 2.9-2.212T18 11.95q0-1.125-.425-2.187T16.25 7.8L16 7.55V10h-2V4h6v2h-2.75l.4.35q1.225 1.225 1.788 2.663T20 11.95q0 2.775-1.662 4.938T14 19.75"/></svg> 
                        Đồng bộ
                    </a>
                    <button class="btn btn-danger-gradient" data-bs-toggle="modal" data-bs-target="#new">
                        <svg xmlns="http://www.w3.org/2000/svg" data-bs-toggle="tooltip" title="Thêm mới"  width="20px" height="20px" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M20 14h-6v6h-4v-6H4v-4h6V4h4v6h6z"/>
                        </svg>
                    </button>
                </div>
                <div class="table-responsive">
                    <table id="data-table" class="table table-hover table-vcenter text-center mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Thao tác</th>
                                <th>Thứ tự</th>
                                <th>Link smm</th>
                                <th>Token</th>
                                <th>Số dư</th>
                                <th>Trạng thái</th>
                                <th>Thời gian</th>
                            </tr>
                        </thead>
                        <tbody class="fw-bold">
                            <?php $__currentLoopData = $smms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $smm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($smm->id); ?></td>
                                <td>
                                    <a href="<?php echo e(route('admin.service.smm.edit', ['id' => $smm->id])); ?>"
                                        class="btn btn-sm btn-success-gradient"
                                        data-bs-toggle="tooltip" title="Xem chi tiết">
                                    <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.service.smm.delete', ['id' => $smm->id])); ?>"
                                        class="btn btn-sm btn-danger-gradient"
                                        data-bs-toggle="tooltip" title="Xóa">
                                    <i class="ti ti-trash"></i>
                                    </a>
                                </td>
                           <td>
    <span class="badge bg-primary-gradient"><?php echo e($smm->order); ?></span>
</td>
<td>
    <a href="<?php echo e($smm->name); ?>" target="_blank"><?php echo e($smm->name); ?></a>
</td>
<td>
    
    <?php echo e(mb_substr($smm->token, 0, 20) . (strlen($smm->token) > 20 ? '...' : '')); ?>

</td>
<td>
    
    <?php echo e(number_format($smm->balance)); ?> VNĐ
</td>
<td>
    <?php if($smm->status == 'active'): ?>
        <span class="badge bg-success-gradient">Hoạt Động</span>
    <?php else: ?>
        <span class="badge bg-danger-gradient">Không Hoạt Động</span>
    <?php endif; ?>
</td>

                                <td><?php echo e($smm->created_at); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Api V1</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.website.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-6 ">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="account_tds" id="account_tds" value="<?php echo e(siteValue('account_tds')); ?>">
                                <label>Account Tradoisub</label>
                            </div>
                        </div>
                        <div class="col-md-6 ">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="password_tds" id="password_tds" value="<?php echo e(siteValue('password_tds')); ?>">
                                <label>Password Tradoisub</label>
                            </div>
                            
                        </div>
                        <div class="col-md-12 ">
                            <div class="form-floating mb-3">
                                <input class="form-control" name="api_token_taphoammo" id="api_token_taphoammo" value="<?php echo e(siteValue('api_token_taphoammo')); ?>">
                                <label>Token Taphoammo</label>
                            </div>
                            
                        </div>
                        
                    </div>
                    <div>
                        <button type="submit" class="btn btn-primary">Lưu cấu hình</button>
                    </div>
                                            
                </form>
            </div>
        </div>
    </div>
</div>
<div class="card-body pc-component">
    <div id="new" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="new" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="new">Thêm mới API</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form  id="smm-create" action="<?php echo e(route('admin.service.smm.create')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" name="name"
                                placeholder="Đường link smm" value="<?php echo e(old('name')); ?>">
                            <label for="name">Link APIv2 SMM</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" name="token"
                                placeholder="Token của nguồn smm" value="<?php echo e(old('token')); ?>">
                            <label for="token">API Token</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" name="tigia"
                                placeholder="Nhập tỉ giá" value="26000">
                            <label for="tigia">Tỉ giá 1 USD = ?VNĐ</label>
                        </div>
                        <div class="form-group">
                            <button type="submit" id="submit-btn" class="btn btn-primary col-12">
                            <i class="fas fa-save"></i> Thêm mới
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script>
    $(document).ready(function() {
        $('#smm-create').on('submit', function(e) {
            e.preventDefault();
    
            var formData = new FormData(this);
    
            $('#submit-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
    
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    Swal.fire({
                        title: 'Thành công',
                        text: response.message || 'Thêm mới thành công!',
                        icon: 'success',
                        showCancelButton: true,
                        confirmButtonText: 'Thêm mới',
                        cancelButtonText: 'Đóng',
                        customClass: {
                            confirmButton: 'swal2-confirm btn btn-success',
                            cancelButton: 'swal2-cancel btn btn-danger'
                        },
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $('#smm-create')[0].reset();
                            $('#submit-btn').prop('disabled', false).html('<i class="fas fa-save"></i> Thêm mới');
                        } else {
                            $('#submit-btn').prop('disabled', false).html('<i class="fas fa-save"></i> Thêm mới');
                            location.reload();  
                        }
                    });
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Có lỗi xảy ra, vui lòng thử lại!',
                        icon: 'error',
                        confirmButtonText: 'Đóng',
                        customClass: {
                            confirmButton: 'swal2-confirm btn btn-danger'
                        }
                    });
    
                    $('#submit-btn').prop('disabled', false).html('<i class="fas fa-save"></i> Thêm mới');
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/muasub.online/resources/views/admin/service/smm.blade.php ENDPATH**/ ?>