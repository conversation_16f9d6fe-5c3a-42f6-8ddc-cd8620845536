@extends('guard.layouts.app')
@section('title', 'Đặt hàng số lượng lớn')
@section('content')
<div class="row">
   <div class="col-md-12">
      <div class="card">
         <div class="card-header">
            <h4>Đặt đơn số lượng lớn</h4>
         </div>
         <div class="card-body p-4">
            <form action="{{ route('api.v2.create.order') }}" method="POST" lamtilo-request="order">
                
               <div class="mb-3">
                  <label class="form-label" data-lang="One order per line in format">Nhập mỗi đơn hàng là một dòng theo định dạng bên dưới</label>
                  <textarea class="form-control txa-bulk-order" rows="15" name="massorder" placeholder="Server_id | Link | Quantity
ID máy chủ | Link | Số lượng cần tăng
VD: 232 |{{site('facebook')}}|10000">
</textarea>
               </div>
               <div class="mb-0">
                  <button type="submit" class="btn btn-primary w-100" >Đặt hàng</button>
               </div>
            </form>
         </div>
      </div>
   </div>
</div>
@endsection
@section('script')
<script>$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
$('form[lamtilo-request="order"]').on('submit', function (e) {
    e.preventDefault();
    const form = $(this);
    const url = form.attr('action');
    const method = form.attr('method');
    const data = form.serialize();
    const btn = form.find('button[type="submit"]');
    const btnText = btn.html();
            $.ajax({
                url: url,
                method: method,
                headers: {
                    'X-ACCESS-TOKEN': $('meta[name="access-token"]').attr('content')
                },
                data: data,
                dataType: 'json',
                beforeSend: function () {
                    btn.html('<i class="fas fa-spinner fa-spin"></i>&ensp;Đang xử lý').attr('disabled', true);
                },
                success: function (response) {
                    if (response.code === '200' && response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: "Thông báo",
                            text: response.message,
                             
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: 'btn btn-lamtilo_success btn-success'  // Thêm class Bootstrap cho nút confirm
                            }
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        swal(response.message, 'error');
                    }
                },
                error: function (xhr) {
                    swal(xhr.responseJSON.message, 'error');
                    // Swal.fire({
                    //     icon: 'error',
                    //     title: "Thông báo",
                    //     text: xhr.responseJSON.message,
                    //     confirmButtonText: "Đồng ý !",
                    // });
                },
                complete: function () {
                    btn.html(btnText).attr('disabled', false);
                }
            

        
    })

}); 
</script>
@endsection