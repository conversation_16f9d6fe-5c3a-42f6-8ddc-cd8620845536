<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('config_sites', function (Blueprint $table) {
            $table->string('smtp_host')->nullable()->after('domain');
            $table->string('smtp_port')->nullable()->after('smtp_host');
            $table->string('smtp_user')->nullable()->after('smtp_port');
            $table->string('smtp_pass')->nullable()->after('smtp_user');
            $table->string('smtp_name')->nullable()->after('smtp_pass');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('config_sites', function (Blueprint $table) {
            $table->dropColumn(['smtp_host', 'smtp_port', 'smtp_user', 'smtp_pass', 'smtp_name']);
        });
    }
};
