<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderProduct extends Model
{
    use HasFactory;

    protected $table = 'order_products';

    protected $fillable = [
        'user_id',
        'product_id',
        'quantity',
        'status',
        'refund_status',
        'order_id',
        'price',
        'rate',
        'payment',
        'data',
        'note',
        'email',
        'phone',
        'type',
        'providerToken',
        'domain',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('email', 'like', '%' . $search . '%')
            ->orWhere('phone', 'like', '%' . $search . '%')
            ->orWhere('note', 'like', '%' . $search . '%')
            ->orWhere('data', 'like', '%' . $search . '%');
    }

    /**
     * L<PERSON>y tên user, hiển thị "Người dùng đã bị xóa" nếu user không tồn tại
     */
    public function getUsernameAttribute()
    {
        return $this->user ? $this->user->username : 'Người dùng đã bị xóa';
    }

    /**
     * Lấy tên sản phẩm, hiển thị "Sản phẩm đã bị xóa" nếu sản phẩm không tồn tại
     */
    public function getProductNameAttribute()
    {
        return $this->product ? $this->product->name : 'Sản phẩm đã bị xóa';
    }

    /**
     * Kiểm tra xem có dữ liệu sản phẩm hay không
     */
    public function hasProductData()
    {
        return !empty($this->data);
    }

    /**
     * Lấy trạng thái dữ liệu sản phẩm
     */
    public function getDataStatusAttribute()
    {
        return $this->hasProductData() ? 'Có dữ liệu' : 'Đã xóa dữ liệu';
    }

    /**
     * Lấy dữ liệu gốc từ note backup cho admin
     */
    public function getAdminDataAttribute()
    {
        // Nếu user chưa xóa dữ liệu, trả về data hiện tại
        if (!empty($this->data)) {
            return $this->data;
        }

        // Tìm dữ liệu backup trong note
        if (preg_match('/ADMIN_DATA_BACKUP: ([A-Za-z0-9+\/=]+)/', $this->note ?? '', $matches)) {
            return base64_decode($matches[1]);
        }

        return '';
    }

    /**
     * Kiểm tra xem user đã xóa dữ liệu chưa
     */
    public function isDataDeletedByUser()
    {
        return empty($this->data) && str_contains($this->note ?? '', 'Dữ liệu đã được xóa bởi user');
    }

    /**
     * Lấy dữ liệu hiển thị cho user (có thể bị xóa)
     */
    public function getUserDataAttribute()
    {
        return $this->data;
    }

    /**
     * Lấy dữ liệu hiển thị cho admin (luôn có dữ liệu gốc)
     */
    public function getAdminViewDataAttribute()
    {
        return $this->admin_data ?: $this->data;
    }

    /**
     * Lấy trạng thái chi tiết cho admin
     */
    public function getAdminDataStatusAttribute()
    {
        if ($this->isDataDeletedByUser()) {
            // Tìm thời gian xóa trong note
            if (preg_match('/Dữ liệu đã được xóa bởi user lúc ([^]]+)\]/', $this->note ?? '', $matches)) {
                return 'Đã bị user xóa lúc ' . $matches[1];
            }
            return 'Đã bị user xóa';
        }

        return $this->hasProductData() ? 'Có dữ liệu' : 'Không có dữ liệu';
    }

}
