<?php $__env->startSection('title', 'Trang thống kê'); ?>
<?php $__env->startSection('content'); ?>
<div>
    <button type="button" class="btn btn-outline-primary status-btn mb-3 active" data-tab="revenues"><i class="fad fa-chart-line me-2"></i><PERSON><PERSON><PERSON>hu <PERSON></button>
    <button type="button" class="btn btn-outline-primary status-btn mb-3" data-tab="products"><i class="fad fa-shopping-bag me-2"></i><PERSON><PERSON><PERSON> <PERSON>hu <PERSON>ản Phẩm</button>
    <button type="button" class="btn btn-outline-primary status-btn mb-3" data-tab="orders"><i class="fad fa-box me-2"></i>Bảng Trạng Thái Đơn Hàng </button>
    <button type="button" class="btn btn-outline-primary status-btn mb-3" data-tab="recharges"><i class="fad fa-landmark me-2"></i> Th<PERSON>ng <PERSON>ê Nạp T<PERSON>ề<PERSON></button>
    <button type="button" class="btn btn-outline-primary status-btn mb-3" data-tab="users"><i class="fad fa-users me-2"></i>Thống Kê Người Dùng</button>
</div>
<div>
    <div class="duy-dash" id="revenues">
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Doanh Thu Hôm Nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRenvenueToday)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Doanh Thu Tuần Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRenvenueWeek)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Doanh Thu Tháng <?php echo e(now()->format('m')); ?></span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRenvenueMonth)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-sack-dollar"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Doanh Thu</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRevenue)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-chart-line fs-6 me-1"></i> THỐNG KÊ TỔNG DOANH THU NĂM <?php echo e(now()->format('Y')); ?>

                        </h5>
                    </div>
                    <div id="total-revenue"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="duy-dash" id="products" style="display: none">
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Lãi Sản Phẩm Hôm Nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productProfitToday)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Lãi Sản Phẩm Tuần Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productProfitWeek)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Lãi Sản Phẩm Tháng Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productProfitMonth)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Lãi Sản Phẩm</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productProfitTotal)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn Hàng SP Hôm Nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productOrdersToday)); ?></span>
                        <span class="text-muted">đơn</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn Hàng SP Tuần Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productOrdersWeek)); ?></span>
                        <span class="text-muted">đơn</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn Hàng SP Tháng Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($productOrdersMonth)); ?></span>
                        <span class="text-muted">đơn</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Đơn Hàng SP</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalProductOrders)); ?></span>
                        <span class="text-muted">đơn</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-shopping-bag fs-6 me-1"></i> THỐNG KÊ LÃI SẢN PHẨM NĂM <?php echo e(now()->format('Y')); ?>

                        </h5>
                    </div>
                    <div id="product-revenue"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="duy-dash" id="orders" style="display: none">
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fad fa-shopping-cart"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Đơn Hàng</span>
                        <span class="fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalOrder)); ?> </span>
                        <span class="text-muted">Đơn Hàng</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fad fa-bug"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn hàng hôm nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($OrderToday)); ?></span>
                        <span class="text-muted">Đơn Hàng</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fad fa-spinner"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn hàng tuần nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($OrderWeek)); ?></span>
                        <span class="text-muted">Đơn Hàng</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fad fa-brush"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đơn hàng tháng <?php echo e(now()->format('m')); ?></span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($OrderMonth)); ?></span>
                        <span class="text-muted">Đơn Hàng</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <?php
        $statusList = [
        'Processing' => ['label' => 'Đang Xử Lý', 'icon' => 'fad fa-dna'],
        'Running' => ['label' => 'Đang Tiến Hành', 'icon' => 'fad fa-running'],
        'Completed' => ['label' => 'Đã Hoàn Thành', 'icon' => 'fad fa-thumbs-up'],
        'Partially Completed' => ['label' => 'Hoàn Thành Một Phần', 'icon' => 'fad fa-tasks'],
        'Pending' => ['label' => 'Chờ Xử Lý', 'icon' => 'fad fa-hourglass-half'],
        'Holding' => ['label' => 'Tạm Dừng', 'icon' => 'fad fa-pause-circle'],
        'Cancelled' => ['label' => 'Đã Huỷ Bỏ', 'icon' => 'fad fa-ban'],
        'Failed' => ['label' => 'Thất Bại', 'icon' => 'fad fa-times-circle'],
        'Expired' => ['label' => 'Đã Hết Hạn', 'icon' => 'fad fa-calendar-times'],
        'Refunded' => ['label' => 'Đã Hoàn Tiền', 'icon' => 'fad fa-undo-alt'],
        'Partially Refunded' => ['label' => 'Hoàn Một Phần', 'icon' => 'fad fa-exchange-alt'],
        'Partial' => ['label' => 'Hoàn Một Phần', 'icon' => 'fad fa-exchange-alt'],
        'PendingRefundCancel' => ['label' => 'Chờ Xử Lý Hoàn', 'icon' => 'fad fa-redo-alt'],
        'PendingRefundPartial' => ['label' => 'Chờ Hoàn Một Phần', 'icon' => 'fad fa-redo-alt'],
        'WaitingForRefund' => ['label' => 'Chờ Hoàn Tiền', 'icon' => 'fad fa-clock'],
        'Success' => ['label' => 'Thành Công', 'icon' => 'fad fa-check-circle'],
        ];
        ?>
        <div class="row">
            <?php $__currentLoopData = $statusList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="<?php echo e($info['icon']); ?>"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase"><?php echo e($info['label']); ?></span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e($countStatusOrder[$status] ?? 0); ?></span>
                        <span class="text-muted">Đơn Hàng</span>
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">THỐNG KÊ TRẠNG THÁI ĐƠN HÀNG NĂM <?php echo e(now()->format('Y')); ?></h5>
                    </div>
                    <div id="order-statistics"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">THỐNG KÊ ĐẶT HÀNG NĂM <?php echo e(now()->format('Y')); ?></h5>
                    </div>
                    <div id="total-orders"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="duy-dash" id="recharges" style="display: none">
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-day"></i> 
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Nạp tiền hôm nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRechargeToday)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Nạp tiền tuần này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRechargeWeek)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-alt"></i> 
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Nạp tiền tháng <?php echo e(now()->format('m')); ?></span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRechargeMonth)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng nạp</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRecharge)); ?></span>
                        <span class="text-muted">đ</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-landmark fs-6 me-1"></i> THỐNG KÊ NẠP TIỀN NĂM <?php echo e(now()->format('Y')); ?>

                        </h5>
                    </div>
                    <div id="recharge-statistics"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-crown fs-6 me-1"></i> TOP 
                            <select id="topRechargeSelect" 
                                style="border: none; background: none; outline: none" 
                                class="fw-bold"
                                onchange="window.location.href='<?php echo e(url()->current()); ?>?top=' + this.value">
                            <option value="5" <?php echo e(request('top') == 5 ? 'selected' : ''); ?>>5</option>
                            <option value="10" <?php echo e(request('top') == 10 ? 'selected' : ''); ?>>10</option>
                            <option value="20" <?php echo e(request('top') == 20 ? 'selected' : ''); ?>>20</option>
                            <option value="30" <?php echo e(request('top') == 30 ? 'selected' : ''); ?>>30</option>
                            <option value="50" <?php echo e(request('top') == 50 ? 'selected' : ''); ?>>50</option>
                            <option value="100" <?php echo e(request('top') == 100 ? 'selected' : ''); ?>>100</option>
                            </select>
                            NẠP TIỀN THÁNG <?php echo e(now()->format('m')); ?>

                        </h5>
                    </div>
                    <div class="card-body pt-0">
                        <?php $__currentLoopData = $topRechargeUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="top-content mb-2 mt-1">
                            <div class="d-flex align-items-center w-100">
                                <div class="flex-grow-1 d-flex align-items-center">
                                    <div>
                                        <img class="rounded-circle me-2" width="43" height="43"
                                            src="https://ui-avatars.com/api/?background=random&name=<?php echo e(urlencode($user->user->name)); ?>"
                                            alt="<?php echo e($user->user->name); ?>">
                                    </div>
                                    <div class="d-flex flex-column">
                                        <div class="text-gray-800 text-hover-primary mb-1 ls-1 fww-bolder"><?php echo e($user->user->name); ?></div>
                                        <span class="f-w-600">[<?php echo e($user->user->id); ?>] <?php echo e($user->user->username); ?></span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bolder fs-6"><?php echo e(number_format($user->total_recharge, 0, ',', '.')); ?>đ</div>
                                    <div class="f-w-600 text-muted fs-6">Số Dư  : <?php echo e(number_format($user->user->balance, 0, ',', '.')); ?>đ</div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="duy-dash" id="users" style="display: none">
        <div class="row justify-content-center">
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Thành Viên Hôm Nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalUserToday)); ?></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Thành Viên Tuần Này</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalUserWeek)); ?></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Thành Viên Tháng <?php echo e(now()->format('m')); ?></span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalUserMonth)); ?></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Thành Viên</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalUser)); ?></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Tổng Số Dư</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalBalance)); ?></span>
                        <span>đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đã Tiêu</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(number_format($totalRecharge - $totalBalance)); ?></span>
                        <span>đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Hoạt Động Hôm Nay</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(\App\Models\User::where('domain', request()->getHost())->whereDate('updated_at', today())->count()); ?></span>
                        <span>đ</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="statistic-content">
                    <div class="statistic-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="ps-3">
                        <span class="d-block text-muted fs-14 pb-1 text-uppercase">Đã Xác Thực 2FA</span>
                        <span class="count fw-bold fs-17">
                        <span class="counter"><?php echo e(\App\Models\User::where('domain', request()->getHost())->where('two_factor_auth', 'yes')->count()); ?></span>
                        <span>đ</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-users fs-6 me-1"></i> THỐNG KÊ THÀNH VIÊN NĂM <?php echo e(now()->format('Y')); ?>

                        </h5>
                    </div>
                    <div id="user-statistics"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card custom-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fad fa-crown fs-6 me-1"></i> CẤP BẬC THÀNH VIÊN
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <canvas id="levelPieChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script>
    $(document).ready(function() {
        function showTab(tabId, button) {
            $('.duy-dash').hide();
            $('#' + tabId).show();
            $('.status-btn').removeClass('active');
            $(button).addClass('active');
    
            localStorage.setItem('activeDashboardTab', tabId);
        }
    
        $('.status-btn').click(function() {
            const tabId = $(this).data('tab');
            showTab(tabId, this);
        });
    
        const savedTab = localStorage.getItem('activeDashboardTab') || $('.status-btn').first().data('tab');
        const savedButton = $('.status-btn[data-tab="' + savedTab + '"]');
        
        showTab(savedTab, savedButton);
    });
</script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/app/js/plugins/apexcharts.min.js"></script>
<script>
    'use strict';
    setTimeout(function () {
      (function () {
        var options_order_statistics = {
          chart: {
            height: 350,
            type: 'area'
          },
          dataLabels: {
            enabled: false
          },
          stroke: {
            curve: 'smooth'
          },
          colors: [
            '#F4A6A6', '#04A9F5', '#4B0082', '#FF8C00', '#F44236', '#28a745'
          ],
     series: [
  {
    name: 'Đang Chờ',
    data: <?php echo json_encode($totalOrderStatus['totalPending'], 15, 512) ?>
  },
  {
    name: 'Đang Chạy',
    data: <?php echo json_encode($totalOrderStatus['totalProcessing'], 15, 512) ?>
  },
  {
    name: 'Đã Huỷ',
    data: <?php echo json_encode($totalOrderStatus['totalCanceled'], 15, 512) ?>
  },
  {
    name: 'Chờ Hoàn Tiền',
    data: <?php echo json_encode($totalOrderStatus['totalPendingRefundCancel'], 15, 512) ?>
  },
  {
    name: 'Hoàn Tiền',
    data: <?php echo json_encode($totalOrderStatus['totalRefund'], 15, 512) ?>
  },
  {
    name: 'Hoàn Thành',
    data: <?php echo json_encode($totalOrderStatus['totalCompleted'], 15, 512) ?>
  }
],
          xaxis: {
            categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
          },
          tooltip: {
            shared: true,
            intersect: false
          }
        };
    
        var chart_order_statistics = new ApexCharts(document.querySelector('#order-statistics'), options_order_statistics);
        chart_order_statistics.render();
      })();
    }, 700);
</script>
<script>
    'use strict';
    setTimeout(function () {
      (function () {
        var options_total_orders = {
          chart: {
            height: 350,
            type: 'area'
          },
          dataLabels: {
            enabled: false
          },
          stroke: {
            curve: 'smooth'
          },
          colors: [
            '#4B0082'
          ],
          series: [
            {
              name: 'Thống Kê Đơn Hàng',
              data: <?php echo json_encode($totalOrderStatus['totalOrder'], 15, 512) ?>
            },
          ],
          xaxis: {
            categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
          },
          tooltip: {
            shared: true,
            intersect: false
          }
        };
    
        var chart_total_orders = new ApexCharts(document.querySelector('#total-orders'), options_total_orders);
        chart_total_orders.render();
      })();
    }, 700);
</script>
<script>
    'use strict';
    setTimeout(function () {
      (function () {
    var options = {
      chart: {
        height: 350,
        type: 'bar', 
      },
      dataLabels: {
        enabled: true
      },
      colors: ['#04A9F5'], 
      series: [{
        name: 'Người dùng',
        data: <?php echo json_encode($data['user'], 15, 512) ?> 
      }],
      xaxis: {
        categories: <?php echo json_encode($labels, 15, 512) ?>,
      },
      tooltip: {
        shared: true,
        intersect: false
      }
    };
    
      var chart = new ApexCharts(document.querySelector("#user-statistics"), options);
      chart.render();
    })();
    }, 700);
</script>
<script>
    'use strict';
    setTimeout(function () {
      (function () {
        var options_order_statistics = {
          chart: {
            height: 350,
            type: 'area'
          },
          dataLabels: {
            enabled: true
          },
          stroke: {
            curve: 'smooth'
          },
          colors: ['#04A9F5'],
          series: [{
            name: 'Nạp tiền',
            data: <?php echo json_encode($data['recharge'], 15, 512) ?>
          }],
          xaxis: {
            categories: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'],
          },
          yaxis: {
            labels: {
              formatter: function (value) {
                return value.toLocaleString('vi-VN'); 
              }
            }
          },
          tooltip: {
            y: {
              formatter: function (value) {
                return value.toLocaleString('vi-VN');
              }
            },
            shared: true,
            intersect: false
          }
        };
    
        var chart_order_statistics = new ApexCharts(document.querySelector('#recharge-statistics'), options_order_statistics);
        chart_order_statistics.render();
      })();
    }, 700);
</script>
<script>
    const levelData = <?php echo json_encode($levelUsersCount, 15, 512) ?>;
    
    const levelMap = {
        member: 'Thành viên',
        collaborator: 'Cộng tác viên',
        agency: 'Đại lý',
        distributor: 'Nhà phân phối',
    };
    
    const labels = Object.keys(levelData).map(key => levelMap[key] || key);
    const data = Object.values(levelData);
    
    const colors = [
        { border: '#f94144', fill: 'rgba(249, 65, 68, 0.6)' },
        { border: '#f3722c', fill: 'rgba(243, 114, 44, 0.6)' },
        { border: '#f8961e', fill: 'rgba(248, 150, 30, 0.6)' },
        { border: '#f9844a', fill: 'rgba(249, 132, 74, 0.6)' }
    ];
    
    const backgroundColor = colors.map(c => c.fill);
    const borderColor = colors.map(c => c.border);
    
    const ctx = document.getElementById('levelPieChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    });
</script>
<script>    
    'use strict';
    setTimeout(function () {
        (function () {
            var options_total_revenue = {
                chart: {
                    height: 350,
                    type: 'area'
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth'
                },
                colors: ['red'],
                series: [
                    {
                        name: 'Tổng Doanh Thu (Dịch vụ + Sản phẩm)',
                        data: <?php echo json_encode($totalOrderStatus['totalRevenue'], 15, 512) ?>
                    },
                ],
                xaxis: {
                    categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return value.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value.toLocaleString('vi-VN');
                        }
                    }
                }
            };
        
            var chart_total_revenue = new ApexCharts(document.querySelector('#total-revenue'), options_total_revenue);
            chart_total_revenue.render();
        })();

        // Biểu đồ doanh thu sản phẩm
        (function () {
            // Tính toán doanh thu sản phẩm theo tháng
            var productRevenueData = [];
            <?php
                for ($month = 1; $month <= 12; $month++) {
                    $productRevenue = (int) DB::table('order_products')
                        ->where('domain', request()->getHost())
                        ->where('status', 'success')
                        ->whereYear('created_at', now()->year)
                        ->whereMonth('created_at', $month)
                        ->selectRaw('SUM(payment - (rate * quantity)) as profit')
                        ->value('profit') ?? 0;
                    echo "productRevenueData.push($productRevenue);\n";
                }
            ?>

            var options_product_revenue = {
                chart: {
                    height: 350,
                    type: 'area'
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth'
                },
                colors: ['#28a745'],
                series: [
                    {
                        name: 'Lãi Sản Phẩm',
                        data: productRevenueData
                    },
                ],
                xaxis: {
                    categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return value.toLocaleString('vi-VN') + ' đ';
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value.toLocaleString('vi-VN');
                        }
                    }
                }
            };

            var chart_product_revenue = new ApexCharts(document.querySelector('#product-revenue'), options_product_revenue);
            chart_product_revenue.render();
        })();
    }, 700);
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/muasub.online/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>