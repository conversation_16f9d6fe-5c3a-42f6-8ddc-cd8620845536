<?php

namespace App\Listeners;

use App\Mail\LoginNotification;
use App\Models\EmailTemplate;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendLoginNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        $user = $event->user;

        // Kiểm tra cấu hình SMTP có đầy đủ không
        if (!$this->isSmtpConfigured()) {
            return;
        }

        // Lấy template email
        $domain = request()->getHost();
        $template = EmailTemplate::getOrCreateDefault($domain, EmailTemplate::TYPE_LOGIN_NOTIFICATION);

        if (!$template || !$template->isEnabled()) {
            return; // Không gửi email nếu template bị tắt
        }

        // D<PERSON> liệu để thay thế placeholder
        $data = [
            '{domain}' => $domain,
            '{title}' => siteValue('name_site') ?? $domain,
            '{username}' => $user->name,
            '{ip}' => request()->ip(),
            '{device}' => $this->getDeviceInfo(),
            '{time}' => now()->format('d/m/Y H:i:s')
        ];

        // Thay thế placeholders
        $emailContent = $template->replacePlaceholders($data);

        // Gửi email
        Mail::send([], [], function ($message) use ($user, $emailContent) {
            $message->to($user->email)
                ->subject($emailContent['subject'])
                ->html($emailContent['content'])
                ->from(siteValue('smtp_user'), siteValue('smtp_name'));
        });
    }

    /**
     * Kiểm tra cấu hình SMTP
     */
    protected function isSmtpConfigured()
    {
        return siteValue('smtp_host') &&
               siteValue('smtp_port') &&
               siteValue('smtp_user') &&
               siteValue('smtp_pass') &&
               siteValue('smtp_name');
    }

    /**
     * Lấy thông tin thiết bị
     */
    protected function getDeviceInfo()
    {
        $userAgent = request()->header('User-Agent');

        if (strpos($userAgent, 'Mobile') !== false) {
            return 'Mobile Device';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            return 'Tablet';
        } else {
            return 'Desktop Computer';
        }
    }
}
