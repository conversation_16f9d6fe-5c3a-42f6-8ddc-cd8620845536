@extends('guard.layouts.app')
@section('title', '<PERSON>ấ<PERSON> & <PERSON>')
@section('content')
<div class="row">
    <div class="col-md-12 mb-3">
    <div class="row gy-2">
      <div class="col-xxl-4 col-sm-6 mt-2">
        <div class="card">
          <div class="card-body bg-soft-blue text-center rounded-top">
            <i class="icofont-bird-wings d-inline-block mt-2 mb-3 display-4 text-blue"></i>
          </div>
          <div class="card-body mt-n52">
            <div class="text-center">
              <div class="py-2 px-3 shadow-sm d-inline-block rounded-pill card-bg">
                <h4 class="d-inline-block fw-bold mb-0">{{ number_format(site('collaborator')) }} ₫</h4>
              </div>
              <h6 class="pt-3 pb-2 m-0 fs-18 fw-medium"><PERSON><PERSON><PERSON> tác viên</h6>
              <ul class="list-unstyled pricing-content text-center pt-2 border-0 mb-3">
                <li>1 lệnh nạp {{ number_format(site('collaborator')) }} ₫ hoặc tổng nạp đạt {{ number_format(site('collaborator')) }} ₫ sẽ được tăng cấp tự động.</li>
                <li>Được tạo website con.</li>
                <li>Được áp dụng giá cấp độ: Cộng tác viên.</li>
              </ul>
              
            </div>
          </div>
        </div>
      </div>
      <div class="col-xxl-4 col-sm-6 mt-2">
        <div class="card">
          <div class="card-body bg-soft-success text-center rounded-top">
            <i class="icofont-elk d-inline-block mt-2 mb-3 display-4 text-success"></i>
          </div>
          <div class="card-body mt-n52">
            <div class="text-center">
              <div class="py-2 px-3 shadow-sm d-inline-block rounded-pill card-bg">
                <h4 class="d-inline-block fw-bold mb-0">{{ number_format(site('agency')) }} ₫</h4>
              </div>
              <h6 class="pt-3 pb-2 m-0 fs-18 fw-medium">Đại lý</h6>
              <ul class="list-unstyled pricing-content text-center pt-2 border-0 mb-3">
                <li>1 lệnh nạp {{ number_format(site('agency')) }} ₫ hoặc tổng nạp đạt {{ number_format(site('agency')) }} ₫ sẽ được tăng cấp tự động.</li>
                <li>Được tạo website con.</li>
                <li>Được áp dụng giá cấp độ: Đại lý.</li>
              </ul>
              
            </div>
          </div>
        </div>

      </div>
      <div class="col-xxl-4 col-sm-6 mt-2">
        <div class="card">
          <div class="card-body bg-soft-warning text-center rounded-top">
            <i class="icofont-fire-burn d-inline-block mt-2 mb-3 display-4 text-warning"></i>
          </div>
          <div class="card-body mt-n52">
            <div class="text-center">
              <div class="py-2 px-3 shadow-sm d-inline-block rounded-pill card-bg">
                <h4 class="d-inline-block fw-bold mb-0">{{ number_format(site('distributor')) }} ₫</h4>
              </div>
              <h6 class="pt-3 pb-2 m-0 fs-18 fw-medium">Nhà phân phối</h6>
              <ul class="list-unstyled pricing-content text-center pt-2 border-0 mb-3">
                <li>1 lệnh nạp {{ number_format(site('distributor')) }} ₫ hoặc tổng nạp đạt {{ number_format(site('distributor')) }} ₫ sẽ được tăng cấp tự động.</li>
                <li>Được tạo website con.</li>
                <li>Được áp dụng giá cấp độ: Nhà phân phối.</li>
                <li>Được bổ sung các tính năng theo yêu cầu.</li>
              </ul>
              
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
     
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Bảng Giá Dịch Vụ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <ul class="nav nav-pills row" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                            @foreach ($platforms as $platform)
                            <li class="col-md-2 text-center">
                                <a class="nav-link {{ $loop->first ? 'active' : '' }} d-flex align-items-center gap-1 justify-content-center"
                                    id="v-pills-{{ $platform->id }}-tab" data-bs-toggle="pill"
                                    href="#v-pills-{{ $platform->id }}" role="tab"
                                    aria-controls="v-pills-{{ $platform->id }}" aria-selected="true">
                                <img src="{{ $platform->image }}" class="wid-25" alt="">
                                <span>{{ $platform->name }}</span>
                                </a>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="col-md-12">
                        <div class="tab-content" id="v-pills-tabContent">
                            @foreach ($platforms as $platform)
                            <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}"
                                id="v-pills-{{ $platform->id }}" role="tabpanel"
                                aria-labelledby="v-pills-{{ $platform->id }}-tab">
                                {{-- accordition service --}}
                                <div class="accordion" id="accordionExample">
                                    @foreach ($platform->services as $service)
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading{{ $service->id }}">
                                            <button class="accordion-button collapsed font-bold" type="button"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#collapse{{ $service->id }}"
                                                aria-expanded="false"
                                                aria-controls="collapse{{ $service->id }}">
                                                {{ $service->name }}
                                            </button>
                                        </h2>
                                        <div id="collapse{{ $service->id }}"
                                            class="accordion-collapse collapse"
                                            aria-labelledby="heading{{ $service->id }}"
                                            data-bs-parent="#accordionExample">
                                            <div class="accordion-body">
                                                <div class="table-responsive">
                                                    <table
                                                        class="table table-bordered table-hover table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>Thông tin</th>
                                                                <th>Máy chủ</th>
                                                                <th>Thành viên</th>
                                                                <th>Cộng tác viên</th>
                                                                <th>Đại lý</th>
                                                                <th>Nhà phân phối</th>
                                                                <th>Trạng thái</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach ($service->servers->where('domain', request()->getHost()) as $server)
                                                            <tr></tr>
                                                            <td>
                                                                <ul class="mb-0">
                                                                    <li class="fs-6 fw-bold">Thông tin:
                                                                        {{ $server->name }}
                                                                    </li>
                                                                    <li class="fs-6 fw-bold">Min - Max:
                                                                        {{ number_format($server->min) }} ~
                                                                        {{ number_format($server->max) }}
                                                                    </li>
                                                                    <li class="fs-6 fw-bold">Giới hạn ngày:
                                                                        <span class="text-danger">
                                                                        @if ($server->limit_day == 0)
                                                                        Không giới hạn
                                                                        @else
                                                                        {{ $server->limit_day }}
                                                                        @endif
                                                                        </span>
                                                                    </li>
                                                                </ul>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="badge bg-primary">{{ $server->package_id }}</span>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="badge bg-success">{{ $server->price_member }}đ</span>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="badge bg-info">{{ $server->price_collaborator }}đ</span>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="badge bg-warning">{{ $server->price_agency }}đ</span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-danger">
                                                                Liên hệ Admin
                                                                </span>
                                                            </td>
                                                            <td>
                                                                {!! statusAction($server->status, true) !!}
                                                            </td>
                                                            </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection