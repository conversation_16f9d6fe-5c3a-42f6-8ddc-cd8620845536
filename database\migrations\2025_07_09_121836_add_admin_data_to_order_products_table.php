<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_products', function (Blueprint $table) {
            // Thêm field admin_data để lưu trữ dữ liệu gốc cho admin
            $table->longText('admin_data')->nullable()->after('data')->comment('Dữ liệu gốc chỉ admin xem được');

            // Thêm field user_deleted_at để ghi lại thời điểm user xóa dữ liệu
            $table->timestamp('user_deleted_at')->nullable()->after('admin_data')->comment('Thời điểm user xóa dữ liệu');
        });

        // Copy dữ liệu hiện tại từ field data sang admin_data
        DB::statement('UPDATE order_products SET admin_data = data WHERE data IS NOT NULL AND data != ""');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_products', function (Blueprint $table) {
            $table->dropColumn(['admin_data', 'user_deleted_at']);
        });
    }
};
