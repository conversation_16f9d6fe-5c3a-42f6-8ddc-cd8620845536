@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON><PERSON> Sử Giao Dịch')

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card">
                <div class="card-header">
                    <h5 class="card-title">L<PERSON><PERSON> Sử Giao Dịch</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="data-table" class="table table-hover table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Ngày Giao Dịch</th>
                                    <th>Loại Giao Dịch</th>
                                    <th>Mã Giao Dịch</th>
                                    <th><PERSON><PERSON> Dư Trước</th>
                                    <th>Số Tiền</th>
                                    <th>Số Dư Sau</th>
                                    <th>Ghi Chú</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($transactions as $key => $transaction)
                                    <tr>
                                        <td>#{{ $key + 1 }}</td>
                                        <td>{{ $transaction->created_at }}</td>
                                        <td>
                                            @if ($transaction->type == 'recharge')
                                                <span class="badge bg-success">Nạp Tiền</span>
                                            @elseif ($transaction->type == 'order')
                                                <span class="badge bg-primary">Đơn Hàng</span>
                                            @elseif($transaction->type == 'balance')
                                                <span class="badge bg-info">Thay Đổi</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $transaction->tran_code }}</span>
                                        </td>
                                        <td class="fw-bold text-muted">{{ number_format($transaction->before_balance) }} VNĐ
                                        </td>
                                        <td class="fw-bold text-muted">
                                            @if ($transaction->action == 'add')
                                                <p class="mb-0 text-success">
                                                    +{{ number_format($transaction->first_balance) }} VNĐ</p>
                                            @elseif ($transaction->action == 'sub')
                                                <p class="mb-0 text-danger">
                                                    -{{ number_format($transaction->first_balance) }} VNĐ</p>
                                            @endif
                                        </td>
                                        <td class="fw-bold text-muted">{{ number_format($transaction->after_balance) }} VNĐ
                                        </td>
                                        <td>{{ $transaction->note }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
