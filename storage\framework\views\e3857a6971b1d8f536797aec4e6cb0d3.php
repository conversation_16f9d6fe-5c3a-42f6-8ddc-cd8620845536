<?php $__env->startSection('title', 'Quản lí sản phẩm'); ?>
<?php $__env->startSection('content'); ?>
    <div class="card custom-card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title">Danh sách sản phẩm</h4>
            
                            <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary">Thêm sản phẩm</a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <div class="table-repsonsive">
                <table class="table w-100" id="table-products-main">
                    <thead>
                        <tr>
                            <th>#</th>
                            
                            <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                                <th>T<PERSON> tác</th>
                            <?php endif; ?>
                            <th>Tên sản phẩm</th>
                            <th>Trạng thái</th>
                            <th>Đã bán</th>
                            <th>Lượt xem</th>
                            <th>Thời gian</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        $(document).ready(function() {
            loadDatatable('#table-products-main', 'products-main', [{
                    data: 'id',
                    name: 'id'
                },
                
                            <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>

                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return `
                            <a href="<?php echo e(url('admin/products')); ?>/edit/${row.id}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-danger btn-delete" onclick="deleteData(${row.id}, 'products-main')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                        }
                    },
                <?php endif; ?> {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        return data == 'active' ? '<span class="badge bg-success">Hiển thị</span>' :
                            '<span class="badge bg-danger">Ẩn</span>';
                    }
                },
                {
                    data: 'sold',
                    name: 'sold',
                    render: function(data) {
                        return formatNumber(data);
                    }
                },
                {
                    data: 'view',
                    name: 'view',
                    render: function(data) {
                        return formatNumber(data);
                    }
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY HH:mm:ss');
                    }
                }
            ])
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/product/products.blade.php ENDPATH**/ ?>