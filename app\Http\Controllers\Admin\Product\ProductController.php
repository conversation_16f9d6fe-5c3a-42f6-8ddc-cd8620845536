<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use App\Models\ProductMain;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    public function products()
    {
        return view('admin.product.products');
    }

    public function create()
    {
        return view('admin.product.product-create');
    }

    public function store(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'name' => 'required',
            'description' => 'required',
            'image' => 'required',
            'slug' => 'required',
            'order' => 'required',
            'status' => 'required|in:active,inactive',
            'is_email' => 'required|in:1,0',
            'is_phone' => 'required|in:1,0',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        // $category = ProductCategory::where('id', $request->category_id)->where('domain', $request->getHost())->first();
        // if (!$category) {
        //     return redirect()->back()->with('error', 'Danh mục không tồn tại')->withInput();
        // }

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/products'), $fileName);
        }

        $slug = Str::slug($request->slug, '-');

        $check = ProductMain::where('slug', $slug)->where('domain', $request->getHost())->first();
        if ($check) {
            return redirect()->back()->with('error', 'Slug đã tồn tại')->withInput();
        }

        $product = ProductMain::create([
            'name' => $request->name,
            'description' => $request->description,
            'image' => '/uploads/products/' . $fileName,
            'order' => $request->order,
            'slug' => $slug,
            'is_email' => $request->is_email,
            'is_phone' => $request->is_phone,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'status' => $request->status,
            'domain' => $request->getHost(),
        ]);

        return redirect()->route('admin.products')->with('success', 'Thêm sản phẩm thành công');
    }

    public function edit($id)
    {
        $product = ProductMain::where('id', $id)->where('domain', request()->getHost())->first();
        if (!$product) {
            return redirect()->route('admin.products')->with('error', 'Sản phẩm không tồn tại');
        }

        return view('admin.product.product-edit', compact('product'));
    }

    public function update(Request $request, $id)
    {
        $valid = Validator::make($request->all(), [
            'name' => 'required',
            'description' => 'required',
            'slug' => 'required',
            'order' => 'required',
            'status' => 'required|in:active,inactive',
            'is_email' => 'required|in:1,0',
            'is_phone' => 'required|in:1,0',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }


        $product = ProductMain::where('id', $id)->where('domain', $request->getHost())->first();
        if (!$product) {
            return redirect()->route('admin.products')->with('error', 'Sản phẩm không tồn tại');
        }

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/products'), $fileName);

            if (file_exists(public_path('uploads/products/' . $product->image))) {
                unlink(public_path('uploads/products/' . $product->image));
            }

            $fileName = '/uploads/products/' . $fileName;
        } else {
            $fileName = $product->image;
        }

        $slug = Str::slug($request->slug, '-');

        $check = ProductMain::where('slug', $slug)->where('domain', $request->getHost())->where('id', '!=', $id)->first();
        if ($check) {
            return redirect()->back()->with('error', 'Slug đã tồn tại')->withInput();
        }

        $product->update([
            'name' => $request->name,
            'description' => $request->description,
            'image' => $fileName,
            'order' => $request->order,
            'slug' => $slug,
            'is_email' => $request->is_email,
            'is_phone' => $request->is_phone,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'status' => $request->status,
        ]);

        return redirect()->route('admin.products')->with('success', 'Cập nhật sản phẩm thành công');
    }


}
