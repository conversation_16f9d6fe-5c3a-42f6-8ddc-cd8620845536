@extends('admin.layouts.app')
@section('title', 'Quản lý sản phẩm con')
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title"><PERSON>h sách sản phẩm con</h4>
                    
                            @if (request()->getHost() === env('APP_MAIN_SITE'))
                        <a href="{{ route('admin.products.child.create') }}" class="btn btn-primary">Thêm mới</a>
                    @else
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-config-server">Cấu hình sản phẩm</button>
                    @endif
                </div>
                <div class="card-body">
                    <div class="table-repsonsive">
                        <table class="table w-100" id="table-products">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    @if (request()->getHost() === env('APP_MAIN_SITE'))
                                    <th>Thao tác</th>
                                    @endif
                                    <th>Tên sản phẩm</th>
                                    <th>Giá</th>
                                    <th>Tồn kho</th>
                                    <th>Trạng thái</th>
                                    <th>Thứ tự</th>
                                    @if (request()->getHost() === env('APP_MAIN_SITE'))
                                    <th>Loại</th>
                                    @endif
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    
                            @if (request()->getHost() !== env('APP_MAIN_SITE'))
        <!-- Modal cấu hình máy chủ -->
        <div class="modal fade" id="modal-config-server" tabindex="-1" aria-labelledby="modal-config-server"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="{{ route('admin.website.update') }}" method="POST">
                        @csrf
                        <div class="modal-header">
                            <h5 class="modal-title" id="modal-config-server">Cấu hình sản phẩm</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                 
                                <div class="col-md-12 form-group mb-3">
                                    <label for="percent_price_product" class="form-label">% tăng giá</label>
                                    <input type="number" class="form-control" name="percent_price_product"
                                        id="percent_price_product" value="{{ site('percent_price_product') }}">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <button type="submit" class="btn btn-primary">Lưu</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endsection
@section('script')
   <script>
        $(document).ready(function() {
            loadDatatable('#table-products', 'products', [{
                    data: 'id',
                    name: 'id'
                },
                    @if (request()->getHost() === env('APP_MAIN_SITE'))
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <a href="{{ url('admin/products/child') }}/edit/${row.id}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-danger" onclick="deleteData(${row.id}, 'products')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                    }
                },
                @endif
                {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'price',
                    name: 'price',
                    render: function(data) {
                        return `<span class="badge bg-primary">${formatCurrency(data)}</span>`;
                    }
                },
                {
                    data: 'inventory',
                    name: 'inventory',
                    render: function(data) {
                        return `<span class="badge bg-primary">${formatNumber(data)}</span>`;
                    }
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        if (data == 'active') {
                            return `<span class="badge bg-success">Hoạt động</span>`;
                        }
                        if (data == 'inactive') {
                            return `<span class="badge bg-danger">Không hoạt động</span>`;
                        }
                    }
                },
                {
                    data: 'order',
                    name: 'order'
                },
                    @if (request()->getHost() === env('APP_MAIN_SITE'))
                {
                    data: 'type',
                    name: 'type',
                    render: function(data) {
                        if (data == 'manual') {
                            return `<span class="badge bg-primary">Thủ công</span>`;
                        }
                        if (data == 'taphoammo') {
                            return `<span class="badge bg-success">Taphoammo.net</span>`;
                        }
                    }
                },
                @endif
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY');
                    }
                },
            ])
        })
    </script>
@endsection
