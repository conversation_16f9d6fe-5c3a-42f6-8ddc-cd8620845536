@extends('admin.layouts.app')
@section('title', '<PERSON><PERSON><PERSON><PERSON>')
@section('content')
<div class="card-body pc-component">
    <div id="config" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="config" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="config">CẤU HÌNH TIẾP THỊ LIÊN KẾT</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('admin.website.update') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-lg col-md-4 col-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="percentage_commission_affiliate"
                                        name="percentage_commission_affiliate" placeholder="Nhập D<PERSON> Liệu"
                                        value="{{ siteValue('percentage_commission_affiliate') }}">
                                    <label for="percentage_commission_affiliate">% Hoa Hồng</label>
                                </div>
                            </div>
                            <div class="col-lg col-md-4 col-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="min_withdraw_ref"
                                        name="min_withdraw_ref" placeholder="Nhập Dữ Liệu"
                                        value="{{ siteValue('min_withdraw_ref') }}">
                                    <label for="min_withdraw_ref">Số Tiền Tối Thiểu</label>
                                </div>
                            </div>                            <div class="col-lg col-md-4 col-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="max_withdraw_ref" name="max_withdraw_ref"
                                        placeholder="Nhập Dữ Liệu" value="{{ siteValue('max_withdraw_ref') }}">
                                    <label for="max_withdraw_ref">Số Tiền Rút Tối Đa</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="telegram_chat_id_withdraw"
                                        name="telegram_chat_id_withdraw" placeholder="Nhập Dữ Liệu"
                                        value="{{ siteValue('telegram_chat_id_withdraw') }}">
                                    <label for="telegram_chat_id_withdraw">Telegram Chat ID Nhận Thông Báo Rút Tiền</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary-gradient w-100">Lưu Cấu Hình</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12 text-right mb-3">
        <button class="btn btn-primary-gradient" data-bs-toggle="modal" data-bs-target="#config">
            <i class="bx bx-cog"></i> Cấu Hình
        </button>
    </div>
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">NGƯỜI DÙNG ĐĂNG KÝ GẦN ĐÂY</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="data-table" class="table table-hover table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Người Đăng Ký</th>
                                <td>Người Giới Thiệu</td>
                                <th>Ngày Tham Gia</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($affiliates as $affiliate)
                            <tr>
                                <td>{{ $affiliate->id }}</td>
                                <td>{{ $affiliate->username }}</td>
                                <td>{{ $affiliate->referrer->username ?? 'Không Có' }}</td>
                                <td>{{ $affiliate->created_at->format('d/m/Y') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">ĐƠN RÚT TIỀN CẦN DUYỆT</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="data-table-2" class="table table-hover table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Người Yêu Cầu</th>
                                <th>Thông Tin</th>
                                <th>Trạng Thái</th>
                                <th>Ngày Tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($withdraws as $withdraw)
                            <tr>
                                <td>{{ $withdraw->id }}</td>
                                <td>{{ $withdraw->user->username }}</td>
                                <td>
                                    <ul>
                                        <li><b>Số Tiền: </b>{{ number_format($withdraw->amount) }} VND</li>
                                        <li><b>STK: </b>{{ $withdraw->account_number }}</li>
                                        <li><b>CTK: </b>{{ $withdraw->account_name }}</li>
                                        <li><b>Ngân Hàng: </b>{{ $withdraw->bank_name }}</li>
                                    </ul>
                                </td>
                                <td>
                                    @if ($withdraw->status == 'pending')
                                    <p><span class="badge bg-warning-gradient">Chờ Xử Lý</span></p>
                                    <p>
                                        <form id="submitwithdrawref" action="{{ route('admin.affiliates.withdraw', $withdraw->id) }}" method="POST" style="display:inline-block;">
                                            @csrf
                                            <div class="form-group">
                                                <input type="hidden" name="status" value="success">
                                                <button type="submit" class="btn btn-sm btn-success-gradient"  data-bs-toggle="tooltip" data-bs-placement="top" aria-label="Duyệt Đơn" data-bs-original-title="Duyệt Đơn"><i class="fas fa-check"></i></button>
                                            </div>
                                        </form>
                                    </p>
                                    @elseif($withdraw->status == 'success')
                                        <span class="badge bg-success-gradient">Thành Công</span>
                                    @endif
                                    
                                </td>
                                <td>{{ $withdraw->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    $('#data-table-2').DataTable();
    $('#jsource-table').DataTable({
        data: dataSet,
    });
</script>
<script>
    document.getElementById('submitwithdrawref').addEventListener('click', function(event) {
        event.preventDefault(); 
        Swal.fire({
            title: 'Xác Nhận Duyệt Đơn Rút?',
            text: "Hành Động Này Không Thể Hoàn Tác!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Duyệt',
            cancelButtonText: 'Hủy',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('submitwithdrawref').submit();
            }
        });
    });
</script>
@endsection