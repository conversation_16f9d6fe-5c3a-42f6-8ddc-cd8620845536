<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductChildController extends Controller
{
    public function child()
    {
        return view('admin.product.child');
    }

    public function create()
    {
        return view('admin.product.child-create');
    }

    public function store(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'product_main_id' => 'required',
            'name' => 'required',
            'price' => 'required',
            'status' => 'required',
            'order' => 'required',
            'type' => 'required',
            'providerToken' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $product = Product::create([
            'product_main_id' => $request->product_main_id,
            'name' => $request->name,
            'price' => $request->price,
            'status' => $request->status,
            'order' => $request->order,
            'type' => $request->type,
            'providerToken' => $request->providerToken,
            'domain' => $request->getHost()
        ]);

        return redirect()->route('admin.products.child')->with('success', 'Tạo sản phẩm con thành công');
    }

    public function edit($id)
    {
        $product = Product::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$product) {
            return redirect()->route('admin.products.child')->with('error', 'Sản phẩm không tồn tại');
        }

        return view('admin.product.child-edit', compact('product'));
    }

    public function update(Request $request, $id)
    {
        $product = Product::where('id', $id)->where('domain', request()->getHost())->first();

        if (!$product) {
            return redirect()->route('admin.products.child')->with('error', 'Sản phẩm không tồn tại');
        }

        $valid = Validator::make($request->all(), [
            'product_main_id' => 'required',
            'name' => 'required',
            'price' => 'required',
            'status' => 'required',
            'order' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $product->update([
            'product_main_id' => $request->product_main_id,
            'name' => $request->name,
            'price' => $request->price,
            'status' => $request->status,
            'order' => $request->order,
        ]);

        if (request()->getHost() === env('APP_MAIN_SITE')) {
            $product->update([
                'type' => $request->type,
                'providerToken' => $request->providerToken,
            ]);
        }

        return redirect()->route('admin.products.child')->with('success', 'Cập nhật sản phẩm con thành công');
    }

    public function storeInventory(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'product_id' => 'required',
            'inventory' => 'required',
        ]);

        if ($valid->fails()) {
            return redirect()->back()->with('error', $valid->errors()->first())->withInput();
        }

        $product = Product::where('id', $request->product_id)->where('domain', request()->getHost())->first();

        if (!$product) {
            return redirect()->route('admin.products.child')->with('error', 'Sản phẩm không tồn tại');
        }

        $listInventory = explode(PHP_EOL, $request->inventory);

        $count = 0;
        foreach ($listInventory as $inventory) {
            $inventory = trim($inventory);

            // lọc ra những inventory bị trùng
            $inventoryExist = $product->inventory()->where('data', $inventory)->first();
            if ($inventoryExist) {
                continue;
            }

            if ($inventory) {
                $product->inventory()->create([
                    'data' => $inventory,
                    'domain' => request()->getHost()
                ]);
                $count++;
            }
        }

        return redirect()->route('admin.products.child')->with('success', 'Thêm kho hàng thành công: ' . ($count));
    }
}
