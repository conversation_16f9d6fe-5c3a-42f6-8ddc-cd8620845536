@extends('admin.layouts.app')
@section('title','Cài đặt thông báo Discord')
@section('content')
<div class="row">
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Admin: Discord Settings</h1>
        <div class="ms-md-1 ms-0">

        </div>
    </div>
    <div class="col-sm-12 col-md-12 col-lg-12">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <h4 class="card-title">Notification | Discord</h4>
            </div>
            <div class="card-body">
                <div class="mt-3">
                    <form action="{{ route('admin.website.update') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Cấu hình Discord Webhook</h5>
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="discord_webhook_url"
                                        name="discord_webhook_url" placeholder="Nhập Discord Webhook URL"
                                        value="{{ siteValue('discord_webhook_url') }}">
                                    <label for="discord_webhook_url">Webhook thông báo đơn hàng + nạp tiền</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="discord_webhook_product_url"
                                        name="discord_webhook_product_url" placeholder="Nhập Discord Webhook URL"
                                        value="{{ siteValue('discord_webhook_product_url') }}">
                                    <label for="discord_webhook_product_url">Webhook Thông Báo Đơn Hàng Sản Phẩm </label>
                                </div>
                                @if (getDomain() == env('APP_MAIN_SITE'))
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="discord_webhook_ticket_revenue_url"
                                        name="discord_webhook_ticket_revenue_url" placeholder="Nhập Discord Webhook URL"
                                        value="{{ siteValue('discord_webhook_ticket_revenue_url') }}">
                                    <label for="discord_webhook_ticket_revenue_url">Webhook thông báo ticket + hoa hồng
                                        + doanh thu (cron)</label>
                                </div>
                                @endif
                                <div class="">
                                    <button type="submit" class="btn btn-primary-gradient">Lưu cấu hình</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">Hướng dẫn cấu hình Discord</h5>
                                <ol>
                                    <li>Tạo một Discord server (nếu chưa có)</li>
                                    <li>Tạo một channel để nhận thông báo</li>
                                    <li>Vào Settings của channel > Integrations > Webhooks > New Webhook</li>
                                    <li>Đặt tên cho webhook và copy webhook URL</li>
                                    <li>Dán webhook URL vào ô Webhook URL ở bên trái</li>
                                    <li>Nhấn "Lưu cấu hình" để lưu lại</li>
                                </ol>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection