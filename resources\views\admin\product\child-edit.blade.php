@extends('admin.layouts.app')
@section('title', 'Sửa sản phẩm con')
@section('content')
    <div class="card custom-card shadow mb-3">
        <div class="card-header">
            <h4 class="card-title">Chỉnh sửa sản phẩm con</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.products.child.update', $product->id) }}" method="POST">
                @csrf
                <div class="form-group mb-3">
                    <label for="product_main_id" class="form-label">Sản phẩm chính</label>
                    <select name="product_main_id" id="product_main_id" class="form-control">
                        <option value="">Chọn sản phẩm chính</option>
                        @foreach (\App\Models\ProductMain::where('domain', env('APP_MAIN_SITE'))->where('status', 'active')->orderBy('order', 'asc')->get() as $productMain)
                            <option value="{{ $productMain->id }}"
                                {{ $product->product_main_id == $productMain->id ? 'selected' : '' }}>
                                {{ $productMain->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="name" class="form-label">Tên sản phẩm</label>
                    <input type="text" name="name" id="name" class="form-control" value="{{ $product->name }}">
                </div>
                <div class="form-group mb-3">
                    <label for="price" class="form-label">Giá sản phẩm</label>
                    <input type="text" name="price" id="price" class="form-control" value="{{ $product->price }}">
                </div>
                <div class="row">
                    <div class="col-md-6 form-group mb-3">
                        <label for="status" class="form-label">Trạng thái</label>
                        <select name="status" id="status" class="form-control">
                            <option value="active" {{ $product->status == 'active' ? 'selected' : '' }}>Kích hoạt</option>
                            <option value="inactive" {{ $product->status == 'inactive' ? 'selected' : '' }}>Chưa kích hoạt
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="order" class="form-label">Thứ tự</label>
                        <input type="number" min="0" name="order" id="order" class="form-control"
                            value="{{ $product->order }}">
                    </div>
                    
                            @if (request()->getHost() === env('APP_MAIN_SITE'))
                        <div class="col-md-6 form-group mb-3">
                            <label for="type" class="form-label">Loại</label>
                            <select name="type" id="type" class="form-control">
                                <option value="manual" {{ $product->type == 'manual' ? 'selected' : '' }}>Thủ công</option>
                                <option value="taphoammo" {{ $product->type == 'taphoammo' ? 'selected' : '' }}>API:
                                    Taphoammo
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 form-group mb-">
                            <label for="providerToken" class="form-label">Token sản phẩm (taphoammo)</label>
                            <input type="text" name="providerToken" id="providerToken" class="form-control"
                                value="{{ $product->providerToken }}">
                        </div>
                    @endif
                </div>
                <button type="submit" class="btn btn-primary">Chỉnh sửa</button>
            </form>
        </div>
    </div>

    @if ($product->type == 'manual' && request()->getHost() === env('APP_MAIN_SITE'))
        <div class="card custom-card shadow mb-3">
            <div class="card-header">
                <h4 class="card-title">Thêm dữ liệu kho</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.products.child.inventory.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    <div class="form-group mb-3">
                        <label for="inventory" class="form-label">Dữ liệu</label>
                        <textarea name="inventory" id="inventory" class="form-control" rows="6"
                            placeholder="Mỗi một sản phẩm là 1 dòng VD(UID|TOKEN|PASS|2FA)"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Thêm dữ liệu</button>
                </form>
            </div>
        </div>
        <div class="card custom-card shadow">
            <div class="card-header">
                <h4 class="card-title">Danh sách dữ liệu kho</h4>
            </div>
            <div class="card-body">
                <div class="table-repsonive">
                    <table class="table w-100" id="table-inventory">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Thao tác</th>
                                <th>Dữ liệu</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    @endif
@endsection
@section('script')
    @if ($product->type == 'manual')
        <script>
            $(document).ready(function() {
                loadDatatable('#table-inventory', 'products.child.inventory', [{
                        data: 'id',
                        name: 'id'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return `
                                <button class="btn btn-sm btn-danger" onclick="deleteData(${row.id}, 'products.child.inventory')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            `;
                        }
                    },
                    {
                        data: 'data',
                        name: 'data'
                    },
                    {
                        data: 'status',
                        name: 'status',
                        render: function(data) {
                            if (data == 'available') {
                                return `<span class="badge bg-success">Chưa bán</span>`;
                            }
                            if (data == 'sold') {
                                return `<span class="badge bg-danger">Đã bán</span>`;
                            }
                            if (data == 'pending') {
                                return `<span class="badge bg-warning">Đang chờ</span>`;
                            }
                        }
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        render: function(data) {
                            return formatDate(data);
                        }
                    }
                ], "product_id={{ $product->id }}");
            });
        </script>
    @endif
@endsection
