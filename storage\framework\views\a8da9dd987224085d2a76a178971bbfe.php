<?php $__env->startSection('title', 'Quản lý sản phẩm con'); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Danh sách sản phẩm con</h4>
                    
                            <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                        <a href="<?php echo e(route('admin.products.child.create')); ?>" class="btn btn-primary">Thêm mới</a>
                    <?php else: ?>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-config-server">C<PERSON><PERSON> hình sản phẩm</button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="table-repsonsive">
                        <table class="table w-100" id="table-products">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                                    <th>Thao tác</th>
                                    <?php endif; ?>
                                    <th>Tên sản phẩm</th>
                                    <th>Giá</th>
                                    <th>Tồn kho</th>
                                    <th>Trạng thái</th>
                                    <th>Thứ tự</th>
                                    <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                                    <th>Loại</th>
                                    <?php endif; ?>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    
                            <?php if(request()->getHost() !== env('APP_MAIN_SITE')): ?>
        <!-- Modal cấu hình máy chủ -->
        <div class="modal fade" id="modal-config-server" tabindex="-1" aria-labelledby="modal-config-server"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('admin.website.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title" id="modal-config-server">Cấu hình sản phẩm</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                 
                                <div class="col-md-12 form-group mb-3">
                                    <label for="percent_price_product" class="form-label">% tăng giá</label>
                                    <input type="number" class="form-control" name="percent_price_product"
                                        id="percent_price_product" value="<?php echo e(site('percent_price_product')); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <button type="submit" class="btn btn-primary">Lưu</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
   <script>
        $(document).ready(function() {
            loadDatatable('#table-products', 'products', [{
                    data: 'id',
                    name: 'id'
                },
                    <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <a href="<?php echo e(url('admin/products/child')); ?>/edit/${row.id}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-danger" onclick="deleteData(${row.id}, 'products')">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                    }
                },
                <?php endif; ?>
                {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'price',
                    name: 'price',
                    render: function(data) {
                        return `<span class="badge bg-primary">${formatCurrency(data)}</span>`;
                    }
                },
                {
                    data: 'inventory',
                    name: 'inventory',
                    render: function(data) {
                        return `<span class="badge bg-primary">${formatNumber(data)}</span>`;
                    }
                },
                {
                    data: 'status',
                    name: 'status',
                    render: function(data) {
                        if (data == 'active') {
                            return `<span class="badge bg-success">Hoạt động</span>`;
                        }
                        if (data == 'inactive') {
                            return `<span class="badge bg-danger">Không hoạt động</span>`;
                        }
                    }
                },
                {
                    data: 'order',
                    name: 'order'
                },
                    <?php if(request()->getHost() === env('APP_MAIN_SITE')): ?>
                {
                    data: 'type',
                    name: 'type',
                    render: function(data) {
                        if (data == 'manual') {
                            return `<span class="badge bg-primary">Thủ công</span>`;
                        }
                        if (data == 'taphoammo') {
                            return `<span class="badge bg-success">Taphoammo.net</span>`;
                        }
                    }
                },
                <?php endif; ?>
                {
                    data: 'created_at',
                    name: 'created_at',
                    render: function(data) {
                        return moment(data).format('DD/MM/YYYY');
                    }
                },
            ])
        })
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/product/child.blade.php ENDPATH**/ ?>