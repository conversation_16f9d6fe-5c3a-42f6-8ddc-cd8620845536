<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Library\TelegramSdk;
use App\Models\Recharge;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;
use Carbon\Carbon;

class ServerActionController extends Controller
{
    public function serverAction(Request $request)
    {
        $action = $request->a;
        
        if($action == 'remove-user'){
            $users = User::where('balance', '>', '0')->whereDate('created_at', Carbon::now()->subDays(5))->limit(100)->get();
            $count=0;
            foreach($users as $user){
                $user->delete();
                $count++;
            }
            return response()->json(['status' => true, 'messsage' => "Xoá thành công: ". $count . " tài khoản"]);
        }

    }

   
}
