<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductMain extends Model
{
    use HasFactory;
    
    protected $table = 'product_mains';
    
    protected $fillable = [
        'category_id',
        'name',
        'description',
        'image',
        'order',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'status',
        'sold',
        'view',
        'is_email',
        'is_phone',
        'domain',
    ];
    
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }
    
    public function product()
    {
        $domain = request()->getHost();
        return $this->hasMany(Product::class, 'product_main_id')
                    ->where('domain', $domain);
    }
    public function getPriceStart()
    {
        return $this->product()->where('status', 'active')->orderBy('price')->first()->price ?? 0;
    }

    public function getPriceEnd()
    {
        return $this->product()->where('status', 'active')->orderByDesc('price')->first()->price ?? 0;
    }
    
    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%' . $search . '%');
    }
}