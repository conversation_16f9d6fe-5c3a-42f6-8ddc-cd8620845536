<!DOCTYPE html>
<html lang="{{ str_replace('-', '_', app()->getLocale()) }}">
    <meta http-equiv="content-type" content="text/html;charset=utf-8" />
    <head>
        <title><PERSON><PERSON><PERSON><PERSON></title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="logo" content="{{ site('logo') }}">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <link rel="icon" href="{{ siteValue('favicon') }}" type="image/x-icon">
        <meta name="description" content="{{ siteValue('description') }}">
        <meta name="keywords" content="{{ siteValue('keywords') }}">
        <meta name="author" content="{{ siteValue('author') }}">
        <meta property="og:title" content="{{ siteValue('title') }}">
        <meta property="og:description" content="{{ siteValue('description') }}">
        <meta property="og:image" content="{{ siteValue('thumbnail') }}">
        <meta property="og:image:width" content="1200">
        <meta property="og:image:height" content="630">
        <meta property="og:url" content="{{ url()->current() }}">
        <meta property="og:site_name" content="{{ siteValue('title') }}">

<!-- App css -->
    <link href="/theme/css/bootstrap.min.css?v=1.0.2" rel="stylesheet" type="text/css" />
    <link href="/theme/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/theme/css/app.min.css?v=1.0.2" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/2.2.0/css/dataTables.bootstrap5.css" rel="stylesheet" type="text/css" />
    <link href="/theme/css/styles.css?time=1750607681" rel="stylesheet" type="text/css" />

    <script data-host="https://analytics.vsm.vn" data-dnt="false" src="https://analytics.vsm.vn/js/script.js" id="ZwSg9rf6GA" async defer></script>

    <style>
.imgbb-control{position:relative;height:40px;line-height:40px;overflow:hidden;background:#e7e7e7;color:#FFF;border-radius:0.4rem;}
.imgbb-iconSelect{background-position:0 -16px;display:inline-block;background:url(https://i.imgur.com/e9GpBzF.png) no-repeat center center;width:16px;height:16px;float:left;margin:12px}
.imgbb-add{cursor:pointer;position:absolute;width:140px;height:40px;overflow:hidden;background:#27ad60;left:0;top:0;z-index:1}
.imgbb-choose{cursor:pointer;z-index:10;opacity:0;filter:alpha(opacity=0);-moz-opacity:0;font-size:300px;height:1000px;right:0;top:0;position:absolute;cursor:pointer}
.imgbb-list{white-space:nowrap;overflow-x:auto;margin-top:5px}
.imgbb-list .imgbb-i{vertical-align:top;width:100px;height:100px;border:1px solid #eee;display:inline-flex;padding:10px;position:relative;margin:0 10px 10px 0}
.imgbb-list .imgbb-i img{margin:auto;max-width:100%;max-height:100%}
.imgbb-remove{position:absolute;top:0;right:0;width:15px;height:15px;line-height:15px;text-align:center;color:#fff;background:red;cursor:pointer}
.imgbb-loading{position:absolute;right:10px;display:none}
</style>

    </script>
<style>
.startbar{display:none}.startbar:has(.simplebar-wrapper){display:block}
</style>

    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/11.7.12/sweetalert2.min.css">
    {!! site('script_head') !!}

    </head>
<body>
    <div class="auth-bg">
        <div class="container-xxl">
            <div class="row vh-100 d-flex justify-content-center py-2">
                <div class="col-12 align-self-center">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-4 mx-auto">

                                <div class="card">
                                    <div class="card-body p-0 auth-header-box rounded-top">
                                        <div class="text-center p-3">
                                            <a href="/auth/login" class="logo logo-admin">
                                                <img src="{{siteValue('logo')}}" style="width:100%;height:auto" alt="logo"
                                                    class="auth-logo">
                                            </a>
                                            <h4 class="mt-3 mb-1 fw-semibold fs-18">Quên Mật Khẩu</h4>
                                        </div>
                                    </div>
                                    <div class="card-body pt-0">
                                        <div class="card mt-4">
                                            <div class="card-body p-4">
                                                <div class="text-center mt-2">
                                                    <h5 class="text-primary">Bạn quên mật khẩu?</h5>
                                                    <p class="text-muted">Nhập đầy đủ thông tin để đặt lại mật khẩu</p>

                                                    <lord-icon src="https://cdn.lordicon.com/rhvddzym.json" trigger="loop" colors="primary:#0ab39c" class="avatar-xl"></lord-icon>

                                                </div>

                                                @if (session('status'))
                                                    <div class="alert border-0 alert-success text-center mb-2 mx-2" role="alert">
                                                        {{ session('status') }}
                                                    </div>
                                                @else
                                                    <div class="alert border-0 alert-warning text-center mb-2 mx-2" role="alert">
                                                        Nhập email của bạn và hướng dẫn sẽ được gửi đến!
                                                    </div>
                                                @endif

                                                @if ($errors->any())
                                                    <div class="alert border-0 alert-danger text-center mb-2 mx-2" role="alert">
                                                        {{ $errors->first() }}
                                                    </div>
                                                @endif

                                                <div class="p-2">
                                                    <form method="POST" action="{{ route('password.email') }}">
                                                        @csrf
                                                        <div class="mb-4">
                                                            <label class="form-label">Email</label>
                                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                                   name="email" value="{{ old('email') }}"
                                                                   placeholder="Nhập Email" required autocomplete="email" autofocus>
                                                        </div>

                                                        <div class="text-center mt-4">
                                                            <button class="btn btn-success w-100" type="submit">Xác minh</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center mb-2">
                                            <p class="text-muted">
                                                <a href="{{ route('login') }}" class="text-primary">Quay lại đăng nhập</a>
                                            </p>
                                        </div>
                                    </div><!--end card-body-->
                                </div><!--end card-->
                            </div><!--end col-->
                        </div><!--end row-->
                    </div><!--end card-body-->
                </div><!--end col-->
            </div><!--end row-->
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="/theme/js/bootstrap.bundle.min.js"></script>
    <script src="/theme/js/simplebar.min.js"></script>

    <script src="//cdn.datatables.net/2.2.0/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.2.0/js/dataTables.bootstrap5.js"></script>

    <script src="/theme/js/app.js?v=1.0.2"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="/app/js/plugins/dataTables.min.js"></script>
    <script src="/app/js/plugins/dataTables.bootstrap5.min.js"></script>

<script src="/app/js/app.js?duy-time={{ time() }}"></script>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
        {!! site('script_footer') !!}

        @if (session('success'))
        <script>
            Swal.fire({
                title: 'Thành công!',
                text: '{{ session('success') }}',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        </script>
        @endif

        @if (session('error'))
        <script>
            Swal.fire({
                title: 'Lỗi!',
                text: '{{ session('error') }}',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        </script>
        @endif

        <script>
            $(document).ready(function() {
                $('form').on('submit', function() {
                    Swal.fire({
                        title: 'Đang Gửi Email',
                        text: 'Vui Lòng Đợi...',
                        icon: 'info',
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        allowEnterKey: false,
                        width: '360px',
                        customClass: {
                            popup: 'swal2-popup swal2-rounded',
                            title: 'swal2-title fs-5',
                            content: 'swal2-content fs-6'
                        },
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                });
            });
        </script>
    </body>
</html>
