<?php

namespace App\Listeners;

use App\Events\ProductDelivered;
use App\Models\EmailTemplate;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendProductDeliveryNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ProductDelivered $event): void
    {
        $orderProduct = $event->orderProduct;
        $user = $orderProduct->user;

        // Kiểm tra cấu hình SMTP có đầy đủ không
        if (!$this->isSmtpConfigured()) {
            return;
        }

        // Lấy template email
        $domain = $orderProduct->domain;
        $template = EmailTemplate::getOrCreateDefault($domain, EmailTemplate::TYPE_PRODUCT_DELIVERY);

        if (!$template || !$template->isEnabled()) {
            return; // Không gửi email nếu template bị tắt
        }

        // Dữ liệu để thay thế placeholder
        $data = [
            '{domain}' => $domain,
            '{title}' => siteValue('name_site') ?? $domain,
            '{username}' => $user->name,
            '{product_name}' => $orderProduct->product->name ?? 'Sản phẩm',
            '{product_data}' => $orderProduct->data ?? 'Không có dữ liệu',
            '{order_id}' => $orderProduct->order_id ?? $orderProduct->id,
            '{time}' => $orderProduct->updated_at->format('d/m/Y H:i:s')
        ];

        // Thay thế placeholders
        $emailContent = $template->replacePlaceholders($data);

        // Gửi email đến email của user hoặc email trong đơn hàng
        $emailTo = $orderProduct->email ?: $user->email;

        if ($emailTo) {
            Mail::send([], [], function ($message) use ($emailTo, $emailContent) {
                $message->to($emailTo)
                    ->subject($emailContent['subject'])
                    ->html($emailContent['content'])
                    ->from(siteValue('smtp_user'), siteValue('smtp_name'));
            });
        }
    }

    /**
     * Kiểm tra cấu hình SMTP
     */
    protected function isSmtpConfigured()
    {
        return siteValue('smtp_host') && 
               siteValue('smtp_port') && 
               siteValue('smtp_user') && 
               siteValue('smtp_pass') && 
               siteValue('smtp_name');
    }
}
