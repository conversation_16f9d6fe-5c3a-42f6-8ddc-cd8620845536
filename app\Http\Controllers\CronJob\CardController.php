<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use App\Models\Card;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use App\Library\TelegramSdk;
use App\Library\DiscordSdk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CardController extends Controller
{
    public function cronRecharge(Request $request)
    {

        if (isset($request->status)) {
            $status = $request->status;
            $code = $request->code;
            $serial = $request->serial;

            $trans_id = $request->trans_id;

            $callback_sign = $request->callback_sign;

            $cardRecharge = Card::where('tranid', $trans_id)->first();
            if ($cardRecharge) {
                $amount = $cardRecharge->card_amount;
                $card_discount = siteValue('percent');
                $sign = md5(siteValue('partner_key') . $code . $serial);
                if ($sign == $callback_sign) {
                    if ($status == 1 && $amount > 0) {
                        $tiennhan = $amount - ($amount * $card_discount / 100);
                        $user = User::where('username', $cardRecharge->username)->first();
                        if ($user) {

                            $tranCode = siteValue('madon') . rand(100000000, 999999999);

                            Transaction::create([
                                'user_id' => $user->id,
                                'tran_code' => $tranCode,
                                'type' => 'payment',
                                'action' => 'add',
                                'first_balance' => $user->balance,
                                'before_balance' => $tiennhan,
                                'after_balance' => $user->balance + $tiennhan,
                                'note' => $request->message,
                                'ip' => $request->ip(),
                                'domain' => $request->getHost()
                            ]);


                            $user->balance = $user->balance + $tiennhan;
                            $user->total_recharge = $user->total_recharge + $tiennhan;
                            $user->save();


                            $cardRecharge->discount = $card_discount;
                            $cardRecharge->card_real_amount = $tiennhan;
                            $cardRecharge->status = 'Success';
                            $cardRecharge->save();
    

                            // Discord notification
                            if (siteValue('discord_webhook_url')) {
                                try {
                                    $discord_notify = new DiscordSdk();
                                    $discord_notify->botNotify()->sendMessage([
                                        'text' => "🎉 **Thông báo nạp tiền** 🎉\n" .
                                            "👤 **Người nạp:** " . $user->username . "\n" .
                                            "💰 **Số tiền:** " . number_format($amount) . " VNĐ\n" .
                                            "🏦 **Loại:** Thẻ cào " . $code . "\n" .
                                            "📝 **Ghi chú:** " . $request->message . "\n" .
                                            "🔗 **Mã giao dịch:** " . $tranCode . "\n" .
                                            "📅 **Thời gian:** " . Carbon::now()->format('d/m/Y H:i:s') . "\n" .
                                            "🔗 **IP:** " . $request->ip() . "\n" .
                                            "🌐 **Domain:** " . $request->getHost()
                                    ]);
                                } catch (\Exception $e) {
                                    // Log error nếu cần
                                }
                            }

                        }
                    } else {
                        $cardRecharge->status = 'Error';
                        $cardRecharge->save();
                    }
                }
            }
        }

        return response()->json([
            'code' => 200,
            'message' => 'Xử lý callback thẻ cào thành công.',
            'status' => 'SUCCESS',
        ]);
    }

    public function card(Request $request)
    {
        // API endpoint cho thẻ cào - tương tự như cronRecharge nhưng trả về JSON response
        return $this->cronRecharge($request);
    }

}