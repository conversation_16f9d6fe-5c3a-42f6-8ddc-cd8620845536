 
<?php $__env->startSection('content'); ?> 
<?php $__env->startSection('title', 'Trang chủ'); ?> 
<style>
    .bg-corner-img {
    background-image: url('https://socialcare.vn/assets/images/extra/corner.png');
    background-position: right;
    background-repeat: no-repeat;
    background-size: contain;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<style>
.twoline{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden;height:34px;}
.mySwiper{display:none}
.mySwiper.swiper-initialized{display:block;}
.select2-results__option .view-detail{display:none}
.select2-container .select2-selection--single .select2-selection__rendered{white-space:unset;}
@media (min-width:1400px) and (max-width:1690px) {
    .col-status{width:50%;max-width:50%;flex:0 0 50%;}
}
</style>
    
            <div class="row">
                
            </div>
        
        
<div class="row">
    <div class="col-md-3">
                    <div class="card bg-corner-img">
                        <div class="card-body">
                            <div class="row d-flex justify-content-center">
                                <div class="col-9">
                                    <p class="text-muted text-uppercase mb-0 fw-normal fs-13">Số dư hiện tại</p>
                                    <h4 class="mt-1 mb-0 fw-medium text-primary"><?php echo e(number_format(Auth::user()->balance ?? 0)); ?> ₫ </h4>
                                </div>
                                <!--end col-->
                                <div class="col-3 align-self-center">
                                    <div class="d-flex justify-content-center align-items-center thumb-md border-dashed border-primary rounded mx-auto">
                                        <i class="iconoir-dollar-circle fs-22 align-self-center mb-0 text-primary"></i>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </div>
                        <!--end card-body-->
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-corner-img">
                        <div class="card-body">
                            <div class="row d-flex justify-content-center">
                                <div class="col-9">
                                    <p class="text-muted text-uppercase mb-0 fw-normal fs-13">Tổng nạp</p>
                                    <h4 class="mt-1 mb-0 fw-medium text-warning"><?php echo e(number_format(Auth::user()->total_recharge ?? 0)); ?> ₫ </h4>
                                </div>
                                <!--end col-->
                                <div class="col-3 align-self-center">
                                    <div class="d-flex justify-content-center align-items-center thumb-md border-dashed border-warning rounded mx-auto">
                                        <i class="iconoir-dollar-circle fs-22 align-self-center mb-0 text-warning"></i>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </div>
                        <!--end card-body-->
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-corner-img">
                        <div class="card-body">
                            <div class="row d-flex justify-content-center">
                                <div class="col-9">
                                    <p class="text-muted text-uppercase mb-0 fw-normal fs-13">Tổng tiêu</p>
                                    <h4 class="mt-1 mb-0 fw-medium text-info"><?php echo e(number_format((Auth::user()->total_recharge ?? 0) - (Auth::user()->balance ?? 0))); ?> ₫</h4>
                                </div>
                                <!--end col-->
                                <div class="col-3 align-self-center">
                                    <div class="d-flex justify-content-center align-items-center thumb-md border-dashed border-info rounded mx-auto">
                                        <i class="iconoir-dollar-circle fs-22 align-self-center mb-0 text-info"></i>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </div>
                        <!--end card-body-->
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-corner-img">
                        <div class="card-body">
                            <div class="row d-flex justify-content-center">
                                <div class="col-9">
                                    <p class="text-muted text-uppercase mb-0 fw-normal fs-13">Cấp bậc</p>
                                    <h4 class="mt-1 mb-0 fw-medium text-danger"><?php echo e(levelUser(Auth::user()->level ?? 'Thành viên')); ?> </h4>
                                </div>
                                <!--end col-->
                                <div class="col-3 align-self-center">
                                    <div class="d-flex justify-content-center align-items-center thumb-md border-dashed border-danger rounded mx-auto">
                                        <i class="iconoir-hexagon-dice fs-22 align-self-center mb-0 text-danger"></i>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </div>
                        <!--end card-body-->
                    </div>
                </div>
                
        <div class="col-md-12">
                        <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3 pb-2 border-bottom">
                        <h2 class="fs-18 text-start m-0">Sản Phẩm</h2>
                        <a href="/product/categories" title="Sản Phẩm">Xem thêm</a>
                    </div>
                    <div class="swiper mySwiper">
                        <div class="swiper-wrapper">
                            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productMain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="swiper-slide">
                                    <a href="<?php echo e(route('client.product', ['slug' => $productMain->slug])); ?>">
                                        <div class="card shadow card-hover-top overflow-hidden border m-0 bg-light" style="box-shadow:none!important">
                                            <img src="<?php echo e(asset($productMain->image)); ?>" class="card-img-top" alt="<?php echo e($productMain->name); ?>">
                                            <div class="card-body text-center p-2">
                                                <h5 class="card-title fs-14 mb-2 twoline">
                                                                                                        <?php echo e($productMain->name); ?>

                                                                                                    </h5>
                                                <strong class="text-primary fs-12">
                                                                                                            <span class="text-danger"><?php echo e(number_format($productMain->getPriceStart() ?? 0)); ?> ₫
                                                            </span>
                                                                                                    </strong>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                                  
                                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>     
                                                    </div>
                        <!--<div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>-->
                    </div>
                </div>
            </div>
               </div>
                <div class="col-md-7"><?php $__currentLoopData = \App\Models\NoticeSystem::where('domain', request()->getHost())->orderBy('id', 'desc')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $noticeSystem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card shadow mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center gap-1 mb-3">
                                <img src="https://s3.ap-northeast-1.amazonaws.com/h.files/images/1724360847451_TRWUheFQ6o.jpg" class="rounded-full" width="40" height="40" alt="">

                                <div>
                                    <a class="fs-6 fw-bold text-primary d-flex align-items-center gap-1" href="">
                                        <span><?php echo e($noticeSystem->title); ?></span>
                                        <img src="https://socialcare.vn/pack/images/verified.png" alt="" width="17" height="17">
                                    </a>
                                    <div class="feed-time fw-bold text-xs"><?php echo e($noticeSystem->created_at); ?></div>
                                </div>
                            </div>

                            <div class="feed-content text-sm">
                                                           <?php echo $noticeSystem->content; ?>


                                                            </div>

                        </div>
                    </div>
                                        
                    
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        
                        <div class="col-md-5">
                                                    <div class="card shadow mb-3">
                        <div class="card-header">
                            <h4 class="card-title">Hoạt động gần đây</h4>
                        </div>
                        <div class="card-body">
                            <div class="scroll h-350px" style="max-height:350px;overflow-y:auto">
                                <ul class="list-unstyled recent-activity-list">
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        
                                                                        <?php $__currentLoopData = \App\Models\NoticeService::where('domain', request()->getHost())->orderBy('id', 'desc')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $noticeSystem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        
                                                                        <li>
                                        <div class="fw-medium fs-14">
                                                                                   <?php echo $noticeSystem->content; ?>

                                                                                </div> 
                                        <span class="fs-11 activity-time text-muted"><?php echo e($noticeSystem->created_at->format('d/m')); ?>

</span>
                                    </li>
                                    
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </ul>
                            </div>
                        </div>
                    </div>        
                                         
                </div>
           
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Thống Kê Đơn Hàng</h5>
            </div>
            <div class="card-body">
                <div id="order-statistics"></div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade modal-animate" id="notiModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center mb-3">
                    <h2>THÔNG BÁO</h2>
                </div>
                <?php echo siteValue('notice'); ?>

                <div class="mt-2 d-flex justify-content-end">
                    <button type="button" class="btn btn-primary shadow-2" id="btn-close-notice">Tôi đã đọc</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/app/js/plugins/apexcharts.min.js"></script>
<?php if(Auth::check()): ?>
<script>
    const labels = <?php echo json_encode($labels, 15, 512) ?>;
    const rechargeData = <?php echo json_encode($data['recharge'], 15, 512) ?>;

    const ctx = document.getElementById('myChart').getContext('2d');
    const myChart = new Chart(ctx, {
        type: 'bar', 
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Tổng nạp',
                    data: rechargeData,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
<script>
  'use strict';
  setTimeout(function () {
    (function () {
      var options_order_statistics = {
        chart: {
          height: 350,
          type: 'area'
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth'
        },
        colors: [
        //'#F4C22B', 
        '#F4A6A6', 
        '#04A9F5', 
        '#4B0082', 
        '#FF8C00', 
        '#F44236', 
        '#28a745'],
        series: [
          //{
          //  name: 'Tổng đơn hàng',
          //  data: <?php echo json_encode($totalOrder, 15, 512) ?>
          //},
          {
            name: 'Đang chờ',
            data: <?php echo json_encode($totalPending, 15, 512) ?>
          },
          {
            name: 'Đang chạy',
            data: <?php echo json_encode($totalProcessing, 15, 512) ?>
          },
          {
            name: 'Đã huỷ',
            data: <?php echo json_encode($totalCanceled, 15, 512) ?>
          },
          {
            name: 'Chờ hoàn tiền',
            data: <?php echo json_encode($totalPendingRefundCancel, 15, 512) ?>
          },
          {
            name: 'Hoàn tiền',
            data: <?php echo json_encode($totalRefund, 15, 512) ?>
          },
          {
            name: 'Hoàn thành',
            data: <?php echo json_encode($totalCompleted, 15, 512) ?>
          }
        ],
        xaxis: {
          categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'], // Tháng trong năm
        },
        tooltip: {
          shared: true,
          intersect: false
        }
      };

      var chart_order_statistics = new ApexCharts(document.querySelector('#order-statistics'), options_order_statistics);
      chart_order_statistics.render();
    })();
  }, 700);
</script>
<?php else: ?>
<script>
     
    const ctx = document.getElementById('myChart').getContext('2d');
    const myChart = new Chart(ctx, {
        type: 'bar', 
        data: {
            labels: ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
            datasets: [
                {
                    label: 'Tổng nạp',
                    data: '',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>

<script>
  'use strict';
  setTimeout(function () {
    (function () {
      var options_order_statistics = {
        chart: {
          height: 350,
          type: 'area'
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth'
        },
        colors: [
          '#F4A6A6', 
          '#04A9F5', 
          '#4B0082', 
          '#FF8C00', 
          '#F44236', 
          '#28a745'
        ],
        series: [
          {
            name: 'Đang chờ',
            data: 0 
          },
          {
            name: 'Đang chạy',
            data: 0
          },
          {
            name: 'Đã huỷ',
            data: 0
          },
          {
            name: 'Chờ hoàn tiền',
            data: 0
          },
          {
            name: 'Hoàn tiền',
            data: 0
          },
          {
            name: 'Hoàn thành',
            data: 0
          }
        ],
        xaxis: {
          categories: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        },
        tooltip: {
          shared: true,
          intersect: false
        }
      };

      var chart_order_statistics = new ApexCharts(document.querySelector('#order-statistics'), options_order_statistics);
      chart_order_statistics.render();
    })();
  }, 700);
</script>

<?php endif; ?>
    <script>
        $(document).ready(function() {
            let isNoticeModal = localStorage.getItem('isNoticeModal');

            let time = 60 * 60 * 1000;

            if (new Date().getTime() - isNoticeModal > time) {
                $('#notiModal').modal('show');
            }

            $('#btn-close-notice').click(function() {
                localStorage.setItem('isNoticeModal', new Date().getTime());
                $('#notiModal').modal('hide');
            });

        });
    </script>
     
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        $(document).ready(function() {
              const swiper = new Swiper(".mySwiper", {
                slidesPerView: 2.5,
                spaceBetween: 20,
                loop: true,
                navigation: {
                  nextEl: ".swiper-button-next",
                  prevEl: ".swiper-button-prev",
                },
                autoplay: {
                  delay: 3000,
                  disableOnInteraction: true
                },
                breakpoints: {
                  1850: {
                    slidesPerView: 8,
                  },
                  1620: {
                    slidesPerView: 7,
                  },
                  1440: {
                    slidesPerView: 5,
                  },
                  1300: {
                    slidesPerView: 5.5,
                  },
                  1180: {
                    slidesPerView: 5.2,
                  },
                  992: {
                    slidesPerView: 4.6,
                  },
                  820: {
                    slidesPerView: 4.2,
                  },
                  768: {
                    slidesPerView: 3.7,
                  },
                  660: {
                    slidesPerView: 3.5,
                    spaceBetween: 10,
                  },
                  575: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                  },
                  0: {
                    slidesPerView: 2.2,
                    spaceBetween: 10,
                  }
                }
              });
             
        });
    </script>
</script>
<script src="<?php echo e(asset('assets/pack-lamtilo/js/service.js?time=')); ?><?php echo e(time()); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('guard.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/guard/home.blade.php ENDPATH**/ ?>