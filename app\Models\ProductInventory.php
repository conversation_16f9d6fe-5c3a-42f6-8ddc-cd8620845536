<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductInventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'data',
        'status',
        'domain'
    ];

    public function product(){
        return $this->belongsTo(Product::class);
    }

    public function scopeSearch($query, $search){
        return $query->where('data', 'like', '%'.$search.'%');
    }
}
