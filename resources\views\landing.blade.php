<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{siteValue('name_site')}}  - <PERSON><PERSON>hống Dịch Vụ Mạng Xã Hội Hàng <PERSON></title>

    <meta name="description" content="Hệ thống dịch vụ mạng xã hội hàng đầu Việt Nam">
    <meta name="keywords" content="social media, marketing, facebook, instagram, tiktok">
    <meta name="author" content="Social Media Platform">
    <meta name="robots" content="index, follow">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            background: #0a0a0a;
            color: #ffffff;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #1e3c72, #2a5298, #667eea, #764ba2);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: floatBubbles 20s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes floatBubbles {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(120deg); }
            66% { transform: translate(-20px, 20px) rotate(240deg); }
        }

        /* Header Styles */
        .main_header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .navbar-brand img {
            max-height: 50px;
            transition: transform 0.3s ease;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        .navbar-brand:hover img {
            transform: scale(1.1) rotate(5deg);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            margin: 0 10px;
            position: relative;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #ffffff !important;
            transform: translateY(-2px);
        }

        .nav-sigin, .nav-signup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            padding: 10px 25px;
            border-radius: 25px;
            margin: 0 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .nav-signup {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 4px 15px rgba(56, 239, 125, 0.4);
        }

        .nav-sigin:hover, .nav-signup:hover {
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        /* Mobile Navigation */
        #navMob {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            z-index: 9999;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-100%); }
            to { opacity: 1; transform: translateY(0); }
        }

        #navMob .nav_content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 30px;
            border-radius: 20px;
            margin: 50px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Hero Section */
        #hero {
            padding: 120px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero_content {
            position: relative;
            z-index: 2;
        }

        .hero_top_content {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 500;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero_content h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
            animation: slideInLeft 1s ease-out;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .hero_content p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            animation: slideInRight 1s ease-out 0.2s both;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .ratings {
            display: flex;
            align-items: center;
            margin: 30px 0;
            gap: 15px;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .rating-badge {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .rating-badge:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.2);
        }

        .rating-badge i {
            color: #ffd700;
        }

        .btn-banner-all {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 10px 30px rgba(56, 239, 125, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 10px 30px rgba(56, 239, 125, 0.4); }
            50% { box-shadow: 0 15px 40px rgba(56, 239, 125, 0.6); }
        }

        .btn-banner-all::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-banner-all:hover::before {
            left: 100%;
        }

        .btn-banner-all:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(56, 239, 125, 0.6);
        }

        /* 3D Hero Image */
        .hero_img {
            position: relative;
            perspective: 1000px;
        }

        .hero_img img {
            max-width: 100%;
            height: auto;
            transform: rotateY(-15deg) rotateX(5deg);
            transition: all 0.3s ease;
            filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
            animation: float3D 6s ease-in-out infinite;
        }

        @keyframes float3D {
            0%, 100% { 
                transform: rotateY(-15deg) rotateX(5deg) translateY(0px);
            }
            50% { 
                transform: rotateY(-10deg) rotateX(0deg) translateY(-20px);
            }
        }

        .hero_img:hover img {
            transform: rotateY(0deg) rotateX(0deg) scale(1.05);
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            animation: floatSlow 8s ease-in-out infinite;
        }

        .floating-element:nth-child(odd) {
            animation-delay: -4s;
        }

        @keyframes floatSlow {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.3);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Get Start Section */
        #get-start {
            padding: 120px 0;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .get_start_container {
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .default_home_title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: slideInUp 1s ease-out;
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .btn-primary.btn-white {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary.btn-white::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary.btn-white:hover::before {
            left: 100%;
        }

        .btn-primary.btn-white:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
        }

        /* Social Media Icons Animation */
        .social-icons {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
        }

        .social-icon {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            animation: orbit 15s linear infinite;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .social-icon:hover {
            transform: scale(1.2);
            background: rgba(255, 255, 255, 0.2);
        }

        .social-icon:nth-child(1) {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0s;
        }

        .social-icon:nth-child(2) {
            top: 25%;
            right: 0;
            animation-delay: -3s;
        }

        .social-icon:nth-child(3) {
            bottom: 25%;
            right: 0;
            animation-delay: -6s;
        }

        .social-icon:nth-child(4) {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: -9s;
        }

        .social-icon:nth-child(5) {
            bottom: 25%;
            left: 0;
            animation-delay: -12s;
        }

        .social-icon:nth-child(6) {
            top: 25%;
            left: 0;
            animation-delay: -15s;
        }

        @keyframes orbit {
            from { transform: rotate(0deg) translateX(150px) rotate(0deg); }
            to { transform: rotate(360deg) translateX(150px) rotate(-360deg); }
        }

        /* Particle Effect */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            /* Header adjustments */
            .navbar-brand img {
                max-height: 40px;
            }
            
            .main_header {
                padding: 10px 0;
            }
            
            /* Hero section mobile optimization */
            #hero {
                padding: 80px 0 60px;
                min-height: auto;
            }
            
            .hero_content {
                text-align: center;
                margin-bottom: 40px;
            }
            
            .hero_content h1 {
                font-size: 2rem;
                line-height: 1.3;
                margin-bottom: 20px;
            }
            
            .hero_content p {
                font-size: 1.1rem;
                margin-bottom: 30px;
                padding: 0 10px;
            }
            
            .hero_top_content {
                font-size: 0.9rem;
                padding: 8px 16px;
                margin-bottom: 20px;
            }
            
            /* Button improvements */
            .btn-banner-all {
                padding: 15px 30px;
                font-size: 1rem;
                width: 100%;
                max-width: 280px;
                margin: 0 auto;
                display: block;
                text-align: center;
            }
            
            /* Ratings mobile layout */
            .ratings {
                justify-content: center;
                gap: 8px;
                margin: 20px 0 30px;
            }
            
            .rating-badge {
                font-size: 0.85rem;
                padding: 6px 12px;
            }

            /* Hero image mobile */
            .hero_img {
                margin-top: 40px;
            }
            
            .hero_img img {
                transform: none;
                max-width: 90%;
                height: auto;
            }

            /* Get start section mobile */
            #get-start {
                padding: 80px 0;
            }
            
            .default_home_title {
                font-size: 1.8rem;
                line-height: 1.3;
                margin-bottom: 20px;
            }
            
            .get_start_container p {
                font-size: 1rem !important;
                margin-bottom: 30px !important;
                padding: 0 15px;
            }
            
            .btn-primary.btn-white {
                padding: 15px 30px;
                font-size: 1rem;
                width: 100%;
                max-width: 280px;
            }

            /* Hide complex animations on mobile */
            .social-icons {
                display: none;
            }
            
            .floating-icon {
                display: none;
            }
            
            .particles {
                display: none;
            }
            
            /* Mobile navigation improvements */
            .navbar-toggler {
                border: none;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            
            .navbar-toggler:focus {
                box-shadow: none;
            }
            
            #navMob .nav_content {
                margin: 20px;
                padding: 25px;
                border-radius: 15px;
            }
            
            #navMob .menu_mobs li {
                margin: 20px 0 !important;
            }
            
            #navMob .menu_mobs a {
                font-size: 1.1rem !important;
                padding: 10px 0;
                display: block;
            }
            
            #navMob .btn_wraper {
                margin-top: 30px !important;
            }
            
            #navMob .btn_wraper a {
                display: block !important;
                margin: 10px 0 !important;
                padding: 12px 20px !important;
                text-align: center;
                font-size: 1rem;
            }
        }
        
        /* Extra small devices */
        @media (max-width: 480px) {
            .hero_content h1 {
                font-size: 1.7rem;
            }
            
            .default_home_title {
                font-size: 1.5rem;
            }
            
            .hero_content p {
                font-size: 1rem;
            }
            
            .ratings {
                flex-wrap: wrap;
                gap: 6px;
            }
            
            .rating-badge {
                font-size: 0.8rem;
                padding: 5px 10px;
            }
            
            .btn-banner-all, .btn-primary.btn-white {
                padding: 12px 25px;
                font-size: 0.95rem;
            }
            
            #navMob .nav_content {
                margin: 15px;
                padding: 20px;
            }
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0a0a0a;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeOut 2s ease-out 1s forwards;
        }

        .loader {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
                visibility: hidden;
            }
        }
    </style>
</head>

<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay">
        <div class="loader"></div>
    </div>

    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Particles -->
    <div class="particles" id="particles"></div>

    <header class="main_header sticky-top">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <img src="{{ site('logo') }}" alt="Social Platform">
                </a>
                <button class="navbar-toggler" type="button" onclick="navToggleMob()">
                    <i class="fas fa-bars text-white"></i>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="/home">Trang Chủ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/apiv2">API</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-sigin" href="/auth/login">Đăng Nhập</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-signup" href="/auth/register">Đăng Ký</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <div id="navMob">
        <button id="cls" onclick="navToggleMob()" style="position: absolute; top: 20px; right: 20px; background: none; border: none; color: white; font-size: 24px;">
            <i class="fas fa-times"></i>
        </button>
        <div class="nav_content">
            <div class="menu">
                <ul class="menu_mobs" style="list-style: none; padding: 0;">
                    <li style="margin: 15px 0;"><a href="/" style="text-decoration: none; color: white; font-size: 18px;">Trang Chủ</a></li>
                    <li style="margin: 15px 0;"><a href="/api/v2" style="text-decoration: none; color: white; font-size: 18px;">API</a></li>
                </ul>
            </div>
            <div class="btn_wraper" style="margin-top: 20px;">
                <a href="/auth/login" style="display: inline-block; margin: 5px 10px 5px 0; padding: 10px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 25px;">Đăng Nhập</a>
                <a href="/auth/register" style="display: inline-block; margin: 5px 10px 5px 0; padding: 10px 20px; background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; text-decoration: none; border-radius: 25px;">Đăng Ký <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </div>

    <!-- Floating Icons -->
    <div class="floating-icon" style="top: 10%; left: 5%;"><i class="fab fa-facebook"></i></div>
    <div class="floating-icon" style="top: 20%; right: 10%;"><i class="fab fa-instagram"></i></div>
    <div class="floating-icon" style="bottom: 30%; left: 8%;"><i class="fab fa-tiktok"></i></div>
    <div class="floating-icon" style="bottom: 10%; right: 5%;"><i class="fab fa-twitter"></i></div>
         
    <section id="hero">
        <div class="container">
            <div class="row d-flex align-items-center">
                <div class="col-lg-6 col-md-12">
                    <div class="hero_content">
                         <h1 class="font-weight-bold mb-4">{{siteValue('name_site')}} - Social Media Marketing 2025</h1>
                        <p>
                            Chuyên Cung Cấp Các Dịch Vụ Tăng Like, Follow, Share, Comment, View Video Cho Các Mạng Xã Hội Như Facebook, Instagram, TikTok, Threads Với Chất Lượng Cao Và Giá Cả Hợp Lý.
                        </p>
                        <div class="ratings">
                            <div class="rating-badge">
                                <i class="fas fa-star"></i>
                                <span>Facebook</span>
                            </div>
                            <div class="rating-badge">
                                <i class="fas fa-star"></i>
                                <span>Instagram</span>
                            </div>
                            <div class="rating-badge">
                                <i class="fas fa-star"></i>
                                <span>TikTok</span>
                            </div>
                            <div class="rating-badge">
                                <i class="fas fa-star"></i>
                                <span>Twitter</span>
                            </div>
                        </div>
                        <a href="/auth/login" class="btn-banner-all">Sử Dụng Ngay <i class="fas fa-arrow-right ms-2"></i></a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-12">
                    <div class="hero_img text-center">
                        <img src="https://i.imgur.com/c9ge3pk.png" class="img-fluid" alt="Social Media Marketing">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="get-start">
        <div class="social-icons">
            <div class="social-icon"><i class="fab fa-facebook-f"></i></div>
            <div class="social-icon"><i class="fab fa-instagram"></i></div>
            <div class="social-icon"><i class="fab fa-tiktok"></i></div>
            <div class="social-icon"><i class="fab fa-youtube"></i></div>
            <div class="social-icon"><i class="fab fa-twitter"></i></div>
        </div>
        
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="get_start_container">
                        <h2 class="default_home_title">
                            Bạn Đã Sẵn Sàng Sử Dụng
                            <br>Dịch Vụ Của Chúng Tôi?
                        </h2>
                        <p class="lead" style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem; margin-bottom: 40px;">
                            Nếu Cần Tư Vấn Thêm Hãy Liên Hệ Với Chúng Tôi Ngay Hôm Nay!
                        </p>
                        <a href="/auth/register" class="btn-primary btn-white">Bắt Đầu Ngay <i class="fas fa-rocket ms-2"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>
        
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function navToggleMob() {
            const navMob = document.getElementById('navMob');
            if (navMob.style.display === 'block') {
                navMob.style.display = 'none';
            } else {
                navMob.style.display = 'block';
            }
        }
        
        // Close mobile nav when clicking outside
        document.getElementById('navMob').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.getElementById('hero');
            const heroImg = document.querySelector('.hero_img img');
            
            if (hero && heroImg) {
                const rate = scrolled * -0.5;
                heroImg.style.transform = `rotateY(-15deg) rotateX(5deg) translateY(${rate}px)`;
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.hero_content > *, .get_start_container > *').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease-out';
            observer.observe(el);
        });

        // Header background change on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.main_header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(0, 0, 0, 0.9)';
                header.style.backdropFilter = 'blur(20px)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.1)';
                header.style.backdropFilter = 'blur(20px)';
            }
        });

        // Mouse move effect for hero section
        document.addEventListener('mousemove', (e) => {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            const heroImg = document.querySelector('.hero_img img');
            if (heroImg) {
                const rotateX = (mouseY - 0.5) * 10;
                const rotateY = (mouseX - 0.5) * -10;
                heroImg.style.transform = `rotateY(${-15 + rotateY}deg) rotateX(${5 + rotateX}deg)`;
            }
        });

        // Add glitch effect to title on hover
        const title = document.querySelector('.hero_content h1');
        if (title) {
            title.addEventListener('mouseenter', () => {
                title.style.animation = 'glitch 0.3s ease-in-out';
            });
            
            title.addEventListener('animationend', () => {
                title.style.animation = 'slideInLeft 1s ease-out';
            });
        }

        // Initialize particles when page loads
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            
            // Trigger initial animations
            setTimeout(() => {
                document.querySelectorAll('.hero_content > *').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            }, 500);
        });

        // Add typing effect to hero title
        function typeWriter(element, text, speed = 50) {
            let i = 0;
            element.innerHTML = '';
            
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Social icons hover effect
        document.querySelectorAll('.social-icon').forEach(icon => {
            icon.addEventListener('mouseenter', () => {
                icon.style.animationPlayState = 'paused';
            });
            
            icon.addEventListener('mouseleave', () => {
                icon.style.animationPlayState = 'running';
            });
        });

        // Add CSS for glitch effect
        const style = document.createElement('style');
        style.textContent = `
            @keyframes glitch {
                0%, 100% { transform: translateX(0); }
                20% { transform: translateX(-2px); }
                40% { transform: translateX(2px); }
                60% { transform: translateX(-2px); }
                80% { transform: translateX(2px); }
            }
            
            .hero_content h1:hover {
                text-shadow: 
                    0.05em 0 0 rgba(255, 0, 0, 0.75),
                    -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                    0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
            }
        `;
        document.head.appendChild(style);