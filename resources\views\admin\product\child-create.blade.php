@extends('admin.layouts.app')
@section('title', "Thêm mới sản phẩm con")
@section('content')
    <div class="card custom-card shadow">
        <div class="card-header">
            <h4 class="card-title">Thêm mới sản phẩm con</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.products.child.store') }}" method="POST">
                @csrf
                <div class="form-group mb-3">
                    <label for="product_main_id" class="form-label">Sản phẩm chính</label>
                    <select name="product_main_id" id="product_main_id" class="form-control">
                        <option value="">Chọn sản phẩm chính</option>
                        @foreach (\App\Models\ProductMain::where('domain', request()->getHost())->where('status', 'active')->orderBy('order','asc')->get() as $productMain)
                            <option value="{{ $productMain->id }}" {{ old('product_main_id') == $productMain->id ? 'selected' : '' }}>{{ $productMain->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="name" class="form-label">Tên sản phẩm</label>
                    <input type="text" name="name" id="name" class="form-control" value="{{ old('name') }}">
                </div>
                <div class="form-group mb-3">
                    <label for="price" class="form-label">Giá sản phẩm</label>
                    <input type="text" name="price" id="price" class="form-control" value="{{ old('price') }}">
                </div>
                <div class="row">
                    <div class="col-md-6 form-group mb-3">
                        <label for="status" class="form-label">Trạng thái</label>
                        <select name="status" id="status" class="form-control">
                            <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Kích hoạt</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Chưa kích hoạt</option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="order" class="form-label">Thứ tự</label>
                        <input type="number" min="0" name="order" id="order" class="form-control" value="{{ old('order') }}">
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="type" class="form-label">Loại</label>
                        <select name="type" id="type" class="form-control">
                            <option value="manual" {{ old('type') == 'manual' ? 'selected' : '' }}>Thủ công</option>
                            <option value="taphoammo" {{ old('type') == 'taphoammo' ? 'selected' : '' }}>API: Taphoammo</option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-">
                        <label for="providerToken" class="form-label">Token sản phẩm (taphoammo)</label>
                        <input type="text" name="providerToken" id="providerToken" class="form-control" value="{{ old('providerToken') }}">
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Thêm mới</button>
            </form>
        </div>
    </div>
@endsection
